package cn.taken.ad.components;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.utils.result.Result;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.configuration.counter.RtbCounter;
import cn.taken.ad.configuration.dmp.DmpMonitor;
import cn.taken.ad.configuration.monitor.DspAdvAdRtbMonitor;
import cn.taken.ad.configuration.monitor.RtbMonitor;
import cn.taken.ad.constant.business.BidPriceType;
import cn.taken.ad.constant.business.BidType;
import cn.taken.ad.constant.business.TagType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.pojo.advertiser.Advertiser;
import cn.taken.ad.core.pojo.advertiser.AdvertiserApp;
import cn.taken.ad.core.pojo.advertiser.AdvertiserProtocol;
import cn.taken.ad.core.pojo.advertiser.AdvertiserTag;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaAdvertiserRequest;
import cn.taken.ad.core.pojo.strategy.StrategyTagAdvertiser;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbDto;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.application.ApplicationContextUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public abstract class AbstractStrategyComponent implements StrategyComponent {

    private static final Logger log = LoggerFactory.getLogger(AbstractStrategyComponent.class);
    @Resource
    protected BaseRedisL2Cache baseRedisL2Cache;
    @Resource(name = "BaseRedis")
    protected RedisClient redis;
    @Resource(name = "FastHttpClient")
    private FastHttpClient fastHttpClient;
    @Resource
    private RtbCounter rtbCounter;
    @Resource
    private RtbMonitor rtbMonitor;
    @Resource
    private DspAdvAdRtbMonitor dspAdvAdRtbMonitor;
    @Resource
    private DmpMonitor dmpMonitor;

    private StatisticsMediaAdvertiserRequest getMonitor(StrategyTagAdvertiser adv, MediaTag mediaTag, RtbDto rtbDto) {
        StatisticsMediaAdvertiserRequest monitor = rtbDto.getMonitors().computeIfAbsent(adv.getId(), k -> new StatisticsMediaAdvertiserRequest());
        monitor.setAdvertiserTagId(adv.getAdvertiserTagId());
        monitor.setAdvertiserId(adv.getAdvertiserId());
        monitor.setAdvertiserAppId(adv.getAdvertiserAppId());
        monitor.setStrategyId(adv.getStrategyId());
        monitor.setStrategyTagAdvId(adv.getId());
        monitor.setMediaId(mediaTag.getMediaId());
        monitor.setMediaAppId(mediaTag.getMediaAppId());
        monitor.setMediaTagId(mediaTag.getId());
        return monitor;
    }

    /**
     * 请求预算
     * 过滤价格、http资源等 过滤完成后才计算填充
     * 返回 预算结果
     */
    protected RtbAdvDto requestAdv(Advertiser advertiser, AdvertiserProtocol protocol, MediaTag mediaTag, AdvertiserTag advTag, StrategyTagAdvertiser tagAdv, RtbRequestDto requestDto, RtbDto rtbDto) throws Throwable {
        long st = System.currentTimeMillis();
        // 构建新的请求对象
        RtbRequestDto newRequestDto = new RtbRequestDto(rtbDto.getRtbId(), requestDto);
        // 媒体原始底价,竞价跑分成时 涨幅模式需要使用此价格计算出价
        Double mediaPrice = requestDto.getTag().getPrice();
        //处理底价
        Double price = processPrice(newRequestDto, mediaTag, advTag, tagAdv);
        // 构建请求对象
        RtbAdvDto advDto = fillAdvDto(tagAdv.getId(), advertiser, protocol, advTag, price);
        advDto.setRequestDto(newRequestDto);
        rtbDto.getAdvs().add(advDto);
        // 按照策略 参数处理策略 替换包名等
        paramReplace(newRequestDto, tagAdv, advTag);
        try {
            // 预算处理器
            AdvProcessor processor = ApplicationContextUtils.getBean(protocol.getCode() + LogicSuffix.ADV_LOGIC_SUFFIX, AdvProcessor.class);
            // 请求预算
            RtbResponseDto advRespDto = processor.reqAdv(fastHttpClient, newRequestDto, advDto);
            // 处理请求结果
            if (null != advRespDto) {
                if (advRespDto.getCode().equals(LogicState.SUCCESS.getCode())) {
                    checkResp(mediaTag, price, advTag, advRespDto, tagAdv, mediaPrice);
                }
            } else {
                advRespDto = new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc());
            }
            advDto.setRtbResponseDto(advRespDto);
            return advDto;
        } catch (Exception e) {
            log.error("Req Adv Error,MediaTagCode:{},AdvTagCode:{}", mediaTag.getCode(), advTag.getCode(), e);
            if (advDto.getRtbResponseDto() == null) {
                advDto.setRtbResponseDto(new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc()));
            }
            return advDto;
        } finally {
            long us = System.currentTimeMillis() - st;
            RtbResponseDto advRespDto = advDto.getRtbResponseDto();
            boolean success = advRespDto.getCode().equals(LogicState.SUCCESS.getCode());
            boolean fail = !advRespDto.getCode().equals(LogicState.SUCCESS.getCode());
            boolean isTimeout = fail && advRespDto.getCode().equals(LogicState.TIMEOUT_ADV.getCode());
            boolean pad = success && !CollectionUtils.isEmpty(advRespDto.getTags());
            rtbMonitor.monitorAdvRequest(tagAdv, mediaTag, 1, success ? 1 : 0, fail ? 1 : 0, (fail && !isTimeout) ? 1 : 0, isTimeout ? 1 : 0, pad ? 1 : 0, (int) us);
            if (StringUtils.isNotBlank(advDto.getCreativeId())) {
                dspAdvAdRtbMonitor.monitorAdvRequest(tagAdv, advDto.getCreativeId(), 1, success ? 1 : 0, fail ? 1 : 0, (fail && !isTimeout) ? 1 : 0, isTimeout ? 1 : 0, pad ? 1 : 0, (int) us);
            }
            if (fail) {
                rtbMonitor.monitorAdvertiserErrorCode(tagAdv, StringUtils.isNotBlank(advRespDto.getAdvErrCode()) ? advRespDto.getAdvErrCode() : advRespDto.getCode());
            }
            if (pad) {
                dmpMonitor.collectPackDevice(newRequestDto.getDevice(), mediaTag.getMediaId(), mediaTag.getMediaAppId(), mediaTag.getId(),
                        advTag.getAdvertiserId(), advTag.getAdvertiserAppId(), advTag.getId(), 0);
            }
            rtbCounter.tagNumberAdd(advTag);
            rtbCounter.strategyNumberAdd(tagAdv, advTag);
        }
    }

    /**
     * 过滤 规则
     * 价格过滤、https资源过滤、域名过滤
     */
    private void checkResp(MediaTag mediaTag, Double price, AdvertiserTag advTag, RtbResponseDto dto, StrategyTagAdvertiser tagAdv, Double mediaPrice) {
        List<TagResponseDto> tags = dto.getTags();
        if (null == tags || tags.isEmpty()) {
            return;
        }
        boolean needHttpsMaterial = Boolean.TRUE.equals(mediaTag.getMaterialHttps());
        boolean needHttpsTrackUrl = null != mediaTag.getNeedHttps() && mediaTag.getNeedHttps() == 1;
        boolean checkDomain;
        // 校验素材 https
        if (needHttpsMaterial && checkMaterialNotHttps(dto)) {
            dto.setCode(LogicState.ERROR_HTTPS_MATERIAL.getCode());
            dto.setMsg(LogicState.ERROR_HTTPS_MATERIAL.getDesc());
            return;
        }

        Media media = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_ID_+mediaTag.getMediaId(), Media.class);
        // 过滤 预算配置的 域名
        Set<String> blackDomains = new HashSet<>();
        if (StringUtils.isNotBlank(advTag.getFilterUrlDomain())) {
            String [] arr = advTag.getFilterUrlDomain().split(",");
            if (arr.length > 0) {
                blackDomains.addAll(Arrays.asList(arr));
            }
        }
        // 媒体域名
        if(Boolean.TRUE.equals(media.getFilterOnOff())){
            if (StringUtils.isNotBlank(media.getFilterUrlDomain())) {
                String [] arr = media.getFilterUrlDomain().split(",");
                if (arr.length > 0) {
                    blackDomains.addAll(Arrays.asList(arr));
                }
            }
        }
        // 媒体广告位 过滤
        if(Boolean.TRUE.equals(mediaTag.getFilterOnOff())){
            if (StringUtils.isNotBlank(mediaTag.getFilterUrlDomain())) {
                String [] arr = mediaTag.getFilterUrlDomain().split(",");
                if (arr.length > 0) {
                    blackDomains.addAll(Arrays.asList(arr));
                }
            }
            //过滤素材比例
            if (mediaTag.getFilterMaterialRatio() == 1) {
                List<TagResponseDto> needRemove = new ArrayList<>();
                for (TagResponseDto tag : tags) {
                    if (tag.getMaterialHeight() != null && tag.getMaterialHeight() > 0 && tag.getMaterialWidth() != null && tag.getMaterialWidth() > 0) {


                        needRemove.add(tag);
                        continue;
                    }
                }
                if (!needRemove.isEmpty()) {
                    tags.removeAll(needRemove);
                }
            }
        }
        checkDomain = !blackDomains.isEmpty();
        // 过滤 域名 或 非https 的上报
        if ((needHttpsTrackUrl || checkDomain)) {
            Result checkResult = checkTrackNotHttpsOrBlackDomain(needHttpsTrackUrl, checkDomain, blackDomains, dto);
            if (!checkResult.getSuccess()) {
                dto.setCode(checkResult.getCode());
                dto.setMsg(checkResult.getMessage());
                return;
            }
        }

        // 竞价模式 价格校验
        if (BidType.BID.getType() == advTag.getSettlementType()) {
            List<TagResponseDto> needRemove = new ArrayList<>();
            for (TagResponseDto tag : tags) {
                if (mediaTag.getBidType() == BidType.BID.getType()) {
                    if (null == tag.getPrice()) {
                        needRemove.add(tag);
                        continue;
                    }
                    if (tag.getPrice() < price) {
                        needRemove.add(tag);
                        continue;
                    }
                    // 价格调整
                    if (advTag.getBidPriceType() == 1) { // 涨幅模式
                        double respMediaPrice = new BigDecimal((tag.getPrice() / ((100 + advTag.getBidRisesRatio()) / 100.0))).setScale(4, RoundingMode.HALF_UP).doubleValue();
                        if (respMediaPrice < price) {
                            needRemove.add(tag);
                            continue;
                        }
                        tag.setRespMediaPrice(respMediaPrice);
                    } else if (advTag.getBidPriceType() == 2) { // 固价模式
                        tag.setRespMediaPrice(advTag.getFixedPrice());
                    }
                } else {
                    tag.setRespMediaPrice(tag.getPrice());
                }
            }
            // 处理低价过滤
            if (!needRemove.isEmpty()) {
                tags.removeAll(needRemove);
            }
            if (tags.isEmpty()) {
                dto.setCode(LogicState.ERROR_PRICE.getCode());
                dto.setMsg(LogicState.ERROR_PRICE.getDesc());
            }
        } else { // 分成
            for (TagResponseDto tag : tags) {
                // 媒体竞价模式
                if (mediaTag.getBidType() == BidType.BID.getType()) {
                    // 使用策略中的出价规则
                    if (tagAdv.getRtbToSharingBidPriceType() == BidPriceType.UP.getType()) { // 涨幅
                        // 媒体底价涨幅后返回给媒体
                        int rate = tagAdv.getRtbToSharingBidRisesRatio();
                        double respMediaPrice = BigDecimal.valueOf(mediaPrice * (1 + (rate / 100.0d))).setScale(4, RoundingMode.HALF_UP).doubleValue();
                        tag.setRespMediaPrice(respMediaPrice);
                    } else { // 固价
                        // 元 转为分
                        tag.setRespMediaPrice(tagAdv.getRtbToSharingFixedPrice());
                    }
                } else {
                    if (null != tag.getPrice()) {
                        if (advTag.getBidPriceType() == 2) {
                            tag.setRespMediaPrice(advTag.getFixedPrice());
                        } else {
                            // 透传
                            tag.setRespMediaPrice(tag.getPrice());
                        }
                    }
                }
            }
        }
    }

    /**
     * 校验素材链接
     */
    private boolean checkMaterialNotHttps(RtbResponseDto tagResponseDto) {
        for (TagResponseDto tag : tagResponseDto.getTags()) {
            if (checkIsNeedRemoveForHttps(tag.getClickUrl())) {
                return true;
            }
            if (checkIsNeedRemoveForHttps(tag.getLogoUrl())) {
                return true;
            }
            if (checkIsNeedRemoveForHttps(tag.getIconUrl())) {
                return true;
            }
            if (checkIsNeedRemoveForHttps(tag.getImgUrls())) {
                return true;
            }
            if (null != tag.getVideoInfo() && checkIsNeedRemoveForHttps(tag.getVideoInfo().getVideoUrl())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验 上报链接
     */
    private Result checkTrackNotHttpsOrBlackDomain(boolean needHttpsTrackUrl, boolean checkDomain, Set<String> blackDomains, RtbResponseDto tagResponseDto) {
        for (TagResponseDto tag : tagResponseDto.getTags()) {
            if (!CollectionUtils.isEmpty(tag.getWinNoticeUrls())) {
                for (String winNoticeUrl : tag.getWinNoticeUrls()) {
                    if (needHttpsTrackUrl && checkIsNeedRemoveForHttps(winNoticeUrl)) {
                        return Result.bad(LogicState.ERROR_HTTPS_TRACK.getCode(), LogicState.ERROR_HTTPS_TRACK.getDesc(), null);
                    }
                    if (checkDomain && checkBlackDomain(blackDomains, winNoticeUrl)) {
                        return Result.bad(LogicState.ERROR_FILTER_DOMAIN.getCode(), LogicState.ERROR_FILTER_DOMAIN.getDesc(), null);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(tag.getFailNoticeUrls())) {
                for (String failNoticeUrl : tag.getFailNoticeUrls()) {
                    if (needHttpsTrackUrl && checkIsNeedRemoveForHttps(failNoticeUrl)) {
                        return Result.bad(LogicState.ERROR_HTTPS_TRACK.getCode(), LogicState.ERROR_HTTPS_TRACK.getDesc(), null);
                    }
                    if (checkDomain && checkBlackDomain(blackDomains, failNoticeUrl)) {
                        return Result.bad(LogicState.ERROR_FILTER_DOMAIN.getCode(), LogicState.ERROR_FILTER_DOMAIN.getDesc(), null);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(tag.getTracks())) {
                for (ResponseTrackDto track : tag.getTracks()) {
                    for (String trackUrl : track.getTrackUrls()) {
                        if (needHttpsTrackUrl && checkIsNeedRemoveForHttps(trackUrl)) {
                            return Result.bad(LogicState.ERROR_HTTPS_TRACK.getCode(), LogicState.ERROR_HTTPS_TRACK.getDesc(), null);
                        }
                        if (checkDomain && checkBlackDomain(blackDomains, trackUrl)) {
                            return Result.bad(LogicState.ERROR_FILTER_DOMAIN.getCode(), LogicState.ERROR_FILTER_DOMAIN.getDesc(), null);
                        }
                    }
                }
            }
        }
        return Result.right();
    }

    private boolean checkBlackDomain(Set<String> blackDomains, String link) {
        if (null == blackDomains || blackDomains.isEmpty()) {
            return false;
        }
        try {
            URL url = new URL(link);
            String host = url.getHost(); // 获取链接的主机名
            // 遍历目标域名列表，检查是否匹配
            for (String targetDomain : blackDomains) {
                if (host.equalsIgnoreCase(targetDomain) || host.endsWith("." + targetDomain)) {
                    return true;
                }
            }
        } catch (Exception ignored) {
        }
        return false;
    }

    /**
     * 校验素材链接地址是否https
     * 返回是否需要过滤
     */
    private boolean checkIsNeedRemoveForHttps(List<String> urls) {
        if (null != urls && !urls.isEmpty()) {
            for (String url : urls) {
                if (StringUtils.isNotBlank(url) && !url.startsWith("https")) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验素材链接地址是否https
     * 返回是否需要过滤
     */
    private boolean checkIsNeedRemoveForHttps(String url) {
        return StringUtils.isNotBlank(url) && !url.startsWith("https");
    }

    /**
     * 获取 请求预算的底价
     */
    private Double processPrice(RtbRequestDto requestDto, MediaTag mediaTag, AdvertiserTag tag, StrategyTagAdvertiser tagAdv) {
        Double price;
        if (mediaTag.getBidType() == 1) { //竞价
            if (tag.getSettlementType() == BidType.BID.getType()) {
                if (tag.getBidPriceType() == 1) {//涨幅
                    int rate = tag.getBidRisesRatio();
                    price = BigDecimal.valueOf(requestDto.getTag().getPrice() * (1 + (rate / 100.0d))).setScale(4, RoundingMode.HALF_UP).doubleValue();
                } else {//使用固定底价
                    price = tag.getFixedPrice();
                }
            } else {
                // 竞价跑分成
                if (tagAdv.getRtbToSharingBasePriceType() == 1) {
                    // 不传底价
                    price = null;
                } else {
                    // 透传
                    price = requestDto.getTag().getPrice();
                }
            }
            requestDto.getTag().setPrice(price);
        } else {//分成
            if (tag.getSettlementType() == 1) { //分成跑竞价->使用分量策略上面定义的底价
                price = tagAdv.getAdvTagFixedPrice();
                requestDto.getTag().setPrice(price);
            } else {
                if (tag.getSharingPriceType() == 2) {//使用固定底价
                    price = tag.getFixedPrice();
                    requestDto.getTag().setPrice(price);
                } else {//透传
                    price = requestDto.getTag().getPrice();
                }
            }
        }
        return price;
    }

    /**
     * 构建请求预算实例
     */
    private RtbAdvDto fillAdvDto(Long tagAdvId, Advertiser advertiser, AdvertiserProtocol protocol, AdvertiserTag tag, Double price) {
        RtbAdvDto dto = new RtbAdvDto();
        AdvertiserApp app = baseRedisL2Cache.get(BaseRedisKeys.KV_ADVERTISER_APP_ID_ + tag.getAdvertiserAppId(), AdvertiserApp.class);
        dto.setStrategyTagAdvId(tagAdvId);
        dto.setAdvertiserId(tag.getAdvertiserId());
        dto.setAdvertiserName(advertiser.getName());
        dto.setAppId(app.getId());
        dto.setAppCode(app.getCode());
        dto.setAppName(app.getName());
        dto.setAppPnyParam(app.getPnyParam());
        dto.setFirstIndustryId(app.getFirstIndustryId());
        dto.setSecondIndustryId(app.getSecondIndustryId());
        dto.setPackageName(app.getPackageName());
        dto.setTagCode(tag.getCode());
        dto.setTagType(TagType.findByType(tag.getType()));
        dto.setTimeout(tag.getTimeout());
        dto.setTagId(tag.getId());
        dto.setPnyParam(advertiser.getPnyParam());
        dto.setTagPnyParam(tag.getPnyParam());
        dto.setRtburl(advertiser.getRtbUrl());
        dto.setProtocolCode(protocol.getCode());
        dto.setSettlementType(tag.getSettlementType());
        if (price != null) {
            dto.setPrice(new BigDecimal(price));
        }
        return dto;
    }

    /**
     * 替换APP请求参数
     */
    private void paramReplace(RtbRequestDto request, StrategyTagAdvertiser adv, AdvertiserTag tag) {
        if (adv.getHandleType() != null && adv.getHandleType() == 2) {
            AdvertiserApp app = baseRedisL2Cache.get(BaseRedisKeys.KV_ADVERTISER_APP_ID_ + tag.getAdvertiserAppId(), AdvertiserApp.class);
            if (StringUtils.isNotEmpty(app.getPackageName()) && !app.getPackageName().equals(request.getApp().getBundle())) {
                // 使用平台的包名
                request.getApp().setBundle(app.getPackageName());
            }
        } else if (adv.getHandleType() != null && adv.getHandleType() == 3) {
            //不传
            request.getApp().setBundle("");
        }
    }
}
