package cn.taken.ad.task.financial;

import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.constant.business.FinancialReleaseState;
import cn.taken.ad.constant.business.FinancialState;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dto.web.oper.media.tag.MediaTagInfo;
import cn.taken.ad.core.pojo.financial.FinancialAdvertiser;
import cn.taken.ad.core.pojo.financial.FinancialMedia;
import cn.taken.ad.core.pojo.statistics.StatisticsAdvertiserEvent;
import cn.taken.ad.core.pojo.statistics.StatisticsAdvertiserRequest;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaEvent;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaRequest;
import cn.taken.ad.core.service.financial.FinancialAdvertiserService;
import cn.taken.ad.core.service.financial.FinancialMediaService;
import cn.taken.ad.core.service.media.MediaTagService;
import cn.taken.ad.core.service.statistics.StatisticsAdvertiserEventService;
import cn.taken.ad.core.service.statistics.StatisticsAdvertiserRequestService;
import cn.taken.ad.core.service.statistics.StatisticsMediaEventService;
import cn.taken.ad.core.service.statistics.StatisticsMediaRequestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 媒体账务任务
 * 媒体凌晨两点 拉取昨天的媒体统计信息
 */
@Component
public class FinancialTask {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Resource
    private StatisticsMediaRequestService statisticsMediaRequestService;
    @Resource
    private StatisticsMediaEventService statisticsMediaEventService;
    @Resource
    private StatisticsAdvertiserRequestService statisticsAdvertiserRequestService;
    @Resource
    private StatisticsAdvertiserEventService statisticsAdvertiserEventService;

    @Resource
    private FinancialMediaService financialmediaService;
    @Resource
    private FinancialAdvertiserService financialAdvertiserService;

    @Resource
    private MediaTagService mediaTagService;

    @SuperScheduled(cron = "0 0 2 * * ?", only = true)
//    @SuperScheduled(fixedDelay = 1000L * 60 * 5)
    public void report() {
        Date date = DateUtils.getDateBefore(new Date(), 1);//new Date();//
        media(date);
        advertiser(date);
    }

    private void media(Date date){
        String timeStr = DateUtils.toString(date, "yyyyMMdd");
        List<StatisticsMediaRequest> mediaRequests = statisticsMediaRequestService.findStatisticsMediaRequest(timeStr, timeStr, StatisticsType.DAY);
        List<StatisticsMediaEvent> mediaEvents = statisticsMediaEventService.findStatisticsMediaEvent(timeStr, timeStr, StatisticsType.DAY);
        Map<String, List<StatisticsMediaEvent>> eventMediaMap = mediaEvents.stream().collect(Collectors.groupingBy(k -> k.getMediaId() + "&&" + k.getMediaTagId() + "&&" + k.getMediaTagId()));
        Set<Long> tagIds = mediaRequests.stream().map(StatisticsMediaRequest::getMediaTagId).collect(Collectors.toSet());
        Map<Long,MediaTagInfo> mapMediaTagInfo = new HashMap<>();
        if (!tagIds.isEmpty()){
            List<MediaTagInfo> tagInfos = mediaTagService.findListByIds(tagIds);
            mapMediaTagInfo = tagInfos.stream().collect(Collectors.toMap(MediaTagInfo::getId, v -> v));
        }

        List<FinancialMedia> financialMedias = new ArrayList<>();
        for (StatisticsMediaRequest item : mediaRequests) {
            FinancialMedia report = new FinancialMedia();
            report.setReportTime(timeStr);
            report.setMediaId(item.getMediaId());
            report.setMediaAppId(item.getMediaAppId());
            report.setMediaTagId(item.getMediaTagId());
            report.setAmount(null != item.getAmount() ? BigDecimalUtils.div(item.getAmount(), new BigDecimal(100), 4) : BigDecimal.ZERO);
            report.setState(FinancialState.NOT_CALCULATED.getType());
            report.setReleaseState(FinancialReleaseState.NO.getType());
            Long clickTotal = 0L;
            Long exposureTotal = 0L;
            String key = item.getMediaId() + "&&" + item.getMediaTagId() + "&&" + item.getMediaTagId();
            List<StatisticsMediaEvent> events = eventMediaMap.get(key);
            if (null != events && !events.isEmpty()) {
                for (StatisticsMediaEvent event : events) {
                    if (event.getEventType().intValue() == EventType.EXPOSURE.getType()) {
                        exposureTotal = event.getTotal();
                    }else if (event.getEventType().intValue() == EventType.CLICK.getType()) {
                        clickTotal = event.getTotal();
                    }
                }
            }
            report.setParticipatingTotal(null == item.getParticipatingTotal() ? 0L : item.getParticipatingTotal());
            report.setExposureTotal(exposureTotal);
            report.setClickTotal(clickTotal);
            MediaTagInfo tagInfo = mapMediaTagInfo.get(item.getMediaTagId());
            if (null != tagInfo){
                if (null != tagInfo.getSettlementRatio()){
                    report.setSettlementRatio(tagInfo.getSettlementRatio());
                }else if (null != tagInfo.getMediaSettlementRatio()){
                    report.setSettlementRatio(tagInfo.getMediaSettlementRatio());
                }
            }
            financialMedias.add(report);
        }
        if (!financialMedias.isEmpty()) {
            financialmediaService.saveBatch(financialMedias);
        }
        log.info("media size:{}", financialMedias.size());
    }

    private void advertiser(Date date){
        String timeStr = DateUtils.toString(date, "yyyyMMdd");
        List<StatisticsAdvertiserRequest> advertiserRequests = statisticsAdvertiserRequestService.findStatisticsAdvertiserRequest(timeStr, timeStr, StatisticsType.DAY);
        List<StatisticsAdvertiserEvent> advertiserEvents = statisticsAdvertiserEventService.findStatisticsAdvertiserEvent(timeStr, timeStr, StatisticsType.DAY);
        Map<String, List<StatisticsAdvertiserEvent>> eventAdvertiserMap = advertiserEvents.stream().collect(Collectors.groupingBy(k -> k.getAdvertiserId() + "&&" + k.getAdvertiserAppId() + "&&" + k.getAdvertiserTagId()));
        List<FinancialAdvertiser> advertiserFinancials = new ArrayList<>();
        advertiserRequests.forEach(item -> {
            FinancialAdvertiser report = new FinancialAdvertiser();
            report.setReportTime(timeStr);
            report.setAdvertiserId(item.getAdvertiserId());
            report.setAdvertiserAppId(item.getAdvertiserAppId());
            report.setAdvertiserTagId(item.getAdvertiserTagId());
            report.setAmount(null != item.getAmount() ? BigDecimalUtils.div(item.getAmount(), new BigDecimal(100), 4) : BigDecimal.ZERO);
            Long clickTotal = 0L;
            Long exposureTotal = 0L;
            String key = item.getAdvertiserId() + "&&" + item.getAdvertiserAppId() + "&&" + item.getAdvertiserTagId();
            List<StatisticsAdvertiserEvent> events = eventAdvertiserMap.get(key);
            if (null != events && !events.isEmpty()) {
                for (StatisticsAdvertiserEvent event : events) {
                    if (event.getEventType().intValue() == EventType.EXPOSURE.getType()) {
                        exposureTotal = event.getTotal();
                    }else if (event.getEventType().intValue() == EventType.CLICK.getType()) {
                        clickTotal = event.getTotal();
                    }
                }
            }
            report.setParticipatingTotal(null == item.getParticipatingTotal() ? 0L : item.getParticipatingTotal());
            report.setExposureTotal(exposureTotal);
            report.setClickTotal(clickTotal);
            report.setState(FinancialState.NOT_CALCULATED.getType());
            advertiserFinancials.add(report);
        });
        if (!advertiserFinancials.isEmpty()) {
            financialAdvertiserService.saveBatch(advertiserFinancials);
        }
        log.info("advertiser size:{}", advertiserFinancials.size());
    }

}
