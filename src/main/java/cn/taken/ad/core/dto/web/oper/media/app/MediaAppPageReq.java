package cn.taken.ad.core.dto.web.oper.media.app;

import cn.taken.ad.core.dto.global.PageReq;

public class MediaAppPageReq extends PageReq {
    
    private String appName;
    private Long mediaId;
    private Integer type;
    private Integer state;

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }
}
