package cn.taken.ad.core.dto.web.oper.financial.media;

import cn.taken.ad.component.excel.common.schema.annotation.ExcelColumn;
import cn.taken.ad.component.excel.common.schema.annotation.ExcelSheet;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.constant.business.FinancialReleaseState;
import cn.taken.ad.constant.business.FinancialState;
import cn.taken.ad.core.pojo.financial.FinancialMedia;
import cn.taken.ad.utils.web.FinancialUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

@ExcelSheet(isAutoWidth = true)
public class FinancialMediaInfo extends FinancialMedia {

    private static final long serialVersionUID = -8950042550804910631L;

    @ExcelColumn(index = 1, title = "媒体")
    private String mediaName;
    @ExcelColumn(index = 2, title = "媒体CODE")
    private String mediaCode;
    @ExcelColumn(index = 3, title = "媒体APP名称")
    private String mediaAppName;
    @ExcelColumn(index = 4, title = "媒体APPCODE")
    private String mediaAppCode;
    private Integer mediaAppType;
    @ExcelColumn(index = 5, title = "媒体APP类型")
    private String mediaAppTypeStr;
    @ExcelColumn(index = 6, title = "媒体广告位名称")
    private String mediaTagName;
    @ExcelColumn(index = 7, title = "媒体广告位CODE")
    private String mediaTagCode;
    private Integer mediaTagType;
    @ExcelColumn(index = 8, title = "媒体广告位类型")
    private String mediaTagTypeStr;
    private Integer mediaTagBidType;
    @ExcelColumn(index = 9, title = "媒体结算类型")
    private String mediaTagBidTypeStr;

    @ExcelColumn(index = 19, title = "ECPM")
    private BigDecimal ecPm;
    @ExcelColumn(index = 20, title = "CPC")
    private BigDecimal cpc;

    private Double mediaSettlementRatio;
    private Double mediaTagSettlementRatio;

    @ExcelColumn(index = 28, title = "请求")
    private Long requestTotal;

    @ExcelColumn(index = 21, title = "状态")
    private String stateStr;

    @ExcelColumn(index = 22, title = "发布状态")
    private String releaseStateStr;

    public void fillValues() {
        if (this.getRealAmount() != null && this.getRealExposureTotal() != null && this.getRealExposureTotal() > 0) {
            this.setEcPm(BigDecimalUtils.div(this.getRealAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(this.getRealExposureTotal()), 4));
        } else {
            this.setEcPm(BigDecimal.ZERO);
        }
        if (this.mediaTagBidType != null) {
            this.mediaTagBidTypeStr = this.mediaTagBidType == 1 ? "RTB" : "分成";
        } else {
            this.mediaTagBidTypeStr = "未知";
        }
        //cpc
        if (this.getRealAmount() != null && this.getRealClickTotal() != null && this.getRealClickTotal() > 0) {
            this.setCpc(BigDecimalUtils.div(this.getRealAmount(), BigDecimal.valueOf(this.getRealClickTotal()), 2));
        } else {
            this.setCpc(BigDecimal.ZERO);
        }
        if (this.getRealAmount() != null) { //保留2位小数点
            this.setRealAmount(this.getRealAmount().setScale(2, RoundingMode.HALF_UP));
        }
        this.participatingGap = FinancialUtils.computeGap(this.getRealParticipatingTotal(), this.getParticipatingTotal());
        this.exposureGap = FinancialUtils.computeGap(this.getRealExposureTotal(), this.getExposureTotal());
        this.clickGap = FinancialUtils.computeGap(this.getRealClickTotal(), this.getClickTotal());
        this.amountGap = FinancialUtils.computeGap(this.getRealAmount(), this.getAmount());
        FinancialState financialState = null;
        if (this.getState() != null) {
            financialState = FinancialState.findByType(this.getState());
        }
        this.stateStr = financialState == null ? "未知" : financialState.getName();
        FinancialReleaseState releaseState = null;
        if (this.getReleaseState() != null) {
            releaseState = FinancialReleaseState.findByType(this.getReleaseState());
        }
        this.releaseStateStr = releaseState == null ? "未知" : releaseState.getName();
        this.participatingRate = this.getRequestTotal() == null || this.getRequestTotal() == 0 || this.getParticipatingTotal() == null ? 0d : new BigDecimal(this.getParticipatingTotal().doubleValue() / this.getRequestTotal().doubleValue() * 100).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 填充gap
     */
    @ExcelColumn(index = 23, title = "填充GAP(%)")
    private Double participatingGap;
    /**
     * 曝光gap
     */
    @ExcelColumn(index = 24, title = "曝光GAP(%)")
    private Double exposureGap;
    /**
     * 点击gap
     */
    @ExcelColumn(index = 25, title = "点击GAP(%)")
    private Double clickGap;
    /**
     * 金额gap
     */
    @ExcelColumn(index = 26, title = "金额GAP(%)")
    private Double amountGap;

    @ExcelColumn(index = 27, title = "填充率(%)")
    private Double participatingRate;

    public String getMediaName() {
        return mediaName;
    }

    public void setMediaName(String mediaName) {
        this.mediaName = mediaName;
    }

    public String getMediaCode() {
        return mediaCode;
    }

    public void setMediaCode(String mediaCode) {
        this.mediaCode = mediaCode;
    }

    public String getMediaAppName() {
        return mediaAppName;
    }

    public void setMediaAppName(String mediaAppName) {
        this.mediaAppName = mediaAppName;
    }

    public String getMediaAppCode() {
        return mediaAppCode;
    }

    public void setMediaAppCode(String mediaAppCode) {
        this.mediaAppCode = mediaAppCode;
    }

    public String getMediaTagName() {
        return mediaTagName;
    }

    public void setMediaTagName(String mediaTagName) {
        this.mediaTagName = mediaTagName;
    }

    public String getMediaTagCode() {
        return mediaTagCode;
    }

    public void setMediaTagCode(String mediaTagCode) {
        this.mediaTagCode = mediaTagCode;
    }

    public Integer getMediaAppType() {
        return mediaAppType;
    }

    public void setMediaAppType(Integer mediaAppType) {
        this.mediaAppType = mediaAppType;
    }

    public Integer getMediaTagType() {
        return mediaTagType;
    }

    public void setMediaTagType(Integer mediaTagType) {
        this.mediaTagType = mediaTagType;
    }

    public Integer getMediaTagBidType() {
        return mediaTagBidType;
    }

    public void setMediaTagBidType(Integer mediaTagBidType) {
        this.mediaTagBidType = mediaTagBidType;
    }

    public String getMediaAppTypeStr() {
        return mediaAppTypeStr;
    }

    public void setMediaAppTypeStr(String mediaAppTypeStr) {
        this.mediaAppTypeStr = mediaAppTypeStr;
    }

    public String getMediaTagTypeStr() {
        return mediaTagTypeStr;
    }

    public void setMediaTagTypeStr(String mediaTagTypeStr) {
        this.mediaTagTypeStr = mediaTagTypeStr;
    }

    public String getMediaTagBidTypeStr() {
        return mediaTagBidTypeStr;
    }

    public void setMediaTagBidTypeStr(String mediaTagBidTypeStr) {
        this.mediaTagBidTypeStr = mediaTagBidTypeStr;
    }

    public Double getParticipatingGap() {
        return participatingGap;
    }

    public void setParticipatingGap(Double participatingGap) {
        this.participatingGap = participatingGap;
    }

    public Double getExposureGap() {
        return exposureGap;
    }

    public void setExposureGap(Double exposureGap) {
        this.exposureGap = exposureGap;
    }

    public Double getClickGap() {
        return clickGap;
    }

    public void setClickGap(Double clickGap) {
        this.clickGap = clickGap;
    }

    public Double getAmountGap() {
        return amountGap;
    }

    public void setAmountGap(Double amountGap) {
        this.amountGap = amountGap;
    }

    public Double getMediaSettlementRatio() {
        return mediaSettlementRatio;
    }

    public void setMediaSettlementRatio(Double mediaSettlementRatio) {
        this.mediaSettlementRatio = mediaSettlementRatio;
    }

    public Double getMediaTagSettlementRatio() {
        return mediaTagSettlementRatio;
    }

    public void setMediaTagSettlementRatio(Double mediaTagSettlementRatio) {
        this.mediaTagSettlementRatio = mediaTagSettlementRatio;
    }


    public BigDecimal getEcPm() {
        return ecPm;
    }

    public void setEcPm(BigDecimal ecPm) {
        this.ecPm = ecPm;
    }

    public BigDecimal getCpc() {
        return cpc;
    }

    public void setCpc(BigDecimal cpc) {
        this.cpc = cpc;
    }

    public Long getRequestTotal() {
        return requestTotal;
    }

    public void setRequestTotal(Long requestTotal) {
        this.requestTotal = requestTotal;
    }

    public String getStateStr() {
        return stateStr;
    }

    public void setStateStr(String stateStr) {
        this.stateStr = stateStr;
    }

    public String getReleaseStateStr() {
        return releaseStateStr;
    }

    public void setReleaseStateStr(String releaseStateStr) {
        this.releaseStateStr = releaseStateStr;
    }

    public Double getParticipatingRate() {
        return participatingRate;
    }

    public void setParticipatingRate(Double participatingRate) {
        this.participatingRate = participatingRate;
    }
}
