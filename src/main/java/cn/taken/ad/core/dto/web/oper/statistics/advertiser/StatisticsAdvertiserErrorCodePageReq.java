package cn.taken.ad.core.dto.web.oper.statistics.advertiser;

import cn.taken.ad.core.dto.global.PageReq;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class StatisticsAdvertiserErrorCodePageReq extends PageReq {

    private String beginTime;
    private String endTime;
    @NotNull(message = "维度未选择")
    private String statisticsType;
    private String errorCode;
    private Long advertiserId;
    private Long advertiserAppId;
    private Long advertiserTagId;

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Long getAdvertiserTagId() {
        return advertiserTagId;
    }

    public void setAdvertiserTagId(Long advertiserTagId) {
        this.advertiserTagId = advertiserTagId;
    }
}
