package cn.taken.ad.core.dto.web.oper.financial.merge;

import cn.taken.ad.component.excel.common.schema.annotation.ExcelColumn;
import cn.taken.ad.component.excel.common.schema.annotation.ExcelSheet;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.core.pojo.financial.FinancialMediaAdvertiser;
import cn.taken.ad.utils.web.FinancialUtils;

import java.math.BigDecimal;

@ExcelSheet(isAutoWidth = true)
public class FinancialMediaAdvInfo extends FinancialMediaAdvertiser {

    private static final long serialVersionUID = 8757925867766064707L;

    @ExcelColumn(index = 1, title = "媒体")
    private String mediaName;
    @ExcelColumn(index = 2, title = "媒体CODE")
    private String mediaCode;
    @ExcelColumn(index = 3, title = "媒体APP名称")
    private String mediaAppName;
    @ExcelColumn(index = 4, title = "媒体APPCODE")
    private String mediaAppCode;
    private Integer mediaAppType;
    @ExcelColumn(index = 5, title = "媒体APP类型")
    private String mediaAppTypeStr;
    @ExcelColumn(index = 6, title = "媒体广告位名称")
    private String mediaTagName;
    @ExcelColumn(index = 7, title = "媒体广告位CODE")
    private String mediaTagCode;
    private Integer mediaTagType;
    @ExcelColumn(index = 8, title = "媒体广告位类型")
    private String mediaTagTypeStr;
    private Integer mediaTagBidType;
    @ExcelColumn(index = 9, title = "媒体广告位结算类型")
    private String mediaTagBidTypeStr;

    @ExcelColumn(index = 10, title = "预算")
    private String advertiserName;
    @ExcelColumn(index = 11, title = "预算APP名称")
    private String advertiserAppName;
    @ExcelColumn(index = 12, title = "预算APP CODE")
    private String advertiserAppCode;
    private Integer advertiserAppType;
    @ExcelColumn(index = 13, title = "预算APP类型")
    private String advertiserAppTypeStr;
    @ExcelColumn(index = 14, title = "预算广告位名称")
    private String advertiserTagName;
    @ExcelColumn(index = 15, title = "预算广告位CODE")
    private String advertiserTagCode;
    private Integer advertiserTagType;
    @ExcelColumn(index = 16, title = "预算广告位类型")
    private String advertiserTagTypeStr;
    private Integer advertiserTagSettlementType;
    @ExcelColumn(index = 17, title = "预算广告位结算方式")
    private String advertiserTagSettlementTypeStr;
    @ExcelColumn(index = 18, title = "媒体ECPM")
    private BigDecimal mediaEcPm;
    @ExcelColumn(index = 19, title = "预算ECPM")
    private BigDecimal advEcPm;

    @ExcelColumn(index = 36, title = "媒体GAP填充(%)")
    private String mediaGapParticipatingTotal;
    @ExcelColumn(index = 37, title = "媒体GAP曝光(%)")
    private String mediaGapExposureTotal;
    @ExcelColumn(index = 38, title = "媒体GAP点击(%)")
    private String mediaGapClickTotal;
    @ExcelColumn(index = 39, title = "媒体GAP收入(%)")
    private String mediaGapAmount;

    @ExcelColumn(index = 40, title = "预算GAP填充(%)")
    private String advertiserGapParticipatingTotal;
    @ExcelColumn(index = 41, title = "预算GAP曝光(%)")
    private String advertiserGapExposureTotal;
    @ExcelColumn(index = 42, title = "预算GAP点击(%)")
    private String advertiserGapClickTotal;
    @ExcelColumn(index = 43, title = "预算GAP收入(%)")
    private String advertiserGapAmount;

    @ExcelColumn(index = 44, title = "盈利")
    private String gain;

    @ExcelColumn(index = 45, title = "媒体CPC")
    private BigDecimal mediaCpc;
    @ExcelColumn(index = 46, title = "预算CPC")
    private BigDecimal advCpc;


    public void fillValues() {
        if(this.getMediaRealAmount()!=null&&this.getMediaRealExposureTotal()!=null&&this.getMediaRealExposureTotal() > 0){
            this.setMediaEcPm(BigDecimalUtils.div(this.getMediaRealAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(this.getMediaRealExposureTotal()), 4));
        }else{
            this.setMediaEcPm(BigDecimal.ZERO);
        }

        if(this.getAdvertiserRealAmount()!=null&&this.getAdvertiserRealExposureTotal()!=null&&this.getAdvertiserRealExposureTotal() > 0){
            this.setAdvEcPm(BigDecimalUtils.div(this.getAdvertiserRealAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(this.getAdvertiserRealExposureTotal()), 4));
        }else{
            this.setAdvEcPm(BigDecimal.ZERO);
        }

        if (this.getMediaRealAmount() != null && this.getMediaRealClickTotal() != null && this.getMediaRealClickTotal() > 0) {
            this.setMediaCpc(BigDecimalUtils.div(this.getMediaRealAmount(), BigDecimal.valueOf(this.getMediaRealClickTotal()), 2));
        } else {
            this.setMediaCpc(BigDecimal.ZERO);
        }

        if (this.getAdvertiserRealAmount() != null && this.getAdvertiserClickTotal() != null && this.getAdvertiserClickTotal() > 0) {
            this.setAdvCpc(BigDecimalUtils.div(this.getAdvertiserRealAmount(), BigDecimal.valueOf(this.getAdvertiserClickTotal()), 2));
        } else {
            this.setAdvCpc(BigDecimal.ZERO);
        }
    }



    public String getMediaName() {
        return mediaName;
    }

    public void setMediaName(String mediaName) {
        this.mediaName = mediaName;
    }

    public String getMediaCode() {
        return mediaCode;
    }

    public void setMediaCode(String mediaCode) {
        this.mediaCode = mediaCode;
    }

    public String getMediaAppName() {
        return mediaAppName;
    }

    public void setMediaAppName(String mediaAppName) {
        this.mediaAppName = mediaAppName;
    }

    public String getMediaAppCode() {
        return mediaAppCode;
    }

    public void setMediaAppCode(String mediaAppCode) {
        this.mediaAppCode = mediaAppCode;
    }

    public Integer getMediaAppType() {
        return mediaAppType;
    }

    public void setMediaAppType(Integer mediaAppType) {
        this.mediaAppType = mediaAppType;
    }

    public String getMediaAppTypeStr() {
        return mediaAppTypeStr;
    }

    public void setMediaAppTypeStr(String mediaAppTypeStr) {
        this.mediaAppTypeStr = mediaAppTypeStr;
    }

    public String getMediaTagName() {
        return mediaTagName;
    }

    public void setMediaTagName(String mediaTagName) {
        this.mediaTagName = mediaTagName;
    }

    public String getMediaTagCode() {
        return mediaTagCode;
    }

    public void setMediaTagCode(String mediaTagCode) {
        this.mediaTagCode = mediaTagCode;
    }

    public Integer getMediaTagType() {
        return mediaTagType;
    }

    public void setMediaTagType(Integer mediaTagType) {
        this.mediaTagType = mediaTagType;
    }

    public String getMediaTagTypeStr() {
        return mediaTagTypeStr;
    }

    public void setMediaTagTypeStr(String mediaTagTypeStr) {
        this.mediaTagTypeStr = mediaTagTypeStr;
    }

    public Integer getMediaTagBidType() {
        return mediaTagBidType;
    }

    public void setMediaTagBidType(Integer mediaTagBidType) {
        this.mediaTagBidType = mediaTagBidType;
    }

    public String getMediaTagBidTypeStr() {
        return mediaTagBidTypeStr;
    }

    public void setMediaTagBidTypeStr(String mediaTagBidTypeStr) {
        this.mediaTagBidTypeStr = mediaTagBidTypeStr;
    }

    public String getAdvertiserName() {
        return advertiserName;
    }

    public void setAdvertiserName(String advertiserName) {
        this.advertiserName = advertiserName;
    }

    public String getAdvertiserAppName() {
        return advertiserAppName;
    }

    public void setAdvertiserAppName(String advertiserAppName) {
        this.advertiserAppName = advertiserAppName;
    }

    public String getAdvertiserAppCode() {
        return advertiserAppCode;
    }

    public void setAdvertiserAppCode(String advertiserAppCode) {
        this.advertiserAppCode = advertiserAppCode;
    }

    public Integer getAdvertiserAppType() {
        return advertiserAppType;
    }

    public void setAdvertiserAppType(Integer advertiserAppType) {
        this.advertiserAppType = advertiserAppType;
    }

    public String getAdvertiserAppTypeStr() {
        return advertiserAppTypeStr;
    }

    public void setAdvertiserAppTypeStr(String advertiserAppTypeStr) {
        this.advertiserAppTypeStr = advertiserAppTypeStr;
    }

    public String getAdvertiserTagName() {
        return advertiserTagName;
    }

    public void setAdvertiserTagName(String advertiserTagName) {
        this.advertiserTagName = advertiserTagName;
    }

    public String getAdvertiserTagCode() {
        return advertiserTagCode;
    }

    public void setAdvertiserTagCode(String advertiserTagCode) {
        this.advertiserTagCode = advertiserTagCode;
    }

    public Integer getAdvertiserTagType() {
        return advertiserTagType;
    }

    public void setAdvertiserTagType(Integer advertiserTagType) {
        this.advertiserTagType = advertiserTagType;
    }

    public String getAdvertiserTagTypeStr() {
        return advertiserTagTypeStr;
    }

    public void setAdvertiserTagTypeStr(String advertiserTagTypeStr) {
        this.advertiserTagTypeStr = advertiserTagTypeStr;
    }

    public Integer getAdvertiserTagSettlementType() {
        return advertiserTagSettlementType;
    }

    public void setAdvertiserTagSettlementType(Integer advertiserTagSettlementType) {
        this.advertiserTagSettlementType = advertiserTagSettlementType;
    }

    public String getAdvertiserTagSettlementTypeStr() {
        return advertiserTagSettlementTypeStr;
    }

    public void setAdvertiserTagSettlementTypeStr(String advertiserTagSettlementTypeStr) {
        this.advertiserTagSettlementTypeStr = advertiserTagSettlementTypeStr;
    }

    public BigDecimal getMediaEcPm() {
        return mediaEcPm;
    }

    public void setMediaEcPm(BigDecimal mediaEcPm) {
        this.mediaEcPm = mediaEcPm;
    }

    public BigDecimal getAdvEcPm() {
        return advEcPm;
    }

    public void setAdvEcPm(BigDecimal advEcPm) {
        this.advEcPm = advEcPm;
    }

    public String getMediaGapParticipatingTotal() {
        return mediaGapParticipatingTotal;
    }

    public void setMediaGapParticipatingTotal(String mediaGapParticipatingTotal) {
        this.mediaGapParticipatingTotal = mediaGapParticipatingTotal;
    }

    public String getMediaGapExposureTotal() {
        return mediaGapExposureTotal;
    }

    public void setMediaGapExposureTotal(String mediaGapExposureTotal) {
        this.mediaGapExposureTotal = mediaGapExposureTotal;
    }

    public String getMediaGapClickTotal() {
        return mediaGapClickTotal;
    }

    public void setMediaGapClickTotal(String mediaGapClickTotal) {
        this.mediaGapClickTotal = mediaGapClickTotal;
    }

    public String getMediaGapAmount() {
        return mediaGapAmount;
    }

    public void setMediaGapAmount(String mediaGapAmount) {
        this.mediaGapAmount = mediaGapAmount;
    }

    public String getAdvertiserGapParticipatingTotal() {
        return advertiserGapParticipatingTotal;
    }

    public void setAdvertiserGapParticipatingTotal(String advertiserGapParticipatingTotal) {
        this.advertiserGapParticipatingTotal = advertiserGapParticipatingTotal;
    }

    public String getAdvertiserGapExposureTotal() {
        return advertiserGapExposureTotal;
    }

    public void setAdvertiserGapExposureTotal(String advertiserGapExposureTotal) {
        this.advertiserGapExposureTotal = advertiserGapExposureTotal;
    }

    public String getAdvertiserGapClickTotal() {
        return advertiserGapClickTotal;
    }

    public void setAdvertiserGapClickTotal(String advertiserGapClickTotal) {
        this.advertiserGapClickTotal = advertiserGapClickTotal;
    }

    public String getAdvertiserGapAmount() {
        return advertiserGapAmount;
    }

    public void setAdvertiserGapAmount(String advertiserGapAmount) {
        this.advertiserGapAmount = advertiserGapAmount;
    }

    public String getGain() {
        return gain;
    }

    public void setGain(String gain) {
        this.gain = gain;
    }

    public BigDecimal getMediaCpc() {
        return mediaCpc;
    }

    public void setMediaCpc(BigDecimal mediaCpc) {
        this.mediaCpc = mediaCpc;
    }

    public BigDecimal getAdvCpc() {
        return advCpc;
    }

    public void setAdvCpc(BigDecimal advCpc) {
        this.advCpc = advCpc;
    }
}
