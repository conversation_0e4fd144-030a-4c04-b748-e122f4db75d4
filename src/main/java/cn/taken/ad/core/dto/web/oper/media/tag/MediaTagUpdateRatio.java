package cn.taken.ad.core.dto.web.oper.media.tag;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class MediaTagUpdateRatio {
    @NotNull(message = "ids集合不能为空")
    private Long[] ids;
    @NotNull(message = "分成比例不能为空")
    private Double ratio;

    public Long[] getIds() {
        return ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Double getRatio() {
        return ratio;
    }

    public void setRatio(Double ratio) {
        this.ratio = ratio;
    }
}
