package cn.taken.ad.core.dto.web.oper.statistics.media;

import cn.taken.ad.component.excel.common.schema.annotation.ExcelColumn;
import cn.taken.ad.component.excel.common.schema.annotation.ExcelSheet;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.business.TagType;
import cn.taken.ad.constant.state.StatisticsType;

import java.math.BigDecimal;
import java.util.Date;

@ExcelSheet(isAutoWidth = true)
public class StatisticsMediaRequestInfo {

    private Long id;
    private Long mediaId;
    private Long mediaAppId;
    private Long mediaTagId;
    private Long strategyId;

    private Long useTimeTotal;

    private Integer bidType;

    @ExcelColumn(index = 0, title = "维度")
    private String statisticsType;
    @ExcelColumn(index = 1, title = "时间")
    private String statisticsTime;
    @ExcelColumn(index = 2, title = "媒体名称")
    private String mediaName;
    @ExcelColumn(index = 3, title = "媒体CODE")
    private String mediaCode;
    @ExcelColumn(index = 4, title = "APP名称")
    private String appName;
    @ExcelColumn(index = 5, title = "APP CODE")
    private String appCode;
    private Integer appType;
    @ExcelColumn(index = 6, title = "APP系统")
    private String appTypeName;
    @ExcelColumn(index = 7, title = "广告位名称")
    private String tagName;
    @ExcelColumn(index = 8, title = "广告位CODE")
    private String tagCode;
    private Integer tagType;
    @ExcelColumn(index = 9, title = "广告位类型")
    private String tagTypeName;
    @ExcelColumn(index = 10, title = "结算类型")
    private String bidTypeName;

    @ExcelColumn(index = 11, title = "请求")
    private Long reqTotal;
    @ExcelColumn(index = 12, title = "请求失败")
    private Long reqInvalidTotal;
    @ExcelColumn(index = 13, title = "请求失败率(%)")
    private BigDecimal reqInvalidRate;
    @ExcelColumn(index = 14, title = "响应失败")
    private Long respFailTotal;
    @ExcelColumn(index = 15, title = "响应失败率(%)")
    private BigDecimal respFailRate;
    @ExcelColumn(index = 16, title = "填充")
    private Long participatingTotal;
    @ExcelColumn(index = 17, title = "填充率(%)")
    private BigDecimal participatingRate;
    @ExcelColumn(index = 18, title = "竟胜")
    private Long winTotal;
    @ExcelColumn(index = 19, title = "竟胜率(%)")
    private BigDecimal winRate;
    @ExcelColumn(index = 20, title = "消费(元)")
    private BigDecimal amount;

    @ExcelColumn(index = 21, title = "最大请求时间(毫秒)")
    private Long maxTime;
    @ExcelColumn(index = 22, title = "最平均请求时间(毫秒)")
    private Long avgTime;
    @ExcelColumn(index = 23, title = "最小请求时间(毫秒)")
    private Long minTime;

    @ExcelColumn(index = 24, title = "曝光")
    private Long event_1;
    @ExcelColumn(index = 25, title = "曝光率(%)")
    private BigDecimal event_1Rate;
    @ExcelColumn(index = 26, title = "点击")
    private Long event_2;
    @ExcelColumn(index = 27, title = "点击率(%)")
    private BigDecimal event_2Rate;
    @ExcelColumn(index = 28, title = "下载开始")
    private Long event_3;
    @ExcelColumn(index = 29, title = "下载完成")
    private Long event_4;
    @ExcelColumn(index = 30, title = "安装开始")
    private Long event_5;
    @ExcelColumn(index = 31, title = "安装完成")
    private Long event_6;
    @ExcelColumn(index = 32, title = "安装完成后打开")
    private Long event_7;
    @ExcelColumn(index = 33, title = "激活")
    private Long event_8;
    @ExcelColumn(index = 34, title = "广告被关闭")
    private Long event_9;
    @ExcelColumn(index = 35, title = "deeplink打开失败")
    private Long event_10;
    @ExcelColumn(index = 36, title = "deeplink成功打开")
    private Long event_11;
    @ExcelColumn(index = 37, title = "尝试调起deeplink")
    private Long event_12;
    @ExcelColumn(index = 38, title = "APP未安装")
    private Long event_13;
    @ExcelColumn(index = 39, title = "APP已安装")
    private Long event_14;
    @ExcelColumn(index = 40, title = "视频开始播放")
    private Long event_15;
    @ExcelColumn(index = 41, title = "视频开始播放至25%")
    private Long event_16;
    @ExcelColumn(index = 42, title = "视频开始播放至50%")
    private Long event_17;
    @ExcelColumn(index = 43, title = "视频开始播放至75%")
    private Long event_18;
    @ExcelColumn(index = 44, title = "视频播放结束")
    private Long event_19;
    @ExcelColumn(index = 45, title = "视频跳过")
    private Long event_20;
    @ExcelColumn(index = 46, title = "视频关闭")
    private Long event_21;
    @ExcelColumn(index = 47, title = "视频全屏播放")
    private Long event_22;
    @ExcelColumn(index = 48, title = "视频退出全屏播放")
    private Long event_23;
    @ExcelColumn(index = 49, title = "视频加载成功")
    private Long event_24;
    @ExcelColumn(index = 50, title = "视频加载失败")
    private Long event_25;
    @ExcelColumn(index = 51, title = "视频静音")
    private Long event_26;
    @ExcelColumn(index = 52, title = "视频取消静音")
    private Long event_27;
    @ExcelColumn(index = 53, title = "暂停播放")
    private Long event_28;
    @ExcelColumn(index = 54, title = "继续播放")
    private Long event_29;
    @ExcelColumn(index = 55, title = "播放错误")
    private Long event_30;
    @ExcelColumn(index = 56, title = "视频重播")
    private Long event_31;
    @ExcelColumn(index = 57, title = "视频上滑事件")
    private Long event_32;
    @ExcelColumn(index = 58, title = "视频下滑事件")
    private Long event_33;
    @ExcelColumn(index = 59, title = "视频播放完成展示出后贴片内容")
    private Long event_34;
    @ExcelColumn(index = 60, title = "视频播放中点击")
    private Long event_35;
    @ExcelColumn(index = 61, title = "小程序调起成功")
    private Long event_36;
    @ExcelColumn(index = 62, title = "小程序调起失败")
    private Long event_37;
    @ExcelColumn(index = 63, title = "ECPM")
    private BigDecimal ecPm;
    //CPC数据，金额/点击次数
    @ExcelColumn(index = 64, title = "CPC")
    private BigDecimal cpc;

    public void fillValues() {
        StatisticsType type = StatisticsType.getByCode(statisticsType);
        if (type != null) {
            this.setStatisticsType(type.getName());
            Date date = DateUtils.parseDate(this.getStatisticsTime(),type.getFormat());
            String dateStr = DateUtils.toString(date,type.getShowFormat());
            this.setStatisticsTime(dateStr);
        }
        this.setAmount(null == this.getAmount() ? BigDecimal.ZERO : BigDecimalUtils.div(this.getAmount(), BigDecimal.valueOf(100), 4));
        OsType osType = OsType.findByType(this.getAppType());
        if (null != osType) {
            this.setAppTypeName(osType.getName());
        }
        TagType tagType = TagType.findByType(this.getAppType());
        if (null != tagType) {
            this.setTagTypeName(tagType.getDesc());
        }

        if (this.getReqInvalidTotal() != null && this.getReqTotal() != null && this.getReqTotal() > 0) {
            this.setReqInvalidRate(BigDecimalUtils.div(BigDecimal.valueOf(100 * this.getReqInvalidTotal()), BigDecimal.valueOf(this.getReqTotal()), 2));
        } else {
            this.setReqInvalidRate(BigDecimal.ZERO);
        }

        if (this.getRespFailTotal() != null && this.getReqTotal() != null && this.getReqTotal() > 0) {
            this.setRespFailRate(BigDecimalUtils.div(BigDecimal.valueOf(100 * this.getRespFailTotal()), BigDecimal.valueOf(this.getReqTotal()), 2));
        } else {
            this.setRespFailRate(BigDecimal.ZERO);
        }

        if (this.getParticipatingTotal() != null && this.getReqTotal() != null && this.getReqTotal() > 0) {
            this.setParticipatingRate(BigDecimalUtils.div(BigDecimal.valueOf(100 * this.getParticipatingTotal()), BigDecimal.valueOf(this.getReqTotal()), 2));
        } else {
            this.setParticipatingRate(BigDecimal.ZERO);
        }

        if (this.getWinTotal() != null && this.getParticipatingTotal() != null && this.getParticipatingTotal() > 0) {
            this.setWinRate(BigDecimalUtils.div(BigDecimal.valueOf(100 * this.getWinTotal()), BigDecimal.valueOf(this.getParticipatingTotal()), 2));
        } else {
            this.setWinRate(BigDecimal.ZERO);
        }

        //曝光率调整->曝光率=曝光/竟胜；无竟胜数时使用填充数
        if (this.getEvent_1() != null && this.getWinTotal() != null && this.getWinTotal() > 0) {
            this.setEvent_1Rate(BigDecimalUtils.div(BigDecimal.valueOf(100L * this.getEvent_1()), BigDecimal.valueOf(this.getWinTotal()), 2));
        } else if (this.getEvent_1() != null && this.getParticipatingTotal() != null && this.getParticipatingTotal() > 0) {
            this.setEvent_1Rate(BigDecimalUtils.div(BigDecimal.valueOf(100L * this.getEvent_1()), BigDecimal.valueOf(this.getParticipatingTotal()), 2));
        } else {
            this.setEvent_1Rate(BigDecimal.ZERO);
        }

        if (this.getEvent_2() != null && this.getEvent_1() != null && this.getEvent_1() > 0) {
            this.setEvent_2Rate(BigDecimalUtils.div(BigDecimal.valueOf(100L * this.getEvent_2()), BigDecimal.valueOf(this.getEvent_1()), 2));
        } else {
            this.setEvent_2Rate(BigDecimal.ZERO);
        }

        if(this.getAmount()!=null&&this.getEvent_1()!=null&&this.getEvent_1() > 0){
            this.setEcPm(BigDecimalUtils.div(this.getAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(this.getEvent_1()), 4));
        }else{
            this.setEcPm(BigDecimal.ZERO);
        }

        if (this.bidType != null) {
            if (this.bidType == 1) {
                this.setBidTypeName("RTB");
            } else {
                this.setBidTypeName("分成");
            }
        } else {
            this.setBidTypeName("未知");
        }
        //cpc
        if (this.getAmount() != null && this.getEvent_2() != null && this.getEvent_2() > 0) {
            this.setCpc(BigDecimalUtils.div(this.getAmount(), BigDecimal.valueOf(this.getEvent_2()), 2));
        } else {
            this.setCpc(BigDecimal.ZERO);
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    public Long getUseTimeTotal() {
        return useTimeTotal;
    }

    public void setUseTimeTotal(Long useTimeTotal) {
        this.useTimeTotal = useTimeTotal;
    }

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getStatisticsTime() {
        return statisticsTime;
    }

    public void setStatisticsTime(String statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public String getMediaName() {
        return mediaName;
    }

    public void setMediaName(String mediaName) {
        this.mediaName = mediaName;
    }

    public String getMediaCode() {
        return mediaCode;
    }

    public void setMediaCode(String mediaCode) {
        this.mediaCode = mediaCode;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    public String getAppTypeName() {
        return appTypeName;
    }

    public void setAppTypeName(String appTypeName) {
        this.appTypeName = appTypeName;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getTagCode() {
        return tagCode;
    }

    public void setTagCode(String tagCode) {
        this.tagCode = tagCode;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public String getTagTypeName() {
        return tagTypeName;
    }

    public void setTagTypeName(String tagTypeName) {
        this.tagTypeName = tagTypeName;
    }

    public Long getReqTotal() {
        return reqTotal;
    }

    public void setReqTotal(Long reqTotal) {
        this.reqTotal = reqTotal;
    }

    public Long getReqInvalidTotal() {
        return reqInvalidTotal;
    }

    public void setReqInvalidTotal(Long reqInvalidTotal) {
        this.reqInvalidTotal = reqInvalidTotal;
    }

    public BigDecimal getReqInvalidRate() {
        return reqInvalidRate;
    }

    public void setReqInvalidRate(BigDecimal reqInvalidRate) {
        this.reqInvalidRate = reqInvalidRate;
    }

    public Long getRespFailTotal() {
        return respFailTotal;
    }

    public void setRespFailTotal(Long respFailTotal) {
        this.respFailTotal = respFailTotal;
    }

    public BigDecimal getRespFailRate() {
        return respFailRate;
    }

    public void setRespFailRate(BigDecimal respFailRate) {
        this.respFailRate = respFailRate;
    }

    public Long getParticipatingTotal() {
        return participatingTotal;
    }

    public void setParticipatingTotal(Long participatingTotal) {
        this.participatingTotal = participatingTotal;
    }

    public BigDecimal getParticipatingRate() {
        return participatingRate;
    }

    public void setParticipatingRate(BigDecimal participatingRate) {
        this.participatingRate = participatingRate;
    }

    public Long getWinTotal() {
        return winTotal;
    }

    public void setWinTotal(Long winTotal) {
        this.winTotal = winTotal;
    }

    public BigDecimal getWinRate() {
        return winRate;
    }

    public void setWinRate(BigDecimal winRate) {
        this.winRate = winRate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(Long maxTime) {
        this.maxTime = maxTime;
    }

    public Long getAvgTime() {
        return avgTime;
    }

    public void setAvgTime(Long avgTime) {
        this.avgTime = avgTime;
    }

    public Long getMinTime() {
        return minTime;
    }

    public void setMinTime(Long minTime) {
        this.minTime = minTime;
    }

    public Long getEvent_1() {
        return event_1;
    }

    public void setEvent_1(Long event_1) {
        this.event_1 = event_1;
    }

    public BigDecimal getEvent_1Rate() {
        return event_1Rate;
    }

    public void setEvent_1Rate(BigDecimal event_1Rate) {
        this.event_1Rate = event_1Rate;
    }

    public Long getEvent_2() {
        return event_2;
    }

    public void setEvent_2(Long event_2) {
        this.event_2 = event_2;
    }

    public BigDecimal getEvent_2Rate() {
        return event_2Rate;
    }

    public void setEvent_2Rate(BigDecimal event_2Rate) {
        this.event_2Rate = event_2Rate;
    }

    public Long getEvent_3() {
        return event_3;
    }

    public void setEvent_3(Long event_3) {
        this.event_3 = event_3;
    }

    public Long getEvent_4() {
        return event_4;
    }

    public void setEvent_4(Long event_4) {
        this.event_4 = event_4;
    }

    public Long getEvent_5() {
        return event_5;
    }

    public void setEvent_5(Long event_5) {
        this.event_5 = event_5;
    }

    public Long getEvent_6() {
        return event_6;
    }

    public void setEvent_6(Long event_6) {
        this.event_6 = event_6;
    }

    public Long getEvent_7() {
        return event_7;
    }

    public void setEvent_7(Long event_7) {
        this.event_7 = event_7;
    }

    public Long getEvent_8() {
        return event_8;
    }

    public void setEvent_8(Long event_8) {
        this.event_8 = event_8;
    }

    public Long getEvent_9() {
        return event_9;
    }

    public void setEvent_9(Long event_9) {
        this.event_9 = event_9;
    }

    public Long getEvent_10() {
        return event_10;
    }

    public void setEvent_10(Long event_10) {
        this.event_10 = event_10;
    }

    public Long getEvent_11() {
        return event_11;
    }

    public void setEvent_11(Long event_11) {
        this.event_11 = event_11;
    }

    public Long getEvent_12() {
        return event_12;
    }

    public void setEvent_12(Long event_12) {
        this.event_12 = event_12;
    }

    public Long getEvent_13() {
        return event_13;
    }

    public void setEvent_13(Long event_13) {
        this.event_13 = event_13;
    }

    public Long getEvent_14() {
        return event_14;
    }

    public void setEvent_14(Long event_14) {
        this.event_14 = event_14;
    }

    public Long getEvent_15() {
        return event_15;
    }

    public void setEvent_15(Long event_15) {
        this.event_15 = event_15;
    }

    public Long getEvent_16() {
        return event_16;
    }

    public void setEvent_16(Long event_16) {
        this.event_16 = event_16;
    }

    public Long getEvent_17() {
        return event_17;
    }

    public void setEvent_17(Long event_17) {
        this.event_17 = event_17;
    }

    public Long getEvent_18() {
        return event_18;
    }

    public void setEvent_18(Long event_18) {
        this.event_18 = event_18;
    }

    public Long getEvent_19() {
        return event_19;
    }

    public void setEvent_19(Long event_19) {
        this.event_19 = event_19;
    }

    public Long getEvent_20() {
        return event_20;
    }

    public void setEvent_20(Long event_20) {
        this.event_20 = event_20;
    }

    public Long getEvent_21() {
        return event_21;
    }

    public void setEvent_21(Long event_21) {
        this.event_21 = event_21;
    }

    public Long getEvent_22() {
        return event_22;
    }

    public void setEvent_22(Long event_22) {
        this.event_22 = event_22;
    }

    public Long getEvent_23() {
        return event_23;
    }

    public void setEvent_23(Long event_23) {
        this.event_23 = event_23;
    }

    public Long getEvent_24() {
        return event_24;
    }

    public void setEvent_24(Long event_24) {
        this.event_24 = event_24;
    }

    public Long getEvent_25() {
        return event_25;
    }

    public void setEvent_25(Long event_25) {
        this.event_25 = event_25;
    }

    public Long getEvent_26() {
        return event_26;
    }

    public void setEvent_26(Long event_26) {
        this.event_26 = event_26;
    }

    public Long getEvent_27() {
        return event_27;
    }

    public void setEvent_27(Long event_27) {
        this.event_27 = event_27;
    }

    public Long getEvent_28() {
        return event_28;
    }

    public void setEvent_28(Long event_28) {
        this.event_28 = event_28;
    }

    public Long getEvent_29() {
        return event_29;
    }

    public void setEvent_29(Long event_29) {
        this.event_29 = event_29;
    }

    public Long getEvent_30() {
        return event_30;
    }

    public void setEvent_30(Long event_30) {
        this.event_30 = event_30;
    }

    public Long getEvent_31() {
        return event_31;
    }

    public void setEvent_31(Long event_31) {
        this.event_31 = event_31;
    }

    public Long getEvent_32() {
        return event_32;
    }

    public void setEvent_32(Long event_32) {
        this.event_32 = event_32;
    }

    public Long getEvent_33() {
        return event_33;
    }

    public void setEvent_33(Long event_33) {
        this.event_33 = event_33;
    }

    public Long getEvent_34() {
        return event_34;
    }

    public void setEvent_34(Long event_34) {
        this.event_34 = event_34;
    }

    public Long getEvent_35() {
        return event_35;
    }

    public void setEvent_35(Long event_35) {
        this.event_35 = event_35;
    }

    public Long getEvent_36() {
        return event_36;
    }

    public void setEvent_36(Long event_36) {
        this.event_36 = event_36;
    }

    public Long getEvent_37() {
        return event_37;
    }

    public void setEvent_37(Long event_37) {
        this.event_37 = event_37;
    }

    public BigDecimal getEcPm() {
        return ecPm;
    }

    public void setEcPm(BigDecimal ecPm) {
        this.ecPm = ecPm;
    }

    public Integer getBidType() {
        return bidType;
    }

    public void setBidType(Integer bidType) {
        this.bidType = bidType;
    }

    public String getBidTypeName() {
        return bidTypeName;
    }

    public void setBidTypeName(String bidTypeName) {
        this.bidTypeName = bidTypeName;
    }

    public BigDecimal getCpc() {
        return cpc;
    }

    public void setCpc(BigDecimal cpc) {
        this.cpc = cpc;
    }
}
