package cn.taken.ad.core.dto.web.oper.strategy.main;

import cn.taken.ad.core.dto.global.PageReq;

public class StrategyPageReq extends PageReq {

    private Long mediaId;
    private Long mediaAppId;
    private Long mediaTagId;
    private Long advertiserId;
    private Long advertiserAppId;
    private Long advertiserTagId;
    private Integer state;

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Long getAdvertiserTagId() {
        return advertiserTagId;
    }

    public void setAdvertiserTagId(Long advertiserTagId) {
        this.advertiserTagId = advertiserTagId;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}
