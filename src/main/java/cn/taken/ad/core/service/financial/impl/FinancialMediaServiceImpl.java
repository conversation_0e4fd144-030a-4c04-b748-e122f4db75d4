package cn.taken.ad.core.service.financial.impl;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.constant.business.BidType;
import cn.taken.ad.constant.business.FinancialState;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.business.TagType;
import cn.taken.ad.core.dao.financial.FinancialMediaDao;
import cn.taken.ad.core.dao.media.MediaTagDao;
import cn.taken.ad.core.dto.web.client.media.tag.ClientMediaFinancialInfo;
import cn.taken.ad.core.dto.web.client.media.tag.ClientMediaFinancialPageReq;
import cn.taken.ad.core.dto.web.oper.financial.media.*;
import cn.taken.ad.core.dto.web.oper.financial.merge.FinancialMediaAdvTaskReq;
import cn.taken.ad.core.dto.web.oper.media.tag.MediaTagUpdateRatio;
import cn.taken.ad.core.pojo.financial.FinancialMedia;
import cn.taken.ad.core.service.financial.FinancialMediaService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class FinancialMediaServiceImpl implements FinancialMediaService {

    @Resource
    private FinancialMediaDao mediaFinancialReportDao;
    @Resource
    private MediaTagDao mediaTagDao;


    @Override
    public Page<FinancialMediaInfo> findPage(FinancialMediaPageReq req) {
        Page<FinancialMediaInfo> page = mediaFinancialReportDao.findPage(req);
        page.getList().forEach(FinancialMediaInfo::fillValues);
        return page;
    }

    @Override
    public Page<ClientMediaFinancialInfo> findClientPage(ClientMediaFinancialPageReq pageReq, Long mediaId) {
        return mediaFinancialReportDao.findClientPage(pageReq, mediaId);
    }

    @Override
    public List<FinancialMediaInfo> findExport(String beginTime, String endTime, Long mediaId, Long mediaAppId, Long mediaTagId, Integer state, Integer releaseState, Integer bidType) {
        List<FinancialMediaInfo> infos = mediaFinancialReportDao.findExport(beginTime, endTime, mediaId, mediaAppId, mediaTagId, state, releaseState, bidType);
        infos.forEach(item -> {
            item.fillValues();
            item.setMediaAppTypeStr(OsType.findByType(item.getMediaAppType()).getName());
            item.setMediaTagTypeStr(TagType.findByType(item.getMediaTagType()).getDesc());
            BidType type = BidType.findByType(item.getMediaTagBidType());
            if (null != type) {
                item.setMediaTagBidTypeStr(type.getName());
            }
            if (item.getRealAmount() != null && item.getRealExposureTotal() != null && item.getRealExposureTotal() > 0) {
                item.setEcPm(BigDecimalUtils.div(item.getRealAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(item.getRealExposureTotal()), 4));
            } else {
                item.setEcPm(BigDecimal.ZERO);
            }


        });
        return infos;
    }

    @Override
    public void saveBatch(List<FinancialMedia> reports) {
        mediaFinancialReportDao.saveBatch(reports);
    }

    @Override
    public SuperResult<String> modify(FinancialMediaModifyReq req) {
        FinancialMedia financialMedia = mediaFinancialReportDao.findById(req.getId());
        if (null == financialMedia) {
            return SuperResult.badResult("账单不存在");
        }
        financialMedia.setRealAmount(req.getRealAmount());
        financialMedia.setRealParticipatingTotal(req.getRealParticipatingTotal());
        financialMedia.setRealExposureTotal(req.getRealExposureTotal());
        financialMedia.setRealClickTotal(req.getRealClickTotal());
        financialMedia.setSettlementRatio(req.getSettlementRatio());
        //同步更新媒体的分成比例
        MediaTagUpdateRatio updateRatio = new MediaTagUpdateRatio();
        updateRatio.setIds(new Long[]{financialMedia.getMediaTagId()});
        updateRatio.setRatio(req.getSettlementRatio());
        mediaTagDao.updateRatios(updateRatio);
        mediaFinancialReportDao.update(financialMedia);
        return SuperResult.rightResult();
    }

    @Override
    public void updateBatchForImport(List<FinancialMedia> list) {
        mediaFinancialReportDao.updateBatchForImport(list);
    }

    @Override
    public SuperResult<String> modifyRelease(FinancialMediaReleaseReq req) {
        FinancialMedia financialMedia = mediaFinancialReportDao.findById(req.getId());
        if (null == financialMedia) {
            return SuperResult.badResult("记录错误");
        }
        financialMedia.setReleaseState(req.getReleaseState());
        mediaFinancialReportDao.update(financialMedia);
        return SuperResult.rightResult();
    }

    @Override
    public List<FinancialMediaInfo> updateAndFindForTask(FinancialMediaAdvTaskReq taskReq) {
        List<FinancialMediaInfo> datas = mediaFinancialReportDao.findForTask(taskReq);
        if (null != datas && !datas.isEmpty()) {
            List<Long> ids = datas.stream().map(FinancialMediaInfo::getId).collect(Collectors.toList());
            mediaFinancialReportDao.updateStateByIds(ids, FinancialState.CALCULATING.getType());
        }
        return datas;
    }

    @Override
    public FinancialMediaInfo findInfo(FinancialMediaInfoReq req) {
        FinancialMediaInfo info = mediaFinancialReportDao.findInfo(req);
        if (null != info) {
            info.fillValues();
        }
        return info;
    }

    @Override
    public void updateRatio(List<Long> ids, Double ratio) {
        Set<Long> mediaTagIds = mediaFinancialReportDao.findByIds(ids).stream().map(FinancialMedia::getMediaTagId).collect(Collectors.toSet());
        mediaFinancialReportDao.updateRatio(ids, ratio);
        //同步更新媒体的分成比例
        MediaTagUpdateRatio updateRatio = new MediaTagUpdateRatio();
        updateRatio.setIds(mediaTagIds.toArray(new Long[0]));
        updateRatio.setRatio(ratio);
        mediaTagDao.updateRatios(updateRatio);
    }

    @Override
    public void updateReleaseState(List<Long> ids, int state) {
        mediaFinancialReportDao.updateReleaseStateByIds(ids, state);
    }

    @Override
    public List<ClientMediaFinancialInfo> findClientList(String beginTime, String endTime, Long mediaAppId, Long mediaTagId, Long mediaId) {
        return mediaFinancialReportDao.findClientList(beginTime, endTime, mediaAppId, mediaTagId, mediaId);
    }

    @Override
    public List<FinancialMediaInfo> updateAndFindForTaskByIds(FinancialMediaAdvTaskReq taskReq, List<Long> mediaTagIds) {
        return mediaFinancialReportDao.updateAndFindForTaskByIds(taskReq, mediaTagIds);
    }
}
