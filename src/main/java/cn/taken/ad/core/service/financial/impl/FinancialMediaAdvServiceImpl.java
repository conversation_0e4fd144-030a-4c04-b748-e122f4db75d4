package cn.taken.ad.core.service.financial.impl;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.constant.business.BidType;
import cn.taken.ad.constant.business.FinancialState;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.business.TagType;
import cn.taken.ad.core.dao.financial.FinancialAdvertiserDao;
import cn.taken.ad.core.dao.financial.FinancialMediaAdvDao;
import cn.taken.ad.core.dao.financial.FinancialMediaDao;
import cn.taken.ad.core.dto.web.oper.financial.media.FinancialMediaInfo;
import cn.taken.ad.core.dto.web.oper.financial.merge.FinancialMediaAdvInfo;
import cn.taken.ad.core.dto.web.oper.financial.merge.FinancialMediaAdvModifyReq;
import cn.taken.ad.core.dto.web.oper.financial.merge.FinancialMediaAdvPageReq;
import cn.taken.ad.core.dto.web.oper.financial.merge.FinancialMediaAdvTaskReq;
import cn.taken.ad.core.pojo.financial.FinancialAdvertiser;
import cn.taken.ad.core.pojo.financial.FinancialMedia;
import cn.taken.ad.core.pojo.financial.FinancialMediaAdvertiser;
import cn.taken.ad.core.service.financial.FinancialMediaAdvService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class FinancialMediaAdvServiceImpl implements FinancialMediaAdvService {
    @Resource
    private FinancialMediaAdvDao financialMediaAdvDao;
    @Resource
    private FinancialMediaDao financialMediaDao;
    @Resource
    private FinancialAdvertiserDao financialAdvertiserDao;

    @Override
    public Page<FinancialMediaAdvInfo> findPage(FinancialMediaAdvPageReq req) {
        Page<FinancialMediaAdvInfo> page = financialMediaAdvDao.findPage(req);
        page.getList().forEach(FinancialMediaAdvInfo::fillValues);
        return page;
    }

    @Override
    public void deleteAndSaveBatch(List<FinancialMediaAdvertiser> financials, Map<Long, FinancialMediaInfo> financialMediaInfoMap) {
        financialMediaAdvDao.deleteByTimeAndStrategyId(financials);
        financialMediaAdvDao.saveBatch(financials);
        // 聚合更新 媒体账务
        Map<String, FinancialMedia> financialMediaMap = new HashMap<>(financials.size());
        Map<String, FinancialAdvertiser> financialAdvertiserMap = new HashMap<>(financials.size());
        financials.forEach(item -> {
            String mediaKey = item.getReportTime() + "&&" + item.getMediaId() + "&&" + item.getMediaAppId() + "&&" + item.getMediaTagId();
            String advertiserKey = item.getReportTime() + "&&" + item.getAdvertiserId() + "&&" + item.getAdvertiserAppId() + "&&" + item.getAdvertiserTagId();
            FinancialMedia financialMedia = financialMediaMap.computeIfAbsent(mediaKey, k -> new FinancialMedia(item.getReportTime(), item.getMediaId(), item.getMediaAppId(), item.getMediaTagId(), item.getMediaSettlementRatio()));
            financialMedia.setState(FinancialState.CALCULATED.getType());

            //媒体实际数据计算,填充
            financialMedia.setRealParticipatingTotal(item.getMediaRealParticipatingTotal() + financialMedia.getRealParticipatingTotal());
            //媒体实际数据计算,曝光
            financialMedia.setRealExposureTotal(item.getMediaRealExposureTotal() + financialMedia.getRealExposureTotal());
            //媒体实际数据计算,点击
            financialMedia.setRealClickTotal(item.getMediaRealClickTotal() + financialMedia.getRealClickTotal());
            //媒体实际数据计算,收入
            financialMedia.setRealAmount(BigDecimalUtils.add(item.getMediaRealAmount(), financialMedia.getRealAmount()));
            //盈利
            financialMedia.setRevenueAmount(BigDecimalUtils.add(financialMedia.getRevenueAmount(), (BigDecimalUtils.sub(item.getAdvertiserRealAmount(), item.getMediaRealAmount()))));

            FinancialAdvertiser financialAdvertiser = financialAdvertiserMap.computeIfAbsent(advertiserKey, k -> new FinancialAdvertiser(item.getReportTime(), item.getAdvertiserId(), item.getAdvertiserAppId(), item.getAdvertiserTagId()));
            financialAdvertiser.setState(FinancialState.CALCULATED.getType());
            financialAdvertiser.setRevenueAmount(BigDecimalUtils.add(financialAdvertiser.getRevenueAmount(), (BigDecimalUtils.sub(item.getAdvertiserRealAmount(), item.getMediaRealAmount()))));
        });
        // 聚合更新 预算账务
        if (!financialMediaMap.isEmpty()) {
            financialMediaDao.updateForTask(new ArrayList<>(financialMediaMap.values()));
        }
        if (!financialAdvertiserMap.isEmpty()) {
            financialAdvertiserDao.updateForTask(new ArrayList<>(financialAdvertiserMap.values()));
        }
    }

    @Override
    public SuperResult<String> modify(FinancialMediaAdvModifyReq req) {
        FinancialMediaAdvertiser financialMediaAdvertiser = financialMediaAdvDao.findById(req.getId());
        if (null == financialMediaAdvertiser) {
            return SuperResult.badResult("记录错误");
        }
        financialMediaAdvertiser.setMediaRealParticipatingTotal(req.getMediaRealParticipatingTotal());
        financialMediaAdvertiser.setMediaRealExposureTotal(req.getMediaRealExposureTotal());
        financialMediaAdvertiser.setMediaRealClickTotal(req.getMediaRealClickTotal());
        financialMediaAdvertiser.setMediaRealAmount(req.getMediaRealAmount());
        financialMediaAdvertiser.setAdvertiserRealParticipatingTotal(req.getAdvertiserRealParticipatingTotal());
        financialMediaAdvertiser.setAdvertiserRealExposureTotal(req.getAdvertiserRealExposureTotal());
        financialMediaAdvertiser.setAdvertiserRealClickTotal(req.getAdvertiserRealClickTotal());
        financialMediaAdvertiser.setAdvertiserRealAmount(req.getAdvertiserRealAmount());
        financialMediaAdvDao.update(financialMediaAdvertiser);
        return SuperResult.rightResult();
    }

    @Override
    public List<FinancialMediaAdvInfo> findForTask(FinancialMediaAdvTaskReq taskReq) {
        return financialMediaAdvDao.findForTask(taskReq);
    }

    @Override
    public List<FinancialMediaAdvInfo> findExport(String beginTime, String endTime, Long mediaId, Long mediaAppId, Long mediaTagId, Long advertiserId, Long advertiserAppId, Long advertiserTagId, Integer bidType, Integer settlementType) {
        List<FinancialMediaAdvInfo> infos = financialMediaAdvDao.findExport(beginTime, endTime, mediaId, mediaAppId, mediaTagId, advertiserId, advertiserAppId, advertiserTagId, bidType, settlementType);
        infos.forEach(item -> {
            item.setAdvertiserAppTypeStr(OsType.findByType(item.getAdvertiserAppType()).getName());
            item.setAdvertiserTagTypeStr(TagType.findByType(item.getAdvertiserTagType()).getDesc());
            BidType type = BidType.findByType(item.getAdvertiserTagSettlementType());
            if (null != type) {
                item.setAdvertiserTagSettlementTypeStr(type.getName());
            }

            item.setMediaAppTypeStr(OsType.findByType(item.getMediaAppType()).getName());
            item.setMediaTagTypeStr(TagType.findByType(item.getMediaTagType()).getDesc());
            BidType mBidType = BidType.findByType(item.getMediaTagBidType());
            if (null != mBidType) {
                item.setMediaTagBidTypeStr(mBidType.getName());
            }

            if (null != item.getMediaRealParticipatingTotal() && item.getMediaRealParticipatingTotal() > 0 && null != item.getMediaParticipatingTotal() && item.getMediaParticipatingTotal() > 0) {
                String gap = computeGap(item.getMediaRealParticipatingTotal(), item.getMediaParticipatingTotal());
                item.setMediaGapParticipatingTotal(gap);
            } else {
                item.setMediaGapParticipatingTotal("0%");
            }

            if (null != item.getMediaRealExposureTotal() && item.getMediaRealExposureTotal() > 0 && null != item.getMediaExposureTotal() && item.getMediaExposureTotal() > 0) {
                String gap = computeGap(item.getMediaRealExposureTotal(), item.getMediaExposureTotal());
                item.setMediaGapExposureTotal(gap);
            } else {
                item.setMediaGapExposureTotal("0%");
            }

            if (null != item.getMediaRealClickTotal() && item.getMediaRealClickTotal() > 0 && null != item.getMediaClickTotal() && item.getMediaClickTotal() > 0) {
                String gap = computeGap(item.getMediaRealClickTotal(), item.getMediaClickTotal());
                item.setMediaGapClickTotal(gap);
            } else {
                item.setMediaGapClickTotal("0%");
            }

            if (null != item.getMediaRealAmount() && null != item.getMediaAmount()) {
                String gap = computeAmountGap(item.getMediaRealClickTotal().floatValue(), item.getMediaAmount().floatValue());
                item.setMediaGapAmount(gap);
            } else {
                item.setMediaGapAmount("0%");
            }


            if (null != item.getAdvertiserRealParticipatingTotal() && item.getAdvertiserRealParticipatingTotal() > 0 && null != item.getAdvertiserParticipatingTotal() && item.getAdvertiserParticipatingTotal() > 0) {
                String gap = computeGap(item.getAdvertiserRealParticipatingTotal(), item.getAdvertiserParticipatingTotal());
                item.setAdvertiserGapParticipatingTotal(gap);
            } else {
                item.setAdvertiserGapParticipatingTotal("0%");
            }


            if (null != item.getAdvertiserRealExposureTotal() && item.getAdvertiserRealExposureTotal() > 0 && null != item.getAdvertiserExposureTotal() && item.getAdvertiserExposureTotal() > 0) {
                String gap = computeGap(item.getAdvertiserRealExposureTotal(), item.getAdvertiserExposureTotal());
                item.setAdvertiserGapExposureTotal(gap);
            } else {
                item.setAdvertiserGapExposureTotal("0%");
            }

            if (null != item.getAdvertiserRealClickTotal() && item.getAdvertiserRealClickTotal() > 0 && null != item.getAdvertiserClickTotal() && item.getAdvertiserClickTotal() > 0) {
                String gap = computeGap(item.getAdvertiserRealClickTotal(), item.getAdvertiserClickTotal());
                item.setAdvertiserGapClickTotal(gap);
            } else {
                item.setAdvertiserGapClickTotal("0%");
            }

            if (null != item.getAdvertiserRealAmount() && null != item.getAdvertiserAmount()) {
                String gap = computeAmountGap(item.getAdvertiserRealAmount().floatValue(), item.getAdvertiserAmount().floatValue());
                item.setAdvertiserGapAmount(gap);
            } else {
                item.setAdvertiserGapAmount("0%");
            }


            if (null != item.getAdvertiserRealAmount() && null != item.getAdvertiserAmount()) {
                String gap = computeAmountGap(item.getAdvertiserRealAmount().floatValue(), item.getAdvertiserAmount().floatValue());
                item.setAdvertiserGapAmount(gap);
            } else {
                item.setAdvertiserGapAmount("0%");
            }

            if (null == item.getMediaRealAmount() && null == item.getAdvertiserRealAmount()) {
                item.setGain("0元");
            } else {
                String gain = computeRevenue(item.getMediaRealAmount(), item.getAdvertiserRealAmount());
                item.setGain(gain);
            }

            if (item.getMediaRealAmount() != null && item.getMediaRealExposureTotal() != null && item.getMediaRealExposureTotal() > 0) {
                item.setMediaEcPm(BigDecimalUtils.div(item.getMediaRealAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(item.getMediaRealExposureTotal()), 4));
            } else {
                item.setMediaEcPm(BigDecimal.ZERO);
            }
            if (item.getAdvertiserRealAmount() != null && item.getAdvertiserRealExposureTotal() != null && item.getAdvertiserRealExposureTotal() > 0) {
                item.setAdvEcPm(BigDecimalUtils.div(item.getAdvertiserRealAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(item.getAdvertiserRealExposureTotal()), 4));
            } else {
                item.setAdvEcPm(BigDecimal.ZERO);
            }

        });
        return infos;
    }

    @Override
    public void deleteAndSaveBatch(List<FinancialMediaAdvertiser> financials, String reportTime,List<Long> mediaTagIds) {
        financialMediaAdvDao.deleteByTimeAndStrategyId(financials);
        financialMediaAdvDao.saveBatch(financials);
        List<FinancialMediaAdvertiser> updates = financialMediaAdvDao.findByAdvTagIds(reportTime, mediaTagIds);
        //让最新的id排序到前面更新-媒体的分成结算比例
        Collections.sort(updates, (o1, o2) -> {
            return o2.getId().compareTo(o1.getId());
        });
        //媒体账务
        Map<String, FinancialMedia> financialMediaMap = new HashMap<>();
        Map<String, FinancialAdvertiser> financialAdvertiserMap = new HashMap<>();
        updates.forEach(item -> {
            String mediaKey = item.getReportTime() + "&&" + item.getMediaId() + "&&" + item.getMediaAppId() + "&&" + item.getMediaTagId();
            String advertiserKey = item.getReportTime() + "&&" + item.getAdvertiserId() + "&&" + item.getAdvertiserAppId() + "&&" + item.getAdvertiserTagId();
            FinancialMedia financialMedia = financialMediaMap.computeIfAbsent(mediaKey, k -> new FinancialMedia(item.getReportTime(), item.getMediaId(), item.getMediaAppId(), item.getMediaTagId(), item.getMediaSettlementRatio()));
            financialMedia.setState(FinancialState.CALCULATED.getType());

            //媒体实际数据计算,填充
            financialMedia.setRealParticipatingTotal(item.getMediaRealParticipatingTotal() + financialMedia.getRealParticipatingTotal());
            //媒体实际数据计算,曝光
            financialMedia.setRealExposureTotal(item.getMediaRealExposureTotal() + financialMedia.getRealExposureTotal());
            //媒体实际数据计算,点击
            financialMedia.setRealClickTotal(item.getMediaRealClickTotal() + financialMedia.getRealClickTotal());
            //媒体实际数据计算,收入
            financialMedia.setRealAmount(BigDecimalUtils.add(item.getMediaRealAmount(), financialMedia.getRealAmount()));
            //盈利
            financialMedia.setRevenueAmount(BigDecimalUtils.add(financialMedia.getRevenueAmount(), (BigDecimalUtils.sub(item.getAdvertiserRealAmount(), item.getMediaRealAmount()))));

            FinancialAdvertiser financialAdvertiser = financialAdvertiserMap.computeIfAbsent(advertiserKey, k -> new FinancialAdvertiser(item.getReportTime(), item.getAdvertiserId(), item.getAdvertiserAppId(), item.getAdvertiserTagId()));
            financialAdvertiser.setState(FinancialState.CALCULATED.getType());
            financialAdvertiser.setRevenueAmount(BigDecimalUtils.add(financialAdvertiser.getRevenueAmount(), (BigDecimalUtils.sub(item.getAdvertiserRealAmount(), item.getMediaRealAmount()))));
        });
        // 聚合更新 预算账务
        if (!financialMediaMap.isEmpty()) {
            financialMediaDao.updateForTask(new ArrayList<>(financialMediaMap.values()));
        }
        if (!financialAdvertiserMap.isEmpty()) {
            financialAdvertiserDao.updateForTask(new ArrayList<>(financialAdvertiserMap.values()));
        }
    }


    private String computeGap(Long numA, Long numB) {
        Long difference = Math.abs(numA - numB);
        Long base = Math.max(numA, numB);
        if (base == 0) {
            return "0%"; // 避免除以0（根据需求调整逻辑）
        }
        return (BigDecimalUtils.div(new BigDecimal(difference), new BigDecimal(base), 2).toString() + '%');
    }

    private String computeAmountGap(float numA, float numB) {
        float difference = Math.abs(numA - numB);
        float base = Math.max(numA, numB);
        if (base == 0) {
            return "0%"; // 避免除以0（根据需求调整逻辑）
        }
        return (BigDecimalUtils.div(new BigDecimal(difference), new BigDecimal(base), 2).toString() + '%');
    }

    private String computeRevenue(BigDecimal mediaAmount, BigDecimal advertiserAmount) {
        if (mediaAmount != null && advertiserAmount != null) {
            return (BigDecimalUtils.sub(advertiserAmount, mediaAmount, 3).toString() + '元');
        }
        return "0元";
    }

}
