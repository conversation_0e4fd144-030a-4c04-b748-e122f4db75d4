package cn.taken.ad.core.service.financial.impl;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.constant.business.BidType;
import cn.taken.ad.constant.business.FinancialState;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.business.TagType;
import cn.taken.ad.core.dao.financial.FinancialAdvertiserDao;
import cn.taken.ad.core.dto.web.oper.financial.advertiser.FinancialAdvertiserInfo;
import cn.taken.ad.core.dto.web.oper.financial.advertiser.FinancialAdvertiserInfoReq;
import cn.taken.ad.core.dto.web.oper.financial.advertiser.FinancialAdvertiserModifyReq;
import cn.taken.ad.core.dto.web.oper.financial.advertiser.FinancialAdvertiserPageReq;
import cn.taken.ad.core.dto.web.oper.financial.merge.FinancialMediaAdvTaskReq;
import cn.taken.ad.core.pojo.financial.FinancialAdvertiser;
import cn.taken.ad.core.service.financial.FinancialAdvertiserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FinancialAdvertiserServiceImpl implements FinancialAdvertiserService {

    @Resource
    private FinancialAdvertiserDao advertiserFinancialReportDao;


    @Override
    public Page<FinancialAdvertiserInfo> findPage(FinancialAdvertiserPageReq req) {
        Page<FinancialAdvertiserInfo> page=advertiserFinancialReportDao.page(req);
        page.getList().forEach(FinancialAdvertiserInfo::fillValues);
        return page;
    }

    @Override
    public void saveBatch(List<FinancialAdvertiser> financialAdvertisers) {
        advertiserFinancialReportDao.saveBatch(financialAdvertisers);
    }

    @Override
    public void updateBatch(List<FinancialAdvertiser> financialAdvertisers) {
        advertiserFinancialReportDao.updateBatch(financialAdvertisers);
    }

    @Override
    public void updateOne(FinancialAdvertiser financialAdvertiser) {
        advertiserFinancialReportDao.update(financialAdvertiser);
    }

    @Override
    public List<FinancialAdvertiserInfo> findExport(String beginTime, String endTime, Long advertiserId, Long advertiserAppId, Long advertiserTagId,Integer state,Integer settlementType) {
        List<FinancialAdvertiserInfo> infos = advertiserFinancialReportDao.findExport(beginTime,endTime,advertiserId,advertiserAppId,advertiserTagId,state,settlementType);
        infos.forEach(item->{
            item.fillValues();
            item.setAdvertiserAppTypeStr(OsType.findByType(item.getAdvertiserAppType()).getName());
            item.setAdvertiserTagTypeStr(TagType.findByType(item.getAdvertiserTagType()).getDesc());
            BidType bidType = BidType.findByType(item.getAdvertiserTagSettlementType());
            if (null != bidType){
                item.setAdvertiserTagSettlementTypeStr(bidType.getName());
            }
            if(item.getRealAmount()!=null&&item.getRealExposureTotal()!=null&&item.getRealExposureTotal() > 0){
                item.setEcPm(BigDecimalUtils.div(item.getRealAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(item.getRealExposureTotal()), 4));
            }else{
                item.setEcPm(BigDecimal.ZERO);
            }
        });
        return infos;
    }

    @Override
    public void updateBatchForImport(List<FinancialAdvertiser> list) {
        advertiserFinancialReportDao.updateBatchForImport(list);
    }

    @Override
    public List<FinancialAdvertiserInfo> updateAndFindForTask(FinancialMediaAdvTaskReq taskReq) {
        List<FinancialAdvertiserInfo> datas = advertiserFinancialReportDao.findForTask(taskReq);
        if (null != datas && !datas.isEmpty()){
            List<Long> ids = datas.stream().map(FinancialAdvertiserInfo::getId).collect(Collectors.toList());
            advertiserFinancialReportDao.updateStateByIds(ids, FinancialState.CALCULATING.getType());
        }
        return datas;
    }

    @Override
    public SuperResult<String> modify(FinancialAdvertiserModifyReq req) {
        FinancialAdvertiser financialAdvertiser = advertiserFinancialReportDao.findById(req.getId());
        if (null == financialAdvertiser){
            return SuperResult.badResult("记录不存在");
        }
        financialAdvertiser.setRealParticipatingTotal(req.getRealParticipatingTotal());
        financialAdvertiser.setRealExposureTotal(req.getRealExposureTotal());
        financialAdvertiser.setRealClickTotal(req.getRealClickTotal());
        financialAdvertiser.setRealAmount(req.getRealAmount());
        advertiserFinancialReportDao.update(financialAdvertiser);
        return SuperResult.rightResult();
    }

    @Override
    public FinancialAdvertiserInfo findInfo(FinancialAdvertiserInfoReq req) {
        FinancialAdvertiserInfo info = advertiserFinancialReportDao.findInfo(req);
        if (null != info){
            info.fillValues();
        }
        return info;
    }
}
