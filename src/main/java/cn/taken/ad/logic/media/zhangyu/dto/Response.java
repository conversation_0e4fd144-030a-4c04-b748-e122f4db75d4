// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: zy_adx.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.media.zhangyu.dto;

/**
 * Protobuf type {@code Response}
 */
public final class Response extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Response)
    ResponseOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      Response.class.getName());
  }
  // Use Response.newBuilder() to construct.
  private Response(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private Response() {
    id_ = "";
    seat_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.taken.ad.logic.media.zhangyu.dto.Response.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Builder.class);
  }

  public interface SeatOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Response.Seat)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 指定请求里的impression id
     * </pre>
     *
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    boolean hasId();
    /**
     * <pre>
     * 指定请求里的impression id
     * </pre>
     *
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad> 
        getAdList();
    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad getAd(int index);
    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    int getAdCount();
    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder> 
        getAdOrBuilderList();
    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder getAdOrBuilder(
        int index);
  }
  /**
   * <pre>
   * 一个位置上的广告
   * </pre>
   *
   * Protobuf type {@code Response.Seat}
   */
  public static final class Seat extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Response.Seat)
      SeatOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        Seat.class.getName());
    }
    // Use Seat.newBuilder() to construct.
    private Seat(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Seat() {
      ad_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder.class);
    }

    public interface AdOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Response.Seat.Ad)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 广告序号，为0
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return Whether the id field is set.
       */
      boolean hasId();
      /**
       * <pre>
       * 广告序号，为0
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return The id.
       */
      int getId();

      /**
       * <pre>
       * 创意类型
       * 1 文字 2 图片 3 Flash 4 视频
       * </pre>
       *
       * <code>optional int32 creative_type = 3;</code>
       * @return Whether the creativeType field is set.
       */
      boolean hasCreativeType();
      /**
       * <pre>
       * 创意类型
       * 1 文字 2 图片 3 Flash 4 视频
       * </pre>
       *
       * <code>optional int32 creative_type = 3;</code>
       * @return The creativeType.
       */
      int getCreativeType();

      /**
       * <pre>
       * 展现反馈地址
       * </pre>
       *
       * <code>repeated string impression_tracking_url = 6;</code>
       * @return A list containing the impressionTrackingUrl.
       */
      java.util.List<java.lang.String>
          getImpressionTrackingUrlList();
      /**
       * <pre>
       * 展现反馈地址
       * </pre>
       *
       * <code>repeated string impression_tracking_url = 6;</code>
       * @return The count of impressionTrackingUrl.
       */
      int getImpressionTrackingUrlCount();
      /**
       * <pre>
       * 展现反馈地址
       * </pre>
       *
       * <code>repeated string impression_tracking_url = 6;</code>
       * @param index The index of the element to return.
       * @return The impressionTrackingUrl at the given index.
       */
      java.lang.String getImpressionTrackingUrl(int index);
      /**
       * <pre>
       * 展现反馈地址
       * </pre>
       *
       * <code>repeated string impression_tracking_url = 6;</code>
       * @param index The index of the value to return.
       * @return The bytes of the impressionTrackingUrl at the given index.
       */
      com.google.protobuf.ByteString
          getImpressionTrackingUrlBytes(int index);

      /**
       * <pre>
       * 点击跳转地址(落地页)
       * </pre>
       *
       * <code>optional string click_through_url = 7;</code>
       * @return Whether the clickThroughUrl field is set.
       */
      boolean hasClickThroughUrl();
      /**
       * <pre>
       * 点击跳转地址(落地页)
       * </pre>
       *
       * <code>optional string click_through_url = 7;</code>
       * @return The clickThroughUrl.
       */
      java.lang.String getClickThroughUrl();
      /**
       * <pre>
       * 点击跳转地址(落地页)
       * </pre>
       *
       * <code>optional string click_through_url = 7;</code>
       * @return The bytes for clickThroughUrl.
       */
      com.google.protobuf.ByteString
          getClickThroughUrlBytes();

      /**
       * <pre>
       * 点击跟踪地址
       * </pre>
       *
       * <code>repeated string click_tracking_url = 8;</code>
       * @return A list containing the clickTrackingUrl.
       */
      java.util.List<java.lang.String>
          getClickTrackingUrlList();
      /**
       * <pre>
       * 点击跟踪地址
       * </pre>
       *
       * <code>repeated string click_tracking_url = 8;</code>
       * @return The count of clickTrackingUrl.
       */
      int getClickTrackingUrlCount();
      /**
       * <pre>
       * 点击跟踪地址
       * </pre>
       *
       * <code>repeated string click_tracking_url = 8;</code>
       * @param index The index of the element to return.
       * @return The clickTrackingUrl at the given index.
       */
      java.lang.String getClickTrackingUrl(int index);
      /**
       * <pre>
       * 点击跟踪地址
       * </pre>
       *
       * <code>repeated string click_tracking_url = 8;</code>
       * @param index The index of the value to return.
       * @return The bytes of the clickTrackingUrl at the given index.
       */
      com.google.protobuf.ByteString
          getClickTrackingUrlBytes(int index);

      /**
       * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
       * @return Whether the nativeAd field is set.
       */
      boolean hasNativeAd();
      /**
       * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
       * @return The nativeAd.
       */
      cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd getNativeAd();
      /**
       * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
       */
      cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAdOrBuilder getNativeAdOrBuilder();

      /**
       * <pre>
       * 广告创意的唯一标识
       * </pre>
       *
       * <code>optional string creative_id = 11;</code>
       * @return Whether the creativeId field is set.
       */
      boolean hasCreativeId();
      /**
       * <pre>
       * 广告创意的唯一标识
       * </pre>
       *
       * <code>optional string creative_id = 11;</code>
       * @return The creativeId.
       */
      java.lang.String getCreativeId();
      /**
       * <pre>
       * 广告创意的唯一标识
       * </pre>
       *
       * <code>optional string creative_id = 11;</code>
       * @return The bytes for creativeId.
       */
      com.google.protobuf.ByteString
          getCreativeIdBytes();

      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>optional string ad_source = 12;</code>
       * @return Whether the adSource field is set.
       */
      boolean hasAdSource();
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>optional string ad_source = 12;</code>
       * @return The adSource.
       */
      java.lang.String getAdSource();
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>optional string ad_source = 12;</code>
       * @return The bytes for adSource.
       */
      com.google.protobuf.ByteString
          getAdSourceBytes();

      /**
       * <pre>
       * APP唤醒地址
       * </pre>
       *
       * <code>optional string deeplink_url = 13;</code>
       * @return Whether the deeplinkUrl field is set.
       */
      boolean hasDeeplinkUrl();
      /**
       * <pre>
       * APP唤醒地址
       * </pre>
       *
       * <code>optional string deeplink_url = 13;</code>
       * @return The deeplinkUrl.
       */
      java.lang.String getDeeplinkUrl();
      /**
       * <pre>
       * APP唤醒地址
       * </pre>
       *
       * <code>optional string deeplink_url = 13;</code>
       * @return The bytes for deeplinkUrl.
       */
      com.google.protobuf.ByteString
          getDeeplinkUrlBytes();

      /**
       * <pre>
       * APP下载地址
       * </pre>
       *
       * <code>optional string download_url = 14;</code>
       * @return Whether the downloadUrl field is set.
       */
      boolean hasDownloadUrl();
      /**
       * <pre>
       * APP下载地址
       * </pre>
       *
       * <code>optional string download_url = 14;</code>
       * @return The downloadUrl.
       */
      java.lang.String getDownloadUrl();
      /**
       * <pre>
       * APP下载地址
       * </pre>
       *
       * <code>optional string download_url = 14;</code>
       * @return The bytes for downloadUrl.
       */
      com.google.protobuf.ByteString
          getDownloadUrlBytes();

      /**
       * <pre>
       * 返回报价供上游adx竞价, 单位(分)
       * </pre>
       *
       * <code>optional int32 bid_price = 15;</code>
       * @return Whether the bidPrice field is set.
       */
      boolean hasBidPrice();
      /**
       * <pre>
       * 返回报价供上游adx竞价, 单位(分)
       * </pre>
       *
       * <code>optional int32 bid_price = 15;</code>
       * @return The bidPrice.
       */
      int getBidPrice();

      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack> 
          getEventTrackList();
      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack getEventTrack(int index);
      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      int getEventTrackCount();
      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder> 
          getEventTrackOrBuilderList();
      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder getEventTrackOrBuilder(
          int index);

      /**
       * <pre>
       * 落地页打开方式：1是打开网页(包含deeplink) 2 点击下载
       * </pre>
       *
       * <code>optional int32 open_type = 17;</code>
       * @return Whether the openType field is set.
       */
      boolean hasOpenType();
      /**
       * <pre>
       * 落地页打开方式：1是打开网页(包含deeplink) 2 点击下载
       * </pre>
       *
       * <code>optional int32 open_type = 17;</code>
       * @return The openType.
       */
      int getOpenType();

      /**
       * <pre>
       * 竞价成功通知，服务端发送
       * </pre>
       *
       * <code>optional string winnotice_url = 18;</code>
       * @return Whether the winnoticeUrl field is set.
       */
      boolean hasWinnoticeUrl();
      /**
       * <pre>
       * 竞价成功通知，服务端发送
       * </pre>
       *
       * <code>optional string winnotice_url = 18;</code>
       * @return The winnoticeUrl.
       */
      java.lang.String getWinnoticeUrl();
      /**
       * <pre>
       * 竞价成功通知，服务端发送
       * </pre>
       *
       * <code>optional string winnotice_url = 18;</code>
       * @return The bytes for winnoticeUrl.
       */
      com.google.protobuf.ByteString
          getWinnoticeUrlBytes();

      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>optional string package_name = 19;</code>
       * @return Whether the packageName field is set.
       */
      boolean hasPackageName();
      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>optional string package_name = 19;</code>
       * @return The packageName.
       */
      java.lang.String getPackageName();
      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>optional string package_name = 19;</code>
       * @return The bytes for packageName.
       */
      com.google.protobuf.ByteString
          getPackageNameBytes();

      /**
       * <pre>
       * 应用名
       * </pre>
       *
       * <code>optional string app_name = 20;</code>
       * @return Whether the appName field is set.
       */
      boolean hasAppName();
      /**
       * <pre>
       * 应用名
       * </pre>
       *
       * <code>optional string app_name = 20;</code>
       * @return The appName.
       */
      java.lang.String getAppName();
      /**
       * <pre>
       * 应用名
       * </pre>
       *
       * <code>optional string app_name = 20;</code>
       * @return The bytes for appName.
       */
      com.google.protobuf.ByteString
          getAppNameBytes();

      /**
       * <pre>
       * *
       * 下载类相关信息
       * </pre>
       *
       * <code>optional string app_desc = 21;</code>
       * @return Whether the appDesc field is set.
       */
      boolean hasAppDesc();
      /**
       * <pre>
       * *
       * 下载类相关信息
       * </pre>
       *
       * <code>optional string app_desc = 21;</code>
       * @return The appDesc.
       */
      java.lang.String getAppDesc();
      /**
       * <pre>
       * *
       * 下载类相关信息
       * </pre>
       *
       * <code>optional string app_desc = 21;</code>
       * @return The bytes for appDesc.
       */
      com.google.protobuf.ByteString
          getAppDescBytes();

      /**
       * <pre>
       * 下载地址
       * </pre>
       *
       * <code>optional string app_download_url = 22;</code>
       * @return Whether the appDownloadUrl field is set.
       */
      boolean hasAppDownloadUrl();
      /**
       * <pre>
       * 下载地址
       * </pre>
       *
       * <code>optional string app_download_url = 22;</code>
       * @return The appDownloadUrl.
       */
      java.lang.String getAppDownloadUrl();
      /**
       * <pre>
       * 下载地址
       * </pre>
       *
       * <code>optional string app_download_url = 22;</code>
       * @return The bytes for appDownloadUrl.
       */
      com.google.protobuf.ByteString
          getAppDownloadUrlBytes();

      /**
       * <pre>
       * 权限名称及权限描述列表
       * </pre>
       *
       * <code>optional string permissions_url = 23;</code>
       * @return Whether the permissionsUrl field is set.
       */
      boolean hasPermissionsUrl();
      /**
       * <pre>
       * 权限名称及权限描述列表
       * </pre>
       *
       * <code>optional string permissions_url = 23;</code>
       * @return The permissionsUrl.
       */
      java.lang.String getPermissionsUrl();
      /**
       * <pre>
       * 权限名称及权限描述列表
       * </pre>
       *
       * <code>optional string permissions_url = 23;</code>
       * @return The bytes for permissionsUrl.
       */
      com.google.protobuf.ByteString
          getPermissionsUrlBytes();

      /**
       * <pre>
       * 产品功能url
       * </pre>
       *
       * <code>optional string function_desc_url = 24;</code>
       * @return Whether the functionDescUrl field is set.
       */
      boolean hasFunctionDescUrl();
      /**
       * <pre>
       * 产品功能url
       * </pre>
       *
       * <code>optional string function_desc_url = 24;</code>
       * @return The functionDescUrl.
       */
      java.lang.String getFunctionDescUrl();
      /**
       * <pre>
       * 产品功能url
       * </pre>
       *
       * <code>optional string function_desc_url = 24;</code>
       * @return The bytes for functionDescUrl.
       */
      com.google.protobuf.ByteString
          getFunctionDescUrlBytes();

      /**
       * <pre>
       * 隐私协议
       * </pre>
       *
       * <code>optional string privacy_url = 25;</code>
       * @return Whether the privacyUrl field is set.
       */
      boolean hasPrivacyUrl();
      /**
       * <pre>
       * 隐私协议
       * </pre>
       *
       * <code>optional string privacy_url = 25;</code>
       * @return The privacyUrl.
       */
      java.lang.String getPrivacyUrl();
      /**
       * <pre>
       * 隐私协议
       * </pre>
       *
       * <code>optional string privacy_url = 25;</code>
       * @return The bytes for privacyUrl.
       */
      com.google.protobuf.ByteString
          getPrivacyUrlBytes();

      /**
       * <pre>
       * 开发者公司名称
       * </pre>
       *
       * <code>optional string developer_name = 26;</code>
       * @return Whether the developerName field is set.
       */
      boolean hasDeveloperName();
      /**
       * <pre>
       * 开发者公司名称
       * </pre>
       *
       * <code>optional string developer_name = 26;</code>
       * @return The developerName.
       */
      java.lang.String getDeveloperName();
      /**
       * <pre>
       * 开发者公司名称
       * </pre>
       *
       * <code>optional string developer_name = 26;</code>
       * @return The bytes for developerName.
       */
      com.google.protobuf.ByteString
          getDeveloperNameBytes();

      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string app_version = 27;</code>
       * @return Whether the appVersion field is set.
       */
      boolean hasAppVersion();
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string app_version = 27;</code>
       * @return The appVersion.
       */
      java.lang.String getAppVersion();
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string app_version = 27;</code>
       * @return The bytes for appVersion.
       */
      com.google.protobuf.ByteString
          getAppVersionBytes();

      /**
       * <pre>
       * App图标链接
       * </pre>
       *
       * <code>optional string app_icon_url = 28;</code>
       * @return Whether the appIconUrl field is set.
       */
      boolean hasAppIconUrl();
      /**
       * <pre>
       * App图标链接
       * </pre>
       *
       * <code>optional string app_icon_url = 28;</code>
       * @return The appIconUrl.
       */
      java.lang.String getAppIconUrl();
      /**
       * <pre>
       * App图标链接
       * </pre>
       *
       * <code>optional string app_icon_url = 28;</code>
       * @return The bytes for appIconUrl.
       */
      com.google.protobuf.ByteString
          getAppIconUrlBytes();

      /**
       * <pre>
       * app大小
       * </pre>
       *
       * <code>optional int64 app_size = 29;</code>
       * @return Whether the appSize field is set.
       */
      boolean hasAppSize();
      /**
       * <pre>
       * app大小
       * </pre>
       *
       * <code>optional int64 app_size = 29;</code>
       * @return The appSize.
       */
      long getAppSize();

      /**
       * <pre>
       * app文件md5
       * </pre>
       *
       * <code>optional string file_md5 = 30;</code>
       * @return Whether the fileMd5 field is set.
       */
      boolean hasFileMd5();
      /**
       * <pre>
       * app文件md5
       * </pre>
       *
       * <code>optional string file_md5 = 30;</code>
       * @return The fileMd5.
       */
      java.lang.String getFileMd5();
      /**
       * <pre>
       * app文件md5
       * </pre>
       *
       * <code>optional string file_md5 = 30;</code>
       * @return The bytes for fileMd5.
       */
      com.google.protobuf.ByteString
          getFileMd5Bytes();
    }
    /**
     * <pre>
     * 广告字段
     * </pre>
     *
     * Protobuf type {@code Response.Seat.Ad}
     */
    public static final class Ad extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:Response.Seat.Ad)
        AdOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 28,
          /* patch= */ 3,
          /* suffix= */ "",
          Ad.class.getName());
      }
      // Use Ad.newBuilder() to construct.
      private Ad(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private Ad() {
        impressionTrackingUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        clickThroughUrl_ = "";
        clickTrackingUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        creativeId_ = "";
        adSource_ = "";
        deeplinkUrl_ = "";
        downloadUrl_ = "";
        eventTrack_ = java.util.Collections.emptyList();
        winnoticeUrl_ = "";
        packageName_ = "";
        appName_ = "";
        appDesc_ = "";
        appDownloadUrl_ = "";
        permissionsUrl_ = "";
        functionDescUrl_ = "";
        privacyUrl_ = "";
        developerName_ = "";
        appVersion_ = "";
        appIconUrl_ = "";
        fileMd5_ = "";
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder.class);
      }

      public interface NativeAdOrBuilder extends
          // @@protoc_insertion_point(interface_extends:Response.Seat.Ad.NativeAd)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr> 
            getAttrList();
        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr getAttr(int index);
        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        int getAttrCount();
        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder> 
            getAttrOrBuilderList();
        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder getAttrOrBuilder(
            int index);
      }
      /**
       * Protobuf type {@code Response.Seat.Ad.NativeAd}
       */
      public static final class NativeAd extends
          com.google.protobuf.GeneratedMessage implements
          // @@protoc_insertion_point(message_implements:Response.Seat.Ad.NativeAd)
          NativeAdOrBuilder {
      private static final long serialVersionUID = 0L;
        static {
          com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
            com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
            /* major= */ 4,
            /* minor= */ 28,
            /* patch= */ 3,
            /* suffix= */ "",
            NativeAd.class.getName());
        }
        // Use NativeAd.newBuilder() to construct.
        private NativeAd(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
          super(builder);
        }
        private NativeAd() {
          attr_ = java.util.Collections.emptyList();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Builder.class);
        }

        public interface AttrOrBuilder extends
            // @@protoc_insertion_point(interface_extends:Response.Seat.Ad.NativeAd.Attr)
            com.google.protobuf.MessageOrBuilder {

          /**
           * <pre>
           * 属性名
           * </pre>
           *
           * <code>optional string name = 1;</code>
           * @return Whether the name field is set.
           */
          boolean hasName();
          /**
           * <pre>
           * 属性名
           * </pre>
           *
           * <code>optional string name = 1;</code>
           * @return The name.
           */
          java.lang.String getName();
          /**
           * <pre>
           * 属性名
           * </pre>
           *
           * <code>optional string name = 1;</code>
           * @return The bytes for name.
           */
          com.google.protobuf.ByteString
              getNameBytes();

          /**
           * <pre>
           * 属性值
           * </pre>
           *
           * <code>optional string value = 2;</code>
           * @return Whether the value field is set.
           */
          boolean hasValue();
          /**
           * <pre>
           * 属性值
           * </pre>
           *
           * <code>optional string value = 2;</code>
           * @return The value.
           */
          java.lang.String getValue();
          /**
           * <pre>
           * 属性值
           * </pre>
           *
           * <code>optional string value = 2;</code>
           * @return The bytes for value.
           */
          com.google.protobuf.ByteString
              getValueBytes();
        }
        /**
         * <pre>
         * 属性（描述）信息
         * </pre>
         *
         * Protobuf type {@code Response.Seat.Ad.NativeAd.Attr}
         */
        public static final class Attr extends
            com.google.protobuf.GeneratedMessage implements
            // @@protoc_insertion_point(message_implements:Response.Seat.Ad.NativeAd.Attr)
            AttrOrBuilder {
        private static final long serialVersionUID = 0L;
          static {
            com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
              com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
              /* major= */ 4,
              /* minor= */ 28,
              /* patch= */ 3,
              /* suffix= */ "",
              Attr.class.getName());
          }
          // Use Attr.newBuilder() to construct.
          private Attr(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
            super(builder);
          }
          private Attr() {
            name_ = "";
            value_ = "";
          }

          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_Attr_descriptor;
          }

          @java.lang.Override
          protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
              internalGetFieldAccessorTable() {
            return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_Attr_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder.class);
          }

          private int bitField0_;
          public static final int NAME_FIELD_NUMBER = 1;
          @SuppressWarnings("serial")
          private volatile java.lang.Object name_ = "";
          /**
           * <pre>
           * 属性名
           * </pre>
           *
           * <code>optional string name = 1;</code>
           * @return Whether the name field is set.
           */
          @java.lang.Override
          public boolean hasName() {
            return ((bitField0_ & 0x00000001) != 0);
          }
          /**
           * <pre>
           * 属性名
           * </pre>
           *
           * <code>optional string name = 1;</code>
           * @return The name.
           */
          @java.lang.Override
          public java.lang.String getName() {
            java.lang.Object ref = name_;
            if (ref instanceof java.lang.String) {
              return (java.lang.String) ref;
            } else {
              com.google.protobuf.ByteString bs = 
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              name_ = s;
              return s;
            }
          }
          /**
           * <pre>
           * 属性名
           * </pre>
           *
           * <code>optional string name = 1;</code>
           * @return The bytes for name.
           */
          @java.lang.Override
          public com.google.protobuf.ByteString
              getNameBytes() {
            java.lang.Object ref = name_;
            if (ref instanceof java.lang.String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              name_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }

          public static final int VALUE_FIELD_NUMBER = 2;
          @SuppressWarnings("serial")
          private volatile java.lang.Object value_ = "";
          /**
           * <pre>
           * 属性值
           * </pre>
           *
           * <code>optional string value = 2;</code>
           * @return Whether the value field is set.
           */
          @java.lang.Override
          public boolean hasValue() {
            return ((bitField0_ & 0x00000002) != 0);
          }
          /**
           * <pre>
           * 属性值
           * </pre>
           *
           * <code>optional string value = 2;</code>
           * @return The value.
           */
          @java.lang.Override
          public java.lang.String getValue() {
            java.lang.Object ref = value_;
            if (ref instanceof java.lang.String) {
              return (java.lang.String) ref;
            } else {
              com.google.protobuf.ByteString bs = 
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              value_ = s;
              return s;
            }
          }
          /**
           * <pre>
           * 属性值
           * </pre>
           *
           * <code>optional string value = 2;</code>
           * @return The bytes for value.
           */
          @java.lang.Override
          public com.google.protobuf.ByteString
              getValueBytes() {
            java.lang.Object ref = value_;
            if (ref instanceof java.lang.String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              value_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }

          private byte memoizedIsInitialized = -1;
          @java.lang.Override
          public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
          }

          @java.lang.Override
          public void writeTo(com.google.protobuf.CodedOutputStream output)
                              throws java.io.IOException {
            if (((bitField0_ & 0x00000001) != 0)) {
              com.google.protobuf.GeneratedMessage.writeString(output, 1, name_);
            }
            if (((bitField0_ & 0x00000002) != 0)) {
              com.google.protobuf.GeneratedMessage.writeString(output, 2, value_);
            }
            getUnknownFields().writeTo(output);
          }

          @java.lang.Override
          public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (((bitField0_ & 0x00000001) != 0)) {
              size += com.google.protobuf.GeneratedMessage.computeStringSize(1, name_);
            }
            if (((bitField0_ & 0x00000002) != 0)) {
              size += com.google.protobuf.GeneratedMessage.computeStringSize(2, value_);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
          }

          @java.lang.Override
          public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
             return true;
            }
            if (!(obj instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr)) {
              return super.equals(obj);
            }
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr other = (cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr) obj;

            if (hasName() != other.hasName()) return false;
            if (hasName()) {
              if (!getName()
                  .equals(other.getName())) return false;
            }
            if (hasValue() != other.hasValue()) return false;
            if (hasValue()) {
              if (!getValue()
                  .equals(other.getValue())) return false;
            }
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
          }

          @java.lang.Override
          public int hashCode() {
            if (memoizedHashCode != 0) {
              return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            if (hasName()) {
              hash = (37 * hash) + NAME_FIELD_NUMBER;
              hash = (53 * hash) + getName().hashCode();
            }
            if (hasValue()) {
              hash = (37 * hash) + VALUE_FIELD_NUMBER;
              hash = (53 * hash) + getValue().hashCode();
            }
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
          }

          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(
              java.nio.ByteBuffer data)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
          }
          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(
              java.nio.ByteBuffer data,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
          }
          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(
              com.google.protobuf.ByteString data)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
          }
          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(
              com.google.protobuf.ByteString data,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
          }
          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(byte[] data)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
          }
          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(
              byte[] data,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
          }
          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(java.io.InputStream input)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessage
                .parseWithIOException(PARSER, input);
          }
          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(
              java.io.InputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessage
                .parseWithIOException(PARSER, input, extensionRegistry);
          }

          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseDelimitedFrom(java.io.InputStream input)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessage
                .parseDelimitedWithIOException(PARSER, input);
          }

          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseDelimitedFrom(
              java.io.InputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessage
                .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
          }
          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(
              com.google.protobuf.CodedInputStream input)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessage
                .parseWithIOException(PARSER, input);
          }
          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr parseFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessage
                .parseWithIOException(PARSER, input, extensionRegistry);
          }

          @java.lang.Override
          public Builder newBuilderForType() { return newBuilder(); }
          public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
          }
          public static Builder newBuilder(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
          }
          @java.lang.Override
          public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                ? new Builder() : new Builder().mergeFrom(this);
          }

          @java.lang.Override
          protected Builder newBuilderForType(
              com.google.protobuf.GeneratedMessage.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
          }
          /**
           * <pre>
           * 属性（描述）信息
           * </pre>
           *
           * Protobuf type {@code Response.Seat.Ad.NativeAd.Attr}
           */
          public static final class Builder extends
              com.google.protobuf.GeneratedMessage.Builder<Builder> implements
              // @@protoc_insertion_point(builder_implements:Response.Seat.Ad.NativeAd.Attr)
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
                getDescriptor() {
              return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_Attr_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
                internalGetFieldAccessorTable() {
              return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_Attr_fieldAccessorTable
                  .ensureFieldAccessorsInitialized(
                      cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder.class);
            }

            // Construct using cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.newBuilder()
            private Builder() {

            }

            private Builder(
                com.google.protobuf.GeneratedMessage.BuilderParent parent) {
              super(parent);

            }
            @java.lang.Override
            public Builder clear() {
              super.clear();
              bitField0_ = 0;
              name_ = "";
              value_ = "";
              return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
                getDescriptorForType() {
              return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_Attr_descriptor;
            }

            @java.lang.Override
            public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr getDefaultInstanceForType() {
              return cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.getDefaultInstance();
            }

            @java.lang.Override
            public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr build() {
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr result = buildPartial();
              if (!result.isInitialized()) {
                throw newUninitializedMessageException(result);
              }
              return result;
            }

            @java.lang.Override
            public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr buildPartial() {
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr result = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr(this);
              if (bitField0_ != 0) { buildPartial0(result); }
              onBuilt();
              return result;
            }

            private void buildPartial0(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr result) {
              int from_bitField0_ = bitField0_;
              int to_bitField0_ = 0;
              if (((from_bitField0_ & 0x00000001) != 0)) {
                result.name_ = name_;
                to_bitField0_ |= 0x00000001;
              }
              if (((from_bitField0_ & 0x00000002) != 0)) {
                result.value_ = value_;
                to_bitField0_ |= 0x00000002;
              }
              result.bitField0_ |= to_bitField0_;
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
              if (other instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr) {
                return mergeFrom((cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr)other);
              } else {
                super.mergeFrom(other);
                return this;
              }
            }

            public Builder mergeFrom(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr other) {
              if (other == cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.getDefaultInstance()) return this;
              if (other.hasName()) {
                name_ = other.name_;
                bitField0_ |= 0x00000001;
                onChanged();
              }
              if (other.hasValue()) {
                value_ = other.value_;
                bitField0_ |= 0x00000002;
                onChanged();
              }
              this.mergeUnknownFields(other.getUnknownFields());
              onChanged();
              return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
              return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
              if (extensionRegistry == null) {
                throw new java.lang.NullPointerException();
              }
              try {
                boolean done = false;
                while (!done) {
                  int tag = input.readTag();
                  switch (tag) {
                    case 0:
                      done = true;
                      break;
                    case 10: {
                      name_ = input.readStringRequireUtf8();
                      bitField0_ |= 0x00000001;
                      break;
                    } // case 10
                    case 18: {
                      value_ = input.readStringRequireUtf8();
                      bitField0_ |= 0x00000002;
                      break;
                    } // case 18
                    default: {
                      if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                        done = true; // was an endgroup tag
                      }
                      break;
                    } // default:
                  } // switch (tag)
                } // while (!done)
              } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.unwrapIOException();
              } finally {
                onChanged();
              } // finally
              return this;
            }
            private int bitField0_;

            private java.lang.Object name_ = "";
            /**
             * <pre>
             * 属性名
             * </pre>
             *
             * <code>optional string name = 1;</code>
             * @return Whether the name field is set.
             */
            public boolean hasName() {
              return ((bitField0_ & 0x00000001) != 0);
            }
            /**
             * <pre>
             * 属性名
             * </pre>
             *
             * <code>optional string name = 1;</code>
             * @return The name.
             */
            public java.lang.String getName() {
              java.lang.Object ref = name_;
              if (!(ref instanceof java.lang.String)) {
                com.google.protobuf.ByteString bs =
                    (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                name_ = s;
                return s;
              } else {
                return (java.lang.String) ref;
              }
            }
            /**
             * <pre>
             * 属性名
             * </pre>
             *
             * <code>optional string name = 1;</code>
             * @return The bytes for name.
             */
            public com.google.protobuf.ByteString
                getNameBytes() {
              java.lang.Object ref = name_;
              if (ref instanceof String) {
                com.google.protobuf.ByteString b = 
                    com.google.protobuf.ByteString.copyFromUtf8(
                        (java.lang.String) ref);
                name_ = b;
                return b;
              } else {
                return (com.google.protobuf.ByteString) ref;
              }
            }
            /**
             * <pre>
             * 属性名
             * </pre>
             *
             * <code>optional string name = 1;</code>
             * @param value The name to set.
             * @return This builder for chaining.
             */
            public Builder setName(
                java.lang.String value) {
              if (value == null) { throw new NullPointerException(); }
              name_ = value;
              bitField0_ |= 0x00000001;
              onChanged();
              return this;
            }
            /**
             * <pre>
             * 属性名
             * </pre>
             *
             * <code>optional string name = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearName() {
              name_ = getDefaultInstance().getName();
              bitField0_ = (bitField0_ & ~0x00000001);
              onChanged();
              return this;
            }
            /**
             * <pre>
             * 属性名
             * </pre>
             *
             * <code>optional string name = 1;</code>
             * @param value The bytes for name to set.
             * @return This builder for chaining.
             */
            public Builder setNameBytes(
                com.google.protobuf.ByteString value) {
              if (value == null) { throw new NullPointerException(); }
              checkByteStringIsUtf8(value);
              name_ = value;
              bitField0_ |= 0x00000001;
              onChanged();
              return this;
            }

            private java.lang.Object value_ = "";
            /**
             * <pre>
             * 属性值
             * </pre>
             *
             * <code>optional string value = 2;</code>
             * @return Whether the value field is set.
             */
            public boolean hasValue() {
              return ((bitField0_ & 0x00000002) != 0);
            }
            /**
             * <pre>
             * 属性值
             * </pre>
             *
             * <code>optional string value = 2;</code>
             * @return The value.
             */
            public java.lang.String getValue() {
              java.lang.Object ref = value_;
              if (!(ref instanceof java.lang.String)) {
                com.google.protobuf.ByteString bs =
                    (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                value_ = s;
                return s;
              } else {
                return (java.lang.String) ref;
              }
            }
            /**
             * <pre>
             * 属性值
             * </pre>
             *
             * <code>optional string value = 2;</code>
             * @return The bytes for value.
             */
            public com.google.protobuf.ByteString
                getValueBytes() {
              java.lang.Object ref = value_;
              if (ref instanceof String) {
                com.google.protobuf.ByteString b = 
                    com.google.protobuf.ByteString.copyFromUtf8(
                        (java.lang.String) ref);
                value_ = b;
                return b;
              } else {
                return (com.google.protobuf.ByteString) ref;
              }
            }
            /**
             * <pre>
             * 属性值
             * </pre>
             *
             * <code>optional string value = 2;</code>
             * @param value The value to set.
             * @return This builder for chaining.
             */
            public Builder setValue(
                java.lang.String value) {
              if (value == null) { throw new NullPointerException(); }
              value_ = value;
              bitField0_ |= 0x00000002;
              onChanged();
              return this;
            }
            /**
             * <pre>
             * 属性值
             * </pre>
             *
             * <code>optional string value = 2;</code>
             * @return This builder for chaining.
             */
            public Builder clearValue() {
              value_ = getDefaultInstance().getValue();
              bitField0_ = (bitField0_ & ~0x00000002);
              onChanged();
              return this;
            }
            /**
             * <pre>
             * 属性值
             * </pre>
             *
             * <code>optional string value = 2;</code>
             * @param value The bytes for value to set.
             * @return This builder for chaining.
             */
            public Builder setValueBytes(
                com.google.protobuf.ByteString value) {
              if (value == null) { throw new NullPointerException(); }
              checkByteStringIsUtf8(value);
              value_ = value;
              bitField0_ |= 0x00000002;
              onChanged();
              return this;
            }

            // @@protoc_insertion_point(builder_scope:Response.Seat.Ad.NativeAd.Attr)
          }

          // @@protoc_insertion_point(class_scope:Response.Seat.Ad.NativeAd.Attr)
          private static final cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr DEFAULT_INSTANCE;
          static {
            DEFAULT_INSTANCE = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr();
          }

          public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr getDefaultInstance() {
            return DEFAULT_INSTANCE;
          }

          private static final com.google.protobuf.Parser<Attr>
              PARSER = new com.google.protobuf.AbstractParser<Attr>() {
            @java.lang.Override
            public Attr parsePartialFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
              Builder builder = newBuilder();
              try {
                builder.mergeFrom(input, extensionRegistry);
              } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(builder.buildPartial());
              } catch (com.google.protobuf.UninitializedMessageException e) {
                throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
              } catch (java.io.IOException e) {
                throw new com.google.protobuf.InvalidProtocolBufferException(e)
                    .setUnfinishedMessage(builder.buildPartial());
              }
              return builder.buildPartial();
            }
          };

          public static com.google.protobuf.Parser<Attr> parser() {
            return PARSER;
          }

          @java.lang.Override
          public com.google.protobuf.Parser<Attr> getParserForType() {
            return PARSER;
          }

          @java.lang.Override
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
          }

        }

        public static final int ATTR_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr> attr_;
        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        @java.lang.Override
        public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr> getAttrList() {
          return attr_;
        }
        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        @java.lang.Override
        public java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder> 
            getAttrOrBuilderList() {
          return attr_;
        }
        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        @java.lang.Override
        public int getAttrCount() {
          return attr_.size();
        }
        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        @java.lang.Override
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr getAttr(int index) {
          return attr_.get(index);
        }
        /**
         * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
         */
        @java.lang.Override
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder getAttrOrBuilder(
            int index) {
          return attr_.get(index);
        }

        private byte memoizedIsInitialized = -1;
        @java.lang.Override
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          memoizedIsInitialized = 1;
          return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          for (int i = 0; i < attr_.size(); i++) {
            output.writeMessage(1, attr_.get(i));
          }
          getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          for (int i = 0; i < attr_.size(); i++) {
            size += com.google.protobuf.CodedOutputStream
              .computeMessageSize(1, attr_.get(i));
          }
          size += getUnknownFields().getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd)) {
            return super.equals(obj);
          }
          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd other = (cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd) obj;

          if (!getAttrList()
              .equals(other.getAttrList())) return false;
          if (!getUnknownFields().equals(other.getUnknownFields())) return false;
          return true;
        }

        @java.lang.Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          if (getAttrCount() > 0) {
            hash = (37 * hash) + ATTR_FIELD_NUMBER;
            hash = (53 * hash) + getAttrList().hashCode();
          }
          hash = (29 * hash) + getUnknownFields().hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input);
        }

        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @java.lang.Override
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * Protobuf type {@code Response.Seat.Ad.NativeAd}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessage.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:Response.Seat.Ad.NativeAd)
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAdOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_descriptor;
          }

          @java.lang.Override
          protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
              internalGetFieldAccessorTable() {
            return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Builder.class);
          }

          // Construct using cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.newBuilder()
          private Builder() {

          }

          private Builder(
              com.google.protobuf.GeneratedMessage.BuilderParent parent) {
            super(parent);

          }
          @java.lang.Override
          public Builder clear() {
            super.clear();
            bitField0_ = 0;
            if (attrBuilder_ == null) {
              attr_ = java.util.Collections.emptyList();
            } else {
              attr_ = null;
              attrBuilder_.clear();
            }
            bitField0_ = (bitField0_ & ~0x00000001);
            return this;
          }

          @java.lang.Override
          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_NativeAd_descriptor;
          }

          @java.lang.Override
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd getDefaultInstanceForType() {
            return cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.getDefaultInstance();
          }

          @java.lang.Override
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd build() {
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          @java.lang.Override
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd buildPartial() {
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd result = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd(this);
            buildPartialRepeatedFields(result);
            if (bitField0_ != 0) { buildPartial0(result); }
            onBuilt();
            return result;
          }

          private void buildPartialRepeatedFields(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd result) {
            if (attrBuilder_ == null) {
              if (((bitField0_ & 0x00000001) != 0)) {
                attr_ = java.util.Collections.unmodifiableList(attr_);
                bitField0_ = (bitField0_ & ~0x00000001);
              }
              result.attr_ = attr_;
            } else {
              result.attr_ = attrBuilder_.build();
            }
          }

          private void buildPartial0(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd result) {
            int from_bitField0_ = bitField0_;
          }

          @java.lang.Override
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd) {
              return mergeFrom((cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd other) {
            if (other == cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.getDefaultInstance()) return this;
            if (attrBuilder_ == null) {
              if (!other.attr_.isEmpty()) {
                if (attr_.isEmpty()) {
                  attr_ = other.attr_;
                  bitField0_ = (bitField0_ & ~0x00000001);
                } else {
                  ensureAttrIsMutable();
                  attr_.addAll(other.attr_);
                }
                onChanged();
              }
            } else {
              if (!other.attr_.isEmpty()) {
                if (attrBuilder_.isEmpty()) {
                  attrBuilder_.dispose();
                  attrBuilder_ = null;
                  attr_ = other.attr_;
                  bitField0_ = (bitField0_ & ~0x00000001);
                  attrBuilder_ = 
                    com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                       getAttrFieldBuilder() : null;
                } else {
                  attrBuilder_.addAllMessages(other.attr_);
                }
              }
            }
            this.mergeUnknownFields(other.getUnknownFields());
            onChanged();
            return this;
          }

          @java.lang.Override
          public final boolean isInitialized() {
            return true;
          }

          @java.lang.Override
          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            if (extensionRegistry == null) {
              throw new java.lang.NullPointerException();
            }
            try {
              boolean done = false;
              while (!done) {
                int tag = input.readTag();
                switch (tag) {
                  case 0:
                    done = true;
                    break;
                  case 10: {
                    cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr m =
                        input.readMessage(
                            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.parser(),
                            extensionRegistry);
                    if (attrBuilder_ == null) {
                      ensureAttrIsMutable();
                      attr_.add(m);
                    } else {
                      attrBuilder_.addMessage(m);
                    }
                    break;
                  } // case 10
                  default: {
                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                      done = true; // was an endgroup tag
                    }
                    break;
                  } // default:
                } // switch (tag)
              } // while (!done)
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.unwrapIOException();
            } finally {
              onChanged();
            } // finally
            return this;
          }
          private int bitField0_;

          private java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr> attr_ =
            java.util.Collections.emptyList();
          private void ensureAttrIsMutable() {
            if (!((bitField0_ & 0x00000001) != 0)) {
              attr_ = new java.util.ArrayList<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr>(attr_);
              bitField0_ |= 0x00000001;
             }
          }

          private com.google.protobuf.RepeatedFieldBuilder<
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder> attrBuilder_;

          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr> getAttrList() {
            if (attrBuilder_ == null) {
              return java.util.Collections.unmodifiableList(attr_);
            } else {
              return attrBuilder_.getMessageList();
            }
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public int getAttrCount() {
            if (attrBuilder_ == null) {
              return attr_.size();
            } else {
              return attrBuilder_.getCount();
            }
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr getAttr(int index) {
            if (attrBuilder_ == null) {
              return attr_.get(index);
            } else {
              return attrBuilder_.getMessage(index);
            }
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public Builder setAttr(
              int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr value) {
            if (attrBuilder_ == null) {
              if (value == null) {
                throw new NullPointerException();
              }
              ensureAttrIsMutable();
              attr_.set(index, value);
              onChanged();
            } else {
              attrBuilder_.setMessage(index, value);
            }
            return this;
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public Builder setAttr(
              int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder builderForValue) {
            if (attrBuilder_ == null) {
              ensureAttrIsMutable();
              attr_.set(index, builderForValue.build());
              onChanged();
            } else {
              attrBuilder_.setMessage(index, builderForValue.build());
            }
            return this;
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public Builder addAttr(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr value) {
            if (attrBuilder_ == null) {
              if (value == null) {
                throw new NullPointerException();
              }
              ensureAttrIsMutable();
              attr_.add(value);
              onChanged();
            } else {
              attrBuilder_.addMessage(value);
            }
            return this;
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public Builder addAttr(
              int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr value) {
            if (attrBuilder_ == null) {
              if (value == null) {
                throw new NullPointerException();
              }
              ensureAttrIsMutable();
              attr_.add(index, value);
              onChanged();
            } else {
              attrBuilder_.addMessage(index, value);
            }
            return this;
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public Builder addAttr(
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder builderForValue) {
            if (attrBuilder_ == null) {
              ensureAttrIsMutable();
              attr_.add(builderForValue.build());
              onChanged();
            } else {
              attrBuilder_.addMessage(builderForValue.build());
            }
            return this;
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public Builder addAttr(
              int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder builderForValue) {
            if (attrBuilder_ == null) {
              ensureAttrIsMutable();
              attr_.add(index, builderForValue.build());
              onChanged();
            } else {
              attrBuilder_.addMessage(index, builderForValue.build());
            }
            return this;
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public Builder addAllAttr(
              java.lang.Iterable<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr> values) {
            if (attrBuilder_ == null) {
              ensureAttrIsMutable();
              com.google.protobuf.AbstractMessageLite.Builder.addAll(
                  values, attr_);
              onChanged();
            } else {
              attrBuilder_.addAllMessages(values);
            }
            return this;
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public Builder clearAttr() {
            if (attrBuilder_ == null) {
              attr_ = java.util.Collections.emptyList();
              bitField0_ = (bitField0_ & ~0x00000001);
              onChanged();
            } else {
              attrBuilder_.clear();
            }
            return this;
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public Builder removeAttr(int index) {
            if (attrBuilder_ == null) {
              ensureAttrIsMutable();
              attr_.remove(index);
              onChanged();
            } else {
              attrBuilder_.remove(index);
            }
            return this;
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder getAttrBuilder(
              int index) {
            return getAttrFieldBuilder().getBuilder(index);
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder getAttrOrBuilder(
              int index) {
            if (attrBuilder_ == null) {
              return attr_.get(index);  } else {
              return attrBuilder_.getMessageOrBuilder(index);
            }
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder> 
               getAttrOrBuilderList() {
            if (attrBuilder_ != null) {
              return attrBuilder_.getMessageOrBuilderList();
            } else {
              return java.util.Collections.unmodifiableList(attr_);
            }
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder addAttrBuilder() {
            return getAttrFieldBuilder().addBuilder(
                cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.getDefaultInstance());
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder addAttrBuilder(
              int index) {
            return getAttrFieldBuilder().addBuilder(
                index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.getDefaultInstance());
          }
          /**
           * <code>repeated .Response.Seat.Ad.NativeAd.Attr attr = 1;</code>
           */
          public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder> 
               getAttrBuilderList() {
            return getAttrFieldBuilder().getBuilderList();
          }
          private com.google.protobuf.RepeatedFieldBuilder<
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder> 
              getAttrFieldBuilder() {
            if (attrBuilder_ == null) {
              attrBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
                  cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Attr.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.AttrOrBuilder>(
                      attr_,
                      ((bitField0_ & 0x00000001) != 0),
                      getParentForChildren(),
                      isClean());
              attr_ = null;
            }
            return attrBuilder_;
          }

          // @@protoc_insertion_point(builder_scope:Response.Seat.Ad.NativeAd)
        }

        // @@protoc_insertion_point(class_scope:Response.Seat.Ad.NativeAd)
        private static final cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd();
        }

        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<NativeAd>
            PARSER = new com.google.protobuf.AbstractParser<NativeAd>() {
          @java.lang.Override
          public NativeAd parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            Builder builder = newBuilder();
            try {
              builder.mergeFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.setUnfinishedMessage(builder.buildPartial());
            } catch (com.google.protobuf.UninitializedMessageException e) {
              throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
            } catch (java.io.IOException e) {
              throw new com.google.protobuf.InvalidProtocolBufferException(e)
                  .setUnfinishedMessage(builder.buildPartial());
            }
            return builder.buildPartial();
          }
        };

        public static com.google.protobuf.Parser<NativeAd> parser() {
          return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<NativeAd> getParserForType() {
          return PARSER;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      public interface EventTrackOrBuilder extends
          // @@protoc_insertion_point(interface_extends:Response.Seat.Ad.EventTrack)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <code>optional uint32 type = 1;</code>
         * @return Whether the type field is set.
         */
        boolean hasType();
        /**
         * <code>optional uint32 type = 1;</code>
         * @return The type.
         */
        int getType();

        /**
         * <code>repeated string url = 2;</code>
         * @return A list containing the url.
         */
        java.util.List<java.lang.String>
            getUrlList();
        /**
         * <code>repeated string url = 2;</code>
         * @return The count of url.
         */
        int getUrlCount();
        /**
         * <code>repeated string url = 2;</code>
         * @param index The index of the element to return.
         * @return The url at the given index.
         */
        java.lang.String getUrl(int index);
        /**
         * <code>repeated string url = 2;</code>
         * @param index The index of the value to return.
         * @return The bytes of the url at the given index.
         */
        com.google.protobuf.ByteString
            getUrlBytes(int index);
      }
      /**
       * <pre>
       * 事件监测url
       * </pre>
       *
       * Protobuf type {@code Response.Seat.Ad.EventTrack}
       */
      public static final class EventTrack extends
          com.google.protobuf.GeneratedMessage implements
          // @@protoc_insertion_point(message_implements:Response.Seat.Ad.EventTrack)
          EventTrackOrBuilder {
      private static final long serialVersionUID = 0L;
        static {
          com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
            com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
            /* major= */ 4,
            /* minor= */ 28,
            /* patch= */ 3,
            /* suffix= */ "",
            EventTrack.class.getName());
        }
        // Use EventTrack.newBuilder() to construct.
        private EventTrack(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
          super(builder);
        }
        private EventTrack() {
          url_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_EventTrack_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_EventTrack_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder.class);
        }

        private int bitField0_;
        public static final int TYPE_FIELD_NUMBER = 1;
        private int type_ = 0;
        /**
         * <code>optional uint32 type = 1;</code>
         * @return Whether the type field is set.
         */
        @java.lang.Override
        public boolean hasType() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional uint32 type = 1;</code>
         * @return The type.
         */
        @java.lang.Override
        public int getType() {
          return type_;
        }

        public static final int URL_FIELD_NUMBER = 2;
        @SuppressWarnings("serial")
        private com.google.protobuf.LazyStringArrayList url_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        /**
         * <code>repeated string url = 2;</code>
         * @return A list containing the url.
         */
        public com.google.protobuf.ProtocolStringList
            getUrlList() {
          return url_;
        }
        /**
         * <code>repeated string url = 2;</code>
         * @return The count of url.
         */
        public int getUrlCount() {
          return url_.size();
        }
        /**
         * <code>repeated string url = 2;</code>
         * @param index The index of the element to return.
         * @return The url at the given index.
         */
        public java.lang.String getUrl(int index) {
          return url_.get(index);
        }
        /**
         * <code>repeated string url = 2;</code>
         * @param index The index of the value to return.
         * @return The bytes of the url at the given index.
         */
        public com.google.protobuf.ByteString
            getUrlBytes(int index) {
          return url_.getByteString(index);
        }

        private byte memoizedIsInitialized = -1;
        @java.lang.Override
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          memoizedIsInitialized = 1;
          return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          if (((bitField0_ & 0x00000001) != 0)) {
            output.writeUInt32(1, type_);
          }
          for (int i = 0; i < url_.size(); i++) {
            com.google.protobuf.GeneratedMessage.writeString(output, 2, url_.getRaw(i));
          }
          getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          if (((bitField0_ & 0x00000001) != 0)) {
            size += com.google.protobuf.CodedOutputStream
              .computeUInt32Size(1, type_);
          }
          {
            int dataSize = 0;
            for (int i = 0; i < url_.size(); i++) {
              dataSize += computeStringSizeNoTag(url_.getRaw(i));
            }
            size += dataSize;
            size += 1 * getUrlList().size();
          }
          size += getUnknownFields().getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack)) {
            return super.equals(obj);
          }
          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack other = (cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack) obj;

          if (hasType() != other.hasType()) return false;
          if (hasType()) {
            if (getType()
                != other.getType()) return false;
          }
          if (!getUrlList()
              .equals(other.getUrlList())) return false;
          if (!getUnknownFields().equals(other.getUnknownFields())) return false;
          return true;
        }

        @java.lang.Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          if (hasType()) {
            hash = (37 * hash) + TYPE_FIELD_NUMBER;
            hash = (53 * hash) + getType();
          }
          if (getUrlCount() > 0) {
            hash = (37 * hash) + URL_FIELD_NUMBER;
            hash = (53 * hash) + getUrlList().hashCode();
          }
          hash = (29 * hash) + getUnknownFields().hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input);
        }

        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @java.lang.Override
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * <pre>
         * 事件监测url
         * </pre>
         *
         * Protobuf type {@code Response.Seat.Ad.EventTrack}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessage.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:Response.Seat.Ad.EventTrack)
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_EventTrack_descriptor;
          }

          @java.lang.Override
          protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
              internalGetFieldAccessorTable() {
            return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_EventTrack_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder.class);
          }

          // Construct using cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.newBuilder()
          private Builder() {

          }

          private Builder(
              com.google.protobuf.GeneratedMessage.BuilderParent parent) {
            super(parent);

          }
          @java.lang.Override
          public Builder clear() {
            super.clear();
            bitField0_ = 0;
            type_ = 0;
            url_ =
                com.google.protobuf.LazyStringArrayList.emptyList();
            return this;
          }

          @java.lang.Override
          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_EventTrack_descriptor;
          }

          @java.lang.Override
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack getDefaultInstanceForType() {
            return cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.getDefaultInstance();
          }

          @java.lang.Override
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack build() {
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          @java.lang.Override
          public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack buildPartial() {
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack result = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack(this);
            if (bitField0_ != 0) { buildPartial0(result); }
            onBuilt();
            return result;
          }

          private void buildPartial0(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack result) {
            int from_bitField0_ = bitField0_;
            int to_bitField0_ = 0;
            if (((from_bitField0_ & 0x00000001) != 0)) {
              result.type_ = type_;
              to_bitField0_ |= 0x00000001;
            }
            if (((from_bitField0_ & 0x00000002) != 0)) {
              url_.makeImmutable();
              result.url_ = url_;
            }
            result.bitField0_ |= to_bitField0_;
          }

          @java.lang.Override
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack) {
              return mergeFrom((cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack other) {
            if (other == cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.getDefaultInstance()) return this;
            if (other.hasType()) {
              setType(other.getType());
            }
            if (!other.url_.isEmpty()) {
              if (url_.isEmpty()) {
                url_ = other.url_;
                bitField0_ |= 0x00000002;
              } else {
                ensureUrlIsMutable();
                url_.addAll(other.url_);
              }
              onChanged();
            }
            this.mergeUnknownFields(other.getUnknownFields());
            onChanged();
            return this;
          }

          @java.lang.Override
          public final boolean isInitialized() {
            return true;
          }

          @java.lang.Override
          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            if (extensionRegistry == null) {
              throw new java.lang.NullPointerException();
            }
            try {
              boolean done = false;
              while (!done) {
                int tag = input.readTag();
                switch (tag) {
                  case 0:
                    done = true;
                    break;
                  case 8: {
                    type_ = input.readUInt32();
                    bitField0_ |= 0x00000001;
                    break;
                  } // case 8
                  case 18: {
                    java.lang.String s = input.readStringRequireUtf8();
                    ensureUrlIsMutable();
                    url_.add(s);
                    break;
                  } // case 18
                  default: {
                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                      done = true; // was an endgroup tag
                    }
                    break;
                  } // default:
                } // switch (tag)
              } // while (!done)
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.unwrapIOException();
            } finally {
              onChanged();
            } // finally
            return this;
          }
          private int bitField0_;

          private int type_ ;
          /**
           * <code>optional uint32 type = 1;</code>
           * @return Whether the type field is set.
           */
          @java.lang.Override
          public boolean hasType() {
            return ((bitField0_ & 0x00000001) != 0);
          }
          /**
           * <code>optional uint32 type = 1;</code>
           * @return The type.
           */
          @java.lang.Override
          public int getType() {
            return type_;
          }
          /**
           * <code>optional uint32 type = 1;</code>
           * @param value The type to set.
           * @return This builder for chaining.
           */
          public Builder setType(int value) {

            type_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }
          /**
           * <code>optional uint32 type = 1;</code>
           * @return This builder for chaining.
           */
          public Builder clearType() {
            bitField0_ = (bitField0_ & ~0x00000001);
            type_ = 0;
            onChanged();
            return this;
          }

          private com.google.protobuf.LazyStringArrayList url_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          private void ensureUrlIsMutable() {
            if (!url_.isModifiable()) {
              url_ = new com.google.protobuf.LazyStringArrayList(url_);
            }
            bitField0_ |= 0x00000002;
          }
          /**
           * <code>repeated string url = 2;</code>
           * @return A list containing the url.
           */
          public com.google.protobuf.ProtocolStringList
              getUrlList() {
            url_.makeImmutable();
            return url_;
          }
          /**
           * <code>repeated string url = 2;</code>
           * @return The count of url.
           */
          public int getUrlCount() {
            return url_.size();
          }
          /**
           * <code>repeated string url = 2;</code>
           * @param index The index of the element to return.
           * @return The url at the given index.
           */
          public java.lang.String getUrl(int index) {
            return url_.get(index);
          }
          /**
           * <code>repeated string url = 2;</code>
           * @param index The index of the value to return.
           * @return The bytes of the url at the given index.
           */
          public com.google.protobuf.ByteString
              getUrlBytes(int index) {
            return url_.getByteString(index);
          }
          /**
           * <code>repeated string url = 2;</code>
           * @param index The index to set the value at.
           * @param value The url to set.
           * @return This builder for chaining.
           */
          public Builder setUrl(
              int index, java.lang.String value) {
            if (value == null) { throw new NullPointerException(); }
            ensureUrlIsMutable();
            url_.set(index, value);
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string url = 2;</code>
           * @param value The url to add.
           * @return This builder for chaining.
           */
          public Builder addUrl(
              java.lang.String value) {
            if (value == null) { throw new NullPointerException(); }
            ensureUrlIsMutable();
            url_.add(value);
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string url = 2;</code>
           * @param values The url to add.
           * @return This builder for chaining.
           */
          public Builder addAllUrl(
              java.lang.Iterable<java.lang.String> values) {
            ensureUrlIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, url_);
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string url = 2;</code>
           * @return This builder for chaining.
           */
          public Builder clearUrl() {
            url_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
            bitField0_ = (bitField0_ & ~0x00000002);;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string url = 2;</code>
           * @param value The bytes of the url to add.
           * @return This builder for chaining.
           */
          public Builder addUrlBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            ensureUrlIsMutable();
            url_.add(value);
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }

          // @@protoc_insertion_point(builder_scope:Response.Seat.Ad.EventTrack)
        }

        // @@protoc_insertion_point(class_scope:Response.Seat.Ad.EventTrack)
        private static final cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack();
        }

        public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<EventTrack>
            PARSER = new com.google.protobuf.AbstractParser<EventTrack>() {
          @java.lang.Override
          public EventTrack parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            Builder builder = newBuilder();
            try {
              builder.mergeFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.setUnfinishedMessage(builder.buildPartial());
            } catch (com.google.protobuf.UninitializedMessageException e) {
              throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
            } catch (java.io.IOException e) {
              throw new com.google.protobuf.InvalidProtocolBufferException(e)
                  .setUnfinishedMessage(builder.buildPartial());
            }
            return builder.buildPartial();
          }
        };

        public static com.google.protobuf.Parser<EventTrack> parser() {
          return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<EventTrack> getParserForType() {
          return PARSER;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      private int bitField0_;
      public static final int ID_FIELD_NUMBER = 1;
      private int id_ = 0;
      /**
       * <pre>
       * 广告序号，为0
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 广告序号，为0
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }

      public static final int CREATIVE_TYPE_FIELD_NUMBER = 3;
      private int creativeType_ = 0;
      /**
       * <pre>
       * 创意类型
       * 1 文字 2 图片 3 Flash 4 视频
       * </pre>
       *
       * <code>optional int32 creative_type = 3;</code>
       * @return Whether the creativeType field is set.
       */
      @java.lang.Override
      public boolean hasCreativeType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 创意类型
       * 1 文字 2 图片 3 Flash 4 视频
       * </pre>
       *
       * <code>optional int32 creative_type = 3;</code>
       * @return The creativeType.
       */
      @java.lang.Override
      public int getCreativeType() {
        return creativeType_;
      }

      public static final int IMPRESSION_TRACKING_URL_FIELD_NUMBER = 6;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList impressionTrackingUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <pre>
       * 展现反馈地址
       * </pre>
       *
       * <code>repeated string impression_tracking_url = 6;</code>
       * @return A list containing the impressionTrackingUrl.
       */
      public com.google.protobuf.ProtocolStringList
          getImpressionTrackingUrlList() {
        return impressionTrackingUrl_;
      }
      /**
       * <pre>
       * 展现反馈地址
       * </pre>
       *
       * <code>repeated string impression_tracking_url = 6;</code>
       * @return The count of impressionTrackingUrl.
       */
      public int getImpressionTrackingUrlCount() {
        return impressionTrackingUrl_.size();
      }
      /**
       * <pre>
       * 展现反馈地址
       * </pre>
       *
       * <code>repeated string impression_tracking_url = 6;</code>
       * @param index The index of the element to return.
       * @return The impressionTrackingUrl at the given index.
       */
      public java.lang.String getImpressionTrackingUrl(int index) {
        return impressionTrackingUrl_.get(index);
      }
      /**
       * <pre>
       * 展现反馈地址
       * </pre>
       *
       * <code>repeated string impression_tracking_url = 6;</code>
       * @param index The index of the value to return.
       * @return The bytes of the impressionTrackingUrl at the given index.
       */
      public com.google.protobuf.ByteString
          getImpressionTrackingUrlBytes(int index) {
        return impressionTrackingUrl_.getByteString(index);
      }

      public static final int CLICK_THROUGH_URL_FIELD_NUMBER = 7;
      @SuppressWarnings("serial")
      private volatile java.lang.Object clickThroughUrl_ = "";
      /**
       * <pre>
       * 点击跳转地址(落地页)
       * </pre>
       *
       * <code>optional string click_through_url = 7;</code>
       * @return Whether the clickThroughUrl field is set.
       */
      @java.lang.Override
      public boolean hasClickThroughUrl() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 点击跳转地址(落地页)
       * </pre>
       *
       * <code>optional string click_through_url = 7;</code>
       * @return The clickThroughUrl.
       */
      @java.lang.Override
      public java.lang.String getClickThroughUrl() {
        java.lang.Object ref = clickThroughUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          clickThroughUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 点击跳转地址(落地页)
       * </pre>
       *
       * <code>optional string click_through_url = 7;</code>
       * @return The bytes for clickThroughUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getClickThroughUrlBytes() {
        java.lang.Object ref = clickThroughUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clickThroughUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int CLICK_TRACKING_URL_FIELD_NUMBER = 8;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList clickTrackingUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <pre>
       * 点击跟踪地址
       * </pre>
       *
       * <code>repeated string click_tracking_url = 8;</code>
       * @return A list containing the clickTrackingUrl.
       */
      public com.google.protobuf.ProtocolStringList
          getClickTrackingUrlList() {
        return clickTrackingUrl_;
      }
      /**
       * <pre>
       * 点击跟踪地址
       * </pre>
       *
       * <code>repeated string click_tracking_url = 8;</code>
       * @return The count of clickTrackingUrl.
       */
      public int getClickTrackingUrlCount() {
        return clickTrackingUrl_.size();
      }
      /**
       * <pre>
       * 点击跟踪地址
       * </pre>
       *
       * <code>repeated string click_tracking_url = 8;</code>
       * @param index The index of the element to return.
       * @return The clickTrackingUrl at the given index.
       */
      public java.lang.String getClickTrackingUrl(int index) {
        return clickTrackingUrl_.get(index);
      }
      /**
       * <pre>
       * 点击跟踪地址
       * </pre>
       *
       * <code>repeated string click_tracking_url = 8;</code>
       * @param index The index of the value to return.
       * @return The bytes of the clickTrackingUrl at the given index.
       */
      public com.google.protobuf.ByteString
          getClickTrackingUrlBytes(int index) {
        return clickTrackingUrl_.getByteString(index);
      }

      public static final int NATIVE_AD_FIELD_NUMBER = 10;
      private cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd nativeAd_;
      /**
       * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
       * @return Whether the nativeAd field is set.
       */
      @java.lang.Override
      public boolean hasNativeAd() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
       * @return The nativeAd.
       */
      @java.lang.Override
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd getNativeAd() {
        return nativeAd_ == null ? cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.getDefaultInstance() : nativeAd_;
      }
      /**
       * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
       */
      @java.lang.Override
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAdOrBuilder getNativeAdOrBuilder() {
        return nativeAd_ == null ? cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.getDefaultInstance() : nativeAd_;
      }

      public static final int CREATIVE_ID_FIELD_NUMBER = 11;
      @SuppressWarnings("serial")
      private volatile java.lang.Object creativeId_ = "";
      /**
       * <pre>
       * 广告创意的唯一标识
       * </pre>
       *
       * <code>optional string creative_id = 11;</code>
       * @return Whether the creativeId field is set.
       */
      @java.lang.Override
      public boolean hasCreativeId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 广告创意的唯一标识
       * </pre>
       *
       * <code>optional string creative_id = 11;</code>
       * @return The creativeId.
       */
      @java.lang.Override
      public java.lang.String getCreativeId() {
        java.lang.Object ref = creativeId_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          creativeId_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 广告创意的唯一标识
       * </pre>
       *
       * <code>optional string creative_id = 11;</code>
       * @return The bytes for creativeId.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getCreativeIdBytes() {
        java.lang.Object ref = creativeId_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          creativeId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int AD_SOURCE_FIELD_NUMBER = 12;
      @SuppressWarnings("serial")
      private volatile java.lang.Object adSource_ = "";
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>optional string ad_source = 12;</code>
       * @return Whether the adSource field is set.
       */
      @java.lang.Override
      public boolean hasAdSource() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>optional string ad_source = 12;</code>
       * @return The adSource.
       */
      @java.lang.Override
      public java.lang.String getAdSource() {
        java.lang.Object ref = adSource_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          adSource_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>optional string ad_source = 12;</code>
       * @return The bytes for adSource.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getAdSourceBytes() {
        java.lang.Object ref = adSource_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          adSource_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DEEPLINK_URL_FIELD_NUMBER = 13;
      @SuppressWarnings("serial")
      private volatile java.lang.Object deeplinkUrl_ = "";
      /**
       * <pre>
       * APP唤醒地址
       * </pre>
       *
       * <code>optional string deeplink_url = 13;</code>
       * @return Whether the deeplinkUrl field is set.
       */
      @java.lang.Override
      public boolean hasDeeplinkUrl() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * APP唤醒地址
       * </pre>
       *
       * <code>optional string deeplink_url = 13;</code>
       * @return The deeplinkUrl.
       */
      @java.lang.Override
      public java.lang.String getDeeplinkUrl() {
        java.lang.Object ref = deeplinkUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          deeplinkUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * APP唤醒地址
       * </pre>
       *
       * <code>optional string deeplink_url = 13;</code>
       * @return The bytes for deeplinkUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getDeeplinkUrlBytes() {
        java.lang.Object ref = deeplinkUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deeplinkUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DOWNLOAD_URL_FIELD_NUMBER = 14;
      @SuppressWarnings("serial")
      private volatile java.lang.Object downloadUrl_ = "";
      /**
       * <pre>
       * APP下载地址
       * </pre>
       *
       * <code>optional string download_url = 14;</code>
       * @return Whether the downloadUrl field is set.
       */
      @java.lang.Override
      public boolean hasDownloadUrl() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * APP下载地址
       * </pre>
       *
       * <code>optional string download_url = 14;</code>
       * @return The downloadUrl.
       */
      @java.lang.Override
      public java.lang.String getDownloadUrl() {
        java.lang.Object ref = downloadUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          downloadUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * APP下载地址
       * </pre>
       *
       * <code>optional string download_url = 14;</code>
       * @return The bytes for downloadUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getDownloadUrlBytes() {
        java.lang.Object ref = downloadUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          downloadUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int BID_PRICE_FIELD_NUMBER = 15;
      private int bidPrice_ = 0;
      /**
       * <pre>
       * 返回报价供上游adx竞价, 单位(分)
       * </pre>
       *
       * <code>optional int32 bid_price = 15;</code>
       * @return Whether the bidPrice field is set.
       */
      @java.lang.Override
      public boolean hasBidPrice() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 返回报价供上游adx竞价, 单位(分)
       * </pre>
       *
       * <code>optional int32 bid_price = 15;</code>
       * @return The bidPrice.
       */
      @java.lang.Override
      public int getBidPrice() {
        return bidPrice_;
      }

      public static final int EVENT_TRACK_FIELD_NUMBER = 16;
      @SuppressWarnings("serial")
      private java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack> eventTrack_;
      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      @java.lang.Override
      public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack> getEventTrackList() {
        return eventTrack_;
      }
      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      @java.lang.Override
      public java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder> 
          getEventTrackOrBuilderList() {
        return eventTrack_;
      }
      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      @java.lang.Override
      public int getEventTrackCount() {
        return eventTrack_.size();
      }
      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      @java.lang.Override
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack getEventTrack(int index) {
        return eventTrack_.get(index);
      }
      /**
       * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
       */
      @java.lang.Override
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder getEventTrackOrBuilder(
          int index) {
        return eventTrack_.get(index);
      }

      public static final int OPEN_TYPE_FIELD_NUMBER = 17;
      private int openType_ = 0;
      /**
       * <pre>
       * 落地页打开方式：1是打开网页(包含deeplink) 2 点击下载
       * </pre>
       *
       * <code>optional int32 open_type = 17;</code>
       * @return Whether the openType field is set.
       */
      @java.lang.Override
      public boolean hasOpenType() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 落地页打开方式：1是打开网页(包含deeplink) 2 点击下载
       * </pre>
       *
       * <code>optional int32 open_type = 17;</code>
       * @return The openType.
       */
      @java.lang.Override
      public int getOpenType() {
        return openType_;
      }

      public static final int WINNOTICE_URL_FIELD_NUMBER = 18;
      @SuppressWarnings("serial")
      private volatile java.lang.Object winnoticeUrl_ = "";
      /**
       * <pre>
       * 竞价成功通知，服务端发送
       * </pre>
       *
       * <code>optional string winnotice_url = 18;</code>
       * @return Whether the winnoticeUrl field is set.
       */
      @java.lang.Override
      public boolean hasWinnoticeUrl() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 竞价成功通知，服务端发送
       * </pre>
       *
       * <code>optional string winnotice_url = 18;</code>
       * @return The winnoticeUrl.
       */
      @java.lang.Override
      public java.lang.String getWinnoticeUrl() {
        java.lang.Object ref = winnoticeUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          winnoticeUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 竞价成功通知，服务端发送
       * </pre>
       *
       * <code>optional string winnotice_url = 18;</code>
       * @return The bytes for winnoticeUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getWinnoticeUrlBytes() {
        java.lang.Object ref = winnoticeUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          winnoticeUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PACKAGE_NAME_FIELD_NUMBER = 19;
      @SuppressWarnings("serial")
      private volatile java.lang.Object packageName_ = "";
      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>optional string package_name = 19;</code>
       * @return Whether the packageName field is set.
       */
      @java.lang.Override
      public boolean hasPackageName() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>optional string package_name = 19;</code>
       * @return The packageName.
       */
      @java.lang.Override
      public java.lang.String getPackageName() {
        java.lang.Object ref = packageName_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          packageName_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>optional string package_name = 19;</code>
       * @return The bytes for packageName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getPackageNameBytes() {
        java.lang.Object ref = packageName_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          packageName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int APP_NAME_FIELD_NUMBER = 20;
      @SuppressWarnings("serial")
      private volatile java.lang.Object appName_ = "";
      /**
       * <pre>
       * 应用名
       * </pre>
       *
       * <code>optional string app_name = 20;</code>
       * @return Whether the appName field is set.
       */
      @java.lang.Override
      public boolean hasAppName() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 应用名
       * </pre>
       *
       * <code>optional string app_name = 20;</code>
       * @return The appName.
       */
      @java.lang.Override
      public java.lang.String getAppName() {
        java.lang.Object ref = appName_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appName_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 应用名
       * </pre>
       *
       * <code>optional string app_name = 20;</code>
       * @return The bytes for appName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getAppNameBytes() {
        java.lang.Object ref = appName_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int APP_DESC_FIELD_NUMBER = 21;
      @SuppressWarnings("serial")
      private volatile java.lang.Object appDesc_ = "";
      /**
       * <pre>
       * *
       * 下载类相关信息
       * </pre>
       *
       * <code>optional string app_desc = 21;</code>
       * @return Whether the appDesc field is set.
       */
      @java.lang.Override
      public boolean hasAppDesc() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * *
       * 下载类相关信息
       * </pre>
       *
       * <code>optional string app_desc = 21;</code>
       * @return The appDesc.
       */
      @java.lang.Override
      public java.lang.String getAppDesc() {
        java.lang.Object ref = appDesc_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appDesc_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * *
       * 下载类相关信息
       * </pre>
       *
       * <code>optional string app_desc = 21;</code>
       * @return The bytes for appDesc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getAppDescBytes() {
        java.lang.Object ref = appDesc_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appDesc_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int APP_DOWNLOAD_URL_FIELD_NUMBER = 22;
      @SuppressWarnings("serial")
      private volatile java.lang.Object appDownloadUrl_ = "";
      /**
       * <pre>
       * 下载地址
       * </pre>
       *
       * <code>optional string app_download_url = 22;</code>
       * @return Whether the appDownloadUrl field is set.
       */
      @java.lang.Override
      public boolean hasAppDownloadUrl() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 下载地址
       * </pre>
       *
       * <code>optional string app_download_url = 22;</code>
       * @return The appDownloadUrl.
       */
      @java.lang.Override
      public java.lang.String getAppDownloadUrl() {
        java.lang.Object ref = appDownloadUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appDownloadUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 下载地址
       * </pre>
       *
       * <code>optional string app_download_url = 22;</code>
       * @return The bytes for appDownloadUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getAppDownloadUrlBytes() {
        java.lang.Object ref = appDownloadUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appDownloadUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PERMISSIONS_URL_FIELD_NUMBER = 23;
      @SuppressWarnings("serial")
      private volatile java.lang.Object permissionsUrl_ = "";
      /**
       * <pre>
       * 权限名称及权限描述列表
       * </pre>
       *
       * <code>optional string permissions_url = 23;</code>
       * @return Whether the permissionsUrl field is set.
       */
      @java.lang.Override
      public boolean hasPermissionsUrl() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 权限名称及权限描述列表
       * </pre>
       *
       * <code>optional string permissions_url = 23;</code>
       * @return The permissionsUrl.
       */
      @java.lang.Override
      public java.lang.String getPermissionsUrl() {
        java.lang.Object ref = permissionsUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          permissionsUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 权限名称及权限描述列表
       * </pre>
       *
       * <code>optional string permissions_url = 23;</code>
       * @return The bytes for permissionsUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getPermissionsUrlBytes() {
        java.lang.Object ref = permissionsUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          permissionsUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int FUNCTION_DESC_URL_FIELD_NUMBER = 24;
      @SuppressWarnings("serial")
      private volatile java.lang.Object functionDescUrl_ = "";
      /**
       * <pre>
       * 产品功能url
       * </pre>
       *
       * <code>optional string function_desc_url = 24;</code>
       * @return Whether the functionDescUrl field is set.
       */
      @java.lang.Override
      public boolean hasFunctionDescUrl() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * 产品功能url
       * </pre>
       *
       * <code>optional string function_desc_url = 24;</code>
       * @return The functionDescUrl.
       */
      @java.lang.Override
      public java.lang.String getFunctionDescUrl() {
        java.lang.Object ref = functionDescUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          functionDescUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 产品功能url
       * </pre>
       *
       * <code>optional string function_desc_url = 24;</code>
       * @return The bytes for functionDescUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getFunctionDescUrlBytes() {
        java.lang.Object ref = functionDescUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          functionDescUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PRIVACY_URL_FIELD_NUMBER = 25;
      @SuppressWarnings("serial")
      private volatile java.lang.Object privacyUrl_ = "";
      /**
       * <pre>
       * 隐私协议
       * </pre>
       *
       * <code>optional string privacy_url = 25;</code>
       * @return Whether the privacyUrl field is set.
       */
      @java.lang.Override
      public boolean hasPrivacyUrl() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * 隐私协议
       * </pre>
       *
       * <code>optional string privacy_url = 25;</code>
       * @return The privacyUrl.
       */
      @java.lang.Override
      public java.lang.String getPrivacyUrl() {
        java.lang.Object ref = privacyUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          privacyUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 隐私协议
       * </pre>
       *
       * <code>optional string privacy_url = 25;</code>
       * @return The bytes for privacyUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getPrivacyUrlBytes() {
        java.lang.Object ref = privacyUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          privacyUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DEVELOPER_NAME_FIELD_NUMBER = 26;
      @SuppressWarnings("serial")
      private volatile java.lang.Object developerName_ = "";
      /**
       * <pre>
       * 开发者公司名称
       * </pre>
       *
       * <code>optional string developer_name = 26;</code>
       * @return Whether the developerName field is set.
       */
      @java.lang.Override
      public boolean hasDeveloperName() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * 开发者公司名称
       * </pre>
       *
       * <code>optional string developer_name = 26;</code>
       * @return The developerName.
       */
      @java.lang.Override
      public java.lang.String getDeveloperName() {
        java.lang.Object ref = developerName_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          developerName_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 开发者公司名称
       * </pre>
       *
       * <code>optional string developer_name = 26;</code>
       * @return The bytes for developerName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getDeveloperNameBytes() {
        java.lang.Object ref = developerName_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          developerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int APP_VERSION_FIELD_NUMBER = 27;
      @SuppressWarnings("serial")
      private volatile java.lang.Object appVersion_ = "";
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string app_version = 27;</code>
       * @return Whether the appVersion field is set.
       */
      @java.lang.Override
      public boolean hasAppVersion() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string app_version = 27;</code>
       * @return The appVersion.
       */
      @java.lang.Override
      public java.lang.String getAppVersion() {
        java.lang.Object ref = appVersion_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appVersion_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string app_version = 27;</code>
       * @return The bytes for appVersion.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getAppVersionBytes() {
        java.lang.Object ref = appVersion_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int APP_ICON_URL_FIELD_NUMBER = 28;
      @SuppressWarnings("serial")
      private volatile java.lang.Object appIconUrl_ = "";
      /**
       * <pre>
       * App图标链接
       * </pre>
       *
       * <code>optional string app_icon_url = 28;</code>
       * @return Whether the appIconUrl field is set.
       */
      @java.lang.Override
      public boolean hasAppIconUrl() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * App图标链接
       * </pre>
       *
       * <code>optional string app_icon_url = 28;</code>
       * @return The appIconUrl.
       */
      @java.lang.Override
      public java.lang.String getAppIconUrl() {
        java.lang.Object ref = appIconUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appIconUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * App图标链接
       * </pre>
       *
       * <code>optional string app_icon_url = 28;</code>
       * @return The bytes for appIconUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getAppIconUrlBytes() {
        java.lang.Object ref = appIconUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appIconUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int APP_SIZE_FIELD_NUMBER = 29;
      private long appSize_ = 0L;
      /**
       * <pre>
       * app大小
       * </pre>
       *
       * <code>optional int64 app_size = 29;</code>
       * @return Whether the appSize field is set.
       */
      @java.lang.Override
      public boolean hasAppSize() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * app大小
       * </pre>
       *
       * <code>optional int64 app_size = 29;</code>
       * @return The appSize.
       */
      @java.lang.Override
      public long getAppSize() {
        return appSize_;
      }

      public static final int FILE_MD5_FIELD_NUMBER = 30;
      @SuppressWarnings("serial")
      private volatile java.lang.Object fileMd5_ = "";
      /**
       * <pre>
       * app文件md5
       * </pre>
       *
       * <code>optional string file_md5 = 30;</code>
       * @return Whether the fileMd5 field is set.
       */
      @java.lang.Override
      public boolean hasFileMd5() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * app文件md5
       * </pre>
       *
       * <code>optional string file_md5 = 30;</code>
       * @return The fileMd5.
       */
      @java.lang.Override
      public java.lang.String getFileMd5() {
        java.lang.Object ref = fileMd5_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          fileMd5_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * app文件md5
       * </pre>
       *
       * <code>optional string file_md5 = 30;</code>
       * @return The bytes for fileMd5.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getFileMd5Bytes() {
        java.lang.Object ref = fileMd5_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeInt32(1, id_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeInt32(3, creativeType_);
        }
        for (int i = 0; i < impressionTrackingUrl_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 6, impressionTrackingUrl_.getRaw(i));
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 7, clickThroughUrl_);
        }
        for (int i = 0; i < clickTrackingUrl_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 8, clickTrackingUrl_.getRaw(i));
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          output.writeMessage(10, getNativeAd());
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 11, creativeId_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 12, adSource_);
        }
        if (((bitField0_ & 0x00000040) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 13, deeplinkUrl_);
        }
        if (((bitField0_ & 0x00000080) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 14, downloadUrl_);
        }
        if (((bitField0_ & 0x00000100) != 0)) {
          output.writeInt32(15, bidPrice_);
        }
        for (int i = 0; i < eventTrack_.size(); i++) {
          output.writeMessage(16, eventTrack_.get(i));
        }
        if (((bitField0_ & 0x00000200) != 0)) {
          output.writeInt32(17, openType_);
        }
        if (((bitField0_ & 0x00000400) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 18, winnoticeUrl_);
        }
        if (((bitField0_ & 0x00000800) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 19, packageName_);
        }
        if (((bitField0_ & 0x00001000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 20, appName_);
        }
        if (((bitField0_ & 0x00002000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 21, appDesc_);
        }
        if (((bitField0_ & 0x00004000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 22, appDownloadUrl_);
        }
        if (((bitField0_ & 0x00008000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 23, permissionsUrl_);
        }
        if (((bitField0_ & 0x00010000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 24, functionDescUrl_);
        }
        if (((bitField0_ & 0x00020000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 25, privacyUrl_);
        }
        if (((bitField0_ & 0x00040000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 26, developerName_);
        }
        if (((bitField0_ & 0x00080000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 27, appVersion_);
        }
        if (((bitField0_ & 0x00100000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 28, appIconUrl_);
        }
        if (((bitField0_ & 0x00200000) != 0)) {
          output.writeInt64(29, appSize_);
        }
        if (((bitField0_ & 0x00400000) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 30, fileMd5_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(1, id_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(3, creativeType_);
        }
        {
          int dataSize = 0;
          for (int i = 0; i < impressionTrackingUrl_.size(); i++) {
            dataSize += computeStringSizeNoTag(impressionTrackingUrl_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getImpressionTrackingUrlList().size();
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(7, clickThroughUrl_);
        }
        {
          int dataSize = 0;
          for (int i = 0; i < clickTrackingUrl_.size(); i++) {
            dataSize += computeStringSizeNoTag(clickTrackingUrl_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getClickTrackingUrlList().size();
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(10, getNativeAd());
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(11, creativeId_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(12, adSource_);
        }
        if (((bitField0_ & 0x00000040) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(13, deeplinkUrl_);
        }
        if (((bitField0_ & 0x00000080) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(14, downloadUrl_);
        }
        if (((bitField0_ & 0x00000100) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(15, bidPrice_);
        }
        for (int i = 0; i < eventTrack_.size(); i++) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(16, eventTrack_.get(i));
        }
        if (((bitField0_ & 0x00000200) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(17, openType_);
        }
        if (((bitField0_ & 0x00000400) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(18, winnoticeUrl_);
        }
        if (((bitField0_ & 0x00000800) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(19, packageName_);
        }
        if (((bitField0_ & 0x00001000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(20, appName_);
        }
        if (((bitField0_ & 0x00002000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(21, appDesc_);
        }
        if (((bitField0_ & 0x00004000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(22, appDownloadUrl_);
        }
        if (((bitField0_ & 0x00008000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(23, permissionsUrl_);
        }
        if (((bitField0_ & 0x00010000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(24, functionDescUrl_);
        }
        if (((bitField0_ & 0x00020000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(25, privacyUrl_);
        }
        if (((bitField0_ & 0x00040000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(26, developerName_);
        }
        if (((bitField0_ & 0x00080000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(27, appVersion_);
        }
        if (((bitField0_ & 0x00100000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(28, appIconUrl_);
        }
        if (((bitField0_ & 0x00200000) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt64Size(29, appSize_);
        }
        if (((bitField0_ & 0x00400000) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(30, fileMd5_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad)) {
          return super.equals(obj);
        }
        cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad other = (cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad) obj;

        if (hasId() != other.hasId()) return false;
        if (hasId()) {
          if (getId()
              != other.getId()) return false;
        }
        if (hasCreativeType() != other.hasCreativeType()) return false;
        if (hasCreativeType()) {
          if (getCreativeType()
              != other.getCreativeType()) return false;
        }
        if (!getImpressionTrackingUrlList()
            .equals(other.getImpressionTrackingUrlList())) return false;
        if (hasClickThroughUrl() != other.hasClickThroughUrl()) return false;
        if (hasClickThroughUrl()) {
          if (!getClickThroughUrl()
              .equals(other.getClickThroughUrl())) return false;
        }
        if (!getClickTrackingUrlList()
            .equals(other.getClickTrackingUrlList())) return false;
        if (hasNativeAd() != other.hasNativeAd()) return false;
        if (hasNativeAd()) {
          if (!getNativeAd()
              .equals(other.getNativeAd())) return false;
        }
        if (hasCreativeId() != other.hasCreativeId()) return false;
        if (hasCreativeId()) {
          if (!getCreativeId()
              .equals(other.getCreativeId())) return false;
        }
        if (hasAdSource() != other.hasAdSource()) return false;
        if (hasAdSource()) {
          if (!getAdSource()
              .equals(other.getAdSource())) return false;
        }
        if (hasDeeplinkUrl() != other.hasDeeplinkUrl()) return false;
        if (hasDeeplinkUrl()) {
          if (!getDeeplinkUrl()
              .equals(other.getDeeplinkUrl())) return false;
        }
        if (hasDownloadUrl() != other.hasDownloadUrl()) return false;
        if (hasDownloadUrl()) {
          if (!getDownloadUrl()
              .equals(other.getDownloadUrl())) return false;
        }
        if (hasBidPrice() != other.hasBidPrice()) return false;
        if (hasBidPrice()) {
          if (getBidPrice()
              != other.getBidPrice()) return false;
        }
        if (!getEventTrackList()
            .equals(other.getEventTrackList())) return false;
        if (hasOpenType() != other.hasOpenType()) return false;
        if (hasOpenType()) {
          if (getOpenType()
              != other.getOpenType()) return false;
        }
        if (hasWinnoticeUrl() != other.hasWinnoticeUrl()) return false;
        if (hasWinnoticeUrl()) {
          if (!getWinnoticeUrl()
              .equals(other.getWinnoticeUrl())) return false;
        }
        if (hasPackageName() != other.hasPackageName()) return false;
        if (hasPackageName()) {
          if (!getPackageName()
              .equals(other.getPackageName())) return false;
        }
        if (hasAppName() != other.hasAppName()) return false;
        if (hasAppName()) {
          if (!getAppName()
              .equals(other.getAppName())) return false;
        }
        if (hasAppDesc() != other.hasAppDesc()) return false;
        if (hasAppDesc()) {
          if (!getAppDesc()
              .equals(other.getAppDesc())) return false;
        }
        if (hasAppDownloadUrl() != other.hasAppDownloadUrl()) return false;
        if (hasAppDownloadUrl()) {
          if (!getAppDownloadUrl()
              .equals(other.getAppDownloadUrl())) return false;
        }
        if (hasPermissionsUrl() != other.hasPermissionsUrl()) return false;
        if (hasPermissionsUrl()) {
          if (!getPermissionsUrl()
              .equals(other.getPermissionsUrl())) return false;
        }
        if (hasFunctionDescUrl() != other.hasFunctionDescUrl()) return false;
        if (hasFunctionDescUrl()) {
          if (!getFunctionDescUrl()
              .equals(other.getFunctionDescUrl())) return false;
        }
        if (hasPrivacyUrl() != other.hasPrivacyUrl()) return false;
        if (hasPrivacyUrl()) {
          if (!getPrivacyUrl()
              .equals(other.getPrivacyUrl())) return false;
        }
        if (hasDeveloperName() != other.hasDeveloperName()) return false;
        if (hasDeveloperName()) {
          if (!getDeveloperName()
              .equals(other.getDeveloperName())) return false;
        }
        if (hasAppVersion() != other.hasAppVersion()) return false;
        if (hasAppVersion()) {
          if (!getAppVersion()
              .equals(other.getAppVersion())) return false;
        }
        if (hasAppIconUrl() != other.hasAppIconUrl()) return false;
        if (hasAppIconUrl()) {
          if (!getAppIconUrl()
              .equals(other.getAppIconUrl())) return false;
        }
        if (hasAppSize() != other.hasAppSize()) return false;
        if (hasAppSize()) {
          if (getAppSize()
              != other.getAppSize()) return false;
        }
        if (hasFileMd5() != other.hasFileMd5()) return false;
        if (hasFileMd5()) {
          if (!getFileMd5()
              .equals(other.getFileMd5())) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasId()) {
          hash = (37 * hash) + ID_FIELD_NUMBER;
          hash = (53 * hash) + getId();
        }
        if (hasCreativeType()) {
          hash = (37 * hash) + CREATIVE_TYPE_FIELD_NUMBER;
          hash = (53 * hash) + getCreativeType();
        }
        if (getImpressionTrackingUrlCount() > 0) {
          hash = (37 * hash) + IMPRESSION_TRACKING_URL_FIELD_NUMBER;
          hash = (53 * hash) + getImpressionTrackingUrlList().hashCode();
        }
        if (hasClickThroughUrl()) {
          hash = (37 * hash) + CLICK_THROUGH_URL_FIELD_NUMBER;
          hash = (53 * hash) + getClickThroughUrl().hashCode();
        }
        if (getClickTrackingUrlCount() > 0) {
          hash = (37 * hash) + CLICK_TRACKING_URL_FIELD_NUMBER;
          hash = (53 * hash) + getClickTrackingUrlList().hashCode();
        }
        if (hasNativeAd()) {
          hash = (37 * hash) + NATIVE_AD_FIELD_NUMBER;
          hash = (53 * hash) + getNativeAd().hashCode();
        }
        if (hasCreativeId()) {
          hash = (37 * hash) + CREATIVE_ID_FIELD_NUMBER;
          hash = (53 * hash) + getCreativeId().hashCode();
        }
        if (hasAdSource()) {
          hash = (37 * hash) + AD_SOURCE_FIELD_NUMBER;
          hash = (53 * hash) + getAdSource().hashCode();
        }
        if (hasDeeplinkUrl()) {
          hash = (37 * hash) + DEEPLINK_URL_FIELD_NUMBER;
          hash = (53 * hash) + getDeeplinkUrl().hashCode();
        }
        if (hasDownloadUrl()) {
          hash = (37 * hash) + DOWNLOAD_URL_FIELD_NUMBER;
          hash = (53 * hash) + getDownloadUrl().hashCode();
        }
        if (hasBidPrice()) {
          hash = (37 * hash) + BID_PRICE_FIELD_NUMBER;
          hash = (53 * hash) + getBidPrice();
        }
        if (getEventTrackCount() > 0) {
          hash = (37 * hash) + EVENT_TRACK_FIELD_NUMBER;
          hash = (53 * hash) + getEventTrackList().hashCode();
        }
        if (hasOpenType()) {
          hash = (37 * hash) + OPEN_TYPE_FIELD_NUMBER;
          hash = (53 * hash) + getOpenType();
        }
        if (hasWinnoticeUrl()) {
          hash = (37 * hash) + WINNOTICE_URL_FIELD_NUMBER;
          hash = (53 * hash) + getWinnoticeUrl().hashCode();
        }
        if (hasPackageName()) {
          hash = (37 * hash) + PACKAGE_NAME_FIELD_NUMBER;
          hash = (53 * hash) + getPackageName().hashCode();
        }
        if (hasAppName()) {
          hash = (37 * hash) + APP_NAME_FIELD_NUMBER;
          hash = (53 * hash) + getAppName().hashCode();
        }
        if (hasAppDesc()) {
          hash = (37 * hash) + APP_DESC_FIELD_NUMBER;
          hash = (53 * hash) + getAppDesc().hashCode();
        }
        if (hasAppDownloadUrl()) {
          hash = (37 * hash) + APP_DOWNLOAD_URL_FIELD_NUMBER;
          hash = (53 * hash) + getAppDownloadUrl().hashCode();
        }
        if (hasPermissionsUrl()) {
          hash = (37 * hash) + PERMISSIONS_URL_FIELD_NUMBER;
          hash = (53 * hash) + getPermissionsUrl().hashCode();
        }
        if (hasFunctionDescUrl()) {
          hash = (37 * hash) + FUNCTION_DESC_URL_FIELD_NUMBER;
          hash = (53 * hash) + getFunctionDescUrl().hashCode();
        }
        if (hasPrivacyUrl()) {
          hash = (37 * hash) + PRIVACY_URL_FIELD_NUMBER;
          hash = (53 * hash) + getPrivacyUrl().hashCode();
        }
        if (hasDeveloperName()) {
          hash = (37 * hash) + DEVELOPER_NAME_FIELD_NUMBER;
          hash = (53 * hash) + getDeveloperName().hashCode();
        }
        if (hasAppVersion()) {
          hash = (37 * hash) + APP_VERSION_FIELD_NUMBER;
          hash = (53 * hash) + getAppVersion().hashCode();
        }
        if (hasAppIconUrl()) {
          hash = (37 * hash) + APP_ICON_URL_FIELD_NUMBER;
          hash = (53 * hash) + getAppIconUrl().hashCode();
        }
        if (hasAppSize()) {
          hash = (37 * hash) + APP_SIZE_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getAppSize());
        }
        if (hasFileMd5()) {
          hash = (37 * hash) + FILE_MD5_FIELD_NUMBER;
          hash = (53 * hash) + getFileMd5().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * 广告字段
       * </pre>
       *
       * Protobuf type {@code Response.Seat.Ad}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Response.Seat.Ad)
          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder.class);
        }

        // Construct using cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessage
                  .alwaysUseFieldBuilders) {
            getNativeAdFieldBuilder();
            getEventTrackFieldBuilder();
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          id_ = 0;
          creativeType_ = 0;
          impressionTrackingUrl_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          clickThroughUrl_ = "";
          clickTrackingUrl_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          nativeAd_ = null;
          if (nativeAdBuilder_ != null) {
            nativeAdBuilder_.dispose();
            nativeAdBuilder_ = null;
          }
          creativeId_ = "";
          adSource_ = "";
          deeplinkUrl_ = "";
          downloadUrl_ = "";
          bidPrice_ = 0;
          if (eventTrackBuilder_ == null) {
            eventTrack_ = java.util.Collections.emptyList();
          } else {
            eventTrack_ = null;
            eventTrackBuilder_.clear();
          }
          bitField0_ = (bitField0_ & ~0x00000800);
          openType_ = 0;
          winnoticeUrl_ = "";
          packageName_ = "";
          appName_ = "";
          appDesc_ = "";
          appDownloadUrl_ = "";
          permissionsUrl_ = "";
          functionDescUrl_ = "";
          privacyUrl_ = "";
          developerName_ = "";
          appVersion_ = "";
          appIconUrl_ = "";
          appSize_ = 0L;
          fileMd5_ = "";
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_Ad_descriptor;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad getDefaultInstanceForType() {
          return cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.getDefaultInstance();
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad build() {
          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad buildPartial() {
          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad result = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad(this);
          buildPartialRepeatedFields(result);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartialRepeatedFields(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad result) {
          if (eventTrackBuilder_ == null) {
            if (((bitField0_ & 0x00000800) != 0)) {
              eventTrack_ = java.util.Collections.unmodifiableList(eventTrack_);
              bitField0_ = (bitField0_ & ~0x00000800);
            }
            result.eventTrack_ = eventTrack_;
          } else {
            result.eventTrack_ = eventTrackBuilder_.build();
          }
        }

        private void buildPartial0(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.id_ = id_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.creativeType_ = creativeType_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            impressionTrackingUrl_.makeImmutable();
            result.impressionTrackingUrl_ = impressionTrackingUrl_;
          }
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.clickThroughUrl_ = clickThroughUrl_;
            to_bitField0_ |= 0x00000004;
          }
          if (((from_bitField0_ & 0x00000010) != 0)) {
            clickTrackingUrl_.makeImmutable();
            result.clickTrackingUrl_ = clickTrackingUrl_;
          }
          if (((from_bitField0_ & 0x00000020) != 0)) {
            result.nativeAd_ = nativeAdBuilder_ == null
                ? nativeAd_
                : nativeAdBuilder_.build();
            to_bitField0_ |= 0x00000008;
          }
          if (((from_bitField0_ & 0x00000040) != 0)) {
            result.creativeId_ = creativeId_;
            to_bitField0_ |= 0x00000010;
          }
          if (((from_bitField0_ & 0x00000080) != 0)) {
            result.adSource_ = adSource_;
            to_bitField0_ |= 0x00000020;
          }
          if (((from_bitField0_ & 0x00000100) != 0)) {
            result.deeplinkUrl_ = deeplinkUrl_;
            to_bitField0_ |= 0x00000040;
          }
          if (((from_bitField0_ & 0x00000200) != 0)) {
            result.downloadUrl_ = downloadUrl_;
            to_bitField0_ |= 0x00000080;
          }
          if (((from_bitField0_ & 0x00000400) != 0)) {
            result.bidPrice_ = bidPrice_;
            to_bitField0_ |= 0x00000100;
          }
          if (((from_bitField0_ & 0x00001000) != 0)) {
            result.openType_ = openType_;
            to_bitField0_ |= 0x00000200;
          }
          if (((from_bitField0_ & 0x00002000) != 0)) {
            result.winnoticeUrl_ = winnoticeUrl_;
            to_bitField0_ |= 0x00000400;
          }
          if (((from_bitField0_ & 0x00004000) != 0)) {
            result.packageName_ = packageName_;
            to_bitField0_ |= 0x00000800;
          }
          if (((from_bitField0_ & 0x00008000) != 0)) {
            result.appName_ = appName_;
            to_bitField0_ |= 0x00001000;
          }
          if (((from_bitField0_ & 0x00010000) != 0)) {
            result.appDesc_ = appDesc_;
            to_bitField0_ |= 0x00002000;
          }
          if (((from_bitField0_ & 0x00020000) != 0)) {
            result.appDownloadUrl_ = appDownloadUrl_;
            to_bitField0_ |= 0x00004000;
          }
          if (((from_bitField0_ & 0x00040000) != 0)) {
            result.permissionsUrl_ = permissionsUrl_;
            to_bitField0_ |= 0x00008000;
          }
          if (((from_bitField0_ & 0x00080000) != 0)) {
            result.functionDescUrl_ = functionDescUrl_;
            to_bitField0_ |= 0x00010000;
          }
          if (((from_bitField0_ & 0x00100000) != 0)) {
            result.privacyUrl_ = privacyUrl_;
            to_bitField0_ |= 0x00020000;
          }
          if (((from_bitField0_ & 0x00200000) != 0)) {
            result.developerName_ = developerName_;
            to_bitField0_ |= 0x00040000;
          }
          if (((from_bitField0_ & 0x00400000) != 0)) {
            result.appVersion_ = appVersion_;
            to_bitField0_ |= 0x00080000;
          }
          if (((from_bitField0_ & 0x00800000) != 0)) {
            result.appIconUrl_ = appIconUrl_;
            to_bitField0_ |= 0x00100000;
          }
          if (((from_bitField0_ & 0x01000000) != 0)) {
            result.appSize_ = appSize_;
            to_bitField0_ |= 0x00200000;
          }
          if (((from_bitField0_ & 0x02000000) != 0)) {
            result.fileMd5_ = fileMd5_;
            to_bitField0_ |= 0x00400000;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad) {
            return mergeFrom((cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad other) {
          if (other == cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.getDefaultInstance()) return this;
          if (other.hasId()) {
            setId(other.getId());
          }
          if (other.hasCreativeType()) {
            setCreativeType(other.getCreativeType());
          }
          if (!other.impressionTrackingUrl_.isEmpty()) {
            if (impressionTrackingUrl_.isEmpty()) {
              impressionTrackingUrl_ = other.impressionTrackingUrl_;
              bitField0_ |= 0x00000004;
            } else {
              ensureImpressionTrackingUrlIsMutable();
              impressionTrackingUrl_.addAll(other.impressionTrackingUrl_);
            }
            onChanged();
          }
          if (other.hasClickThroughUrl()) {
            clickThroughUrl_ = other.clickThroughUrl_;
            bitField0_ |= 0x00000008;
            onChanged();
          }
          if (!other.clickTrackingUrl_.isEmpty()) {
            if (clickTrackingUrl_.isEmpty()) {
              clickTrackingUrl_ = other.clickTrackingUrl_;
              bitField0_ |= 0x00000010;
            } else {
              ensureClickTrackingUrlIsMutable();
              clickTrackingUrl_.addAll(other.clickTrackingUrl_);
            }
            onChanged();
          }
          if (other.hasNativeAd()) {
            mergeNativeAd(other.getNativeAd());
          }
          if (other.hasCreativeId()) {
            creativeId_ = other.creativeId_;
            bitField0_ |= 0x00000040;
            onChanged();
          }
          if (other.hasAdSource()) {
            adSource_ = other.adSource_;
            bitField0_ |= 0x00000080;
            onChanged();
          }
          if (other.hasDeeplinkUrl()) {
            deeplinkUrl_ = other.deeplinkUrl_;
            bitField0_ |= 0x00000100;
            onChanged();
          }
          if (other.hasDownloadUrl()) {
            downloadUrl_ = other.downloadUrl_;
            bitField0_ |= 0x00000200;
            onChanged();
          }
          if (other.hasBidPrice()) {
            setBidPrice(other.getBidPrice());
          }
          if (eventTrackBuilder_ == null) {
            if (!other.eventTrack_.isEmpty()) {
              if (eventTrack_.isEmpty()) {
                eventTrack_ = other.eventTrack_;
                bitField0_ = (bitField0_ & ~0x00000800);
              } else {
                ensureEventTrackIsMutable();
                eventTrack_.addAll(other.eventTrack_);
              }
              onChanged();
            }
          } else {
            if (!other.eventTrack_.isEmpty()) {
              if (eventTrackBuilder_.isEmpty()) {
                eventTrackBuilder_.dispose();
                eventTrackBuilder_ = null;
                eventTrack_ = other.eventTrack_;
                bitField0_ = (bitField0_ & ~0x00000800);
                eventTrackBuilder_ = 
                  com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                     getEventTrackFieldBuilder() : null;
              } else {
                eventTrackBuilder_.addAllMessages(other.eventTrack_);
              }
            }
          }
          if (other.hasOpenType()) {
            setOpenType(other.getOpenType());
          }
          if (other.hasWinnoticeUrl()) {
            winnoticeUrl_ = other.winnoticeUrl_;
            bitField0_ |= 0x00002000;
            onChanged();
          }
          if (other.hasPackageName()) {
            packageName_ = other.packageName_;
            bitField0_ |= 0x00004000;
            onChanged();
          }
          if (other.hasAppName()) {
            appName_ = other.appName_;
            bitField0_ |= 0x00008000;
            onChanged();
          }
          if (other.hasAppDesc()) {
            appDesc_ = other.appDesc_;
            bitField0_ |= 0x00010000;
            onChanged();
          }
          if (other.hasAppDownloadUrl()) {
            appDownloadUrl_ = other.appDownloadUrl_;
            bitField0_ |= 0x00020000;
            onChanged();
          }
          if (other.hasPermissionsUrl()) {
            permissionsUrl_ = other.permissionsUrl_;
            bitField0_ |= 0x00040000;
            onChanged();
          }
          if (other.hasFunctionDescUrl()) {
            functionDescUrl_ = other.functionDescUrl_;
            bitField0_ |= 0x00080000;
            onChanged();
          }
          if (other.hasPrivacyUrl()) {
            privacyUrl_ = other.privacyUrl_;
            bitField0_ |= 0x00100000;
            onChanged();
          }
          if (other.hasDeveloperName()) {
            developerName_ = other.developerName_;
            bitField0_ |= 0x00200000;
            onChanged();
          }
          if (other.hasAppVersion()) {
            appVersion_ = other.appVersion_;
            bitField0_ |= 0x00400000;
            onChanged();
          }
          if (other.hasAppIconUrl()) {
            appIconUrl_ = other.appIconUrl_;
            bitField0_ |= 0x00800000;
            onChanged();
          }
          if (other.hasAppSize()) {
            setAppSize(other.getAppSize());
          }
          if (other.hasFileMd5()) {
            fileMd5_ = other.fileMd5_;
            bitField0_ |= 0x02000000;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  id_ = input.readInt32();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 24: {
                  creativeType_ = input.readInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 24
                case 50: {
                  java.lang.String s = input.readStringRequireUtf8();
                  ensureImpressionTrackingUrlIsMutable();
                  impressionTrackingUrl_.add(s);
                  break;
                } // case 50
                case 58: {
                  clickThroughUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000008;
                  break;
                } // case 58
                case 66: {
                  java.lang.String s = input.readStringRequireUtf8();
                  ensureClickTrackingUrlIsMutable();
                  clickTrackingUrl_.add(s);
                  break;
                } // case 66
                case 82: {
                  input.readMessage(
                      getNativeAdFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField0_ |= 0x00000020;
                  break;
                } // case 82
                case 90: {
                  creativeId_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000040;
                  break;
                } // case 90
                case 98: {
                  adSource_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000080;
                  break;
                } // case 98
                case 106: {
                  deeplinkUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000100;
                  break;
                } // case 106
                case 114: {
                  downloadUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000200;
                  break;
                } // case 114
                case 120: {
                  bidPrice_ = input.readInt32();
                  bitField0_ |= 0x00000400;
                  break;
                } // case 120
                case 130: {
                  cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack m =
                      input.readMessage(
                          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.parser(),
                          extensionRegistry);
                  if (eventTrackBuilder_ == null) {
                    ensureEventTrackIsMutable();
                    eventTrack_.add(m);
                  } else {
                    eventTrackBuilder_.addMessage(m);
                  }
                  break;
                } // case 130
                case 136: {
                  openType_ = input.readInt32();
                  bitField0_ |= 0x00001000;
                  break;
                } // case 136
                case 146: {
                  winnoticeUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00002000;
                  break;
                } // case 146
                case 154: {
                  packageName_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00004000;
                  break;
                } // case 154
                case 162: {
                  appName_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00008000;
                  break;
                } // case 162
                case 170: {
                  appDesc_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00010000;
                  break;
                } // case 170
                case 178: {
                  appDownloadUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00020000;
                  break;
                } // case 178
                case 186: {
                  permissionsUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00040000;
                  break;
                } // case 186
                case 194: {
                  functionDescUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00080000;
                  break;
                } // case 194
                case 202: {
                  privacyUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00100000;
                  break;
                } // case 202
                case 210: {
                  developerName_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00200000;
                  break;
                } // case 210
                case 218: {
                  appVersion_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00400000;
                  break;
                } // case 218
                case 226: {
                  appIconUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00800000;
                  break;
                } // case 226
                case 232: {
                  appSize_ = input.readInt64();
                  bitField0_ |= 0x01000000;
                  break;
                } // case 232
                case 242: {
                  fileMd5_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x02000000;
                  break;
                } // case 242
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private int id_ ;
        /**
         * <pre>
         * 广告序号，为0
         * </pre>
         *
         * <code>optional int32 id = 1;</code>
         * @return Whether the id field is set.
         */
        @java.lang.Override
        public boolean hasId() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <pre>
         * 广告序号，为0
         * </pre>
         *
         * <code>optional int32 id = 1;</code>
         * @return The id.
         */
        @java.lang.Override
        public int getId() {
          return id_;
        }
        /**
         * <pre>
         * 广告序号，为0
         * </pre>
         *
         * <code>optional int32 id = 1;</code>
         * @param value The id to set.
         * @return This builder for chaining.
         */
        public Builder setId(int value) {

          id_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 广告序号，为0
         * </pre>
         *
         * <code>optional int32 id = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearId() {
          bitField0_ = (bitField0_ & ~0x00000001);
          id_ = 0;
          onChanged();
          return this;
        }

        private int creativeType_ ;
        /**
         * <pre>
         * 创意类型
         * 1 文字 2 图片 3 Flash 4 视频
         * </pre>
         *
         * <code>optional int32 creative_type = 3;</code>
         * @return Whether the creativeType field is set.
         */
        @java.lang.Override
        public boolean hasCreativeType() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <pre>
         * 创意类型
         * 1 文字 2 图片 3 Flash 4 视频
         * </pre>
         *
         * <code>optional int32 creative_type = 3;</code>
         * @return The creativeType.
         */
        @java.lang.Override
        public int getCreativeType() {
          return creativeType_;
        }
        /**
         * <pre>
         * 创意类型
         * 1 文字 2 图片 3 Flash 4 视频
         * </pre>
         *
         * <code>optional int32 creative_type = 3;</code>
         * @param value The creativeType to set.
         * @return This builder for chaining.
         */
        public Builder setCreativeType(int value) {

          creativeType_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 创意类型
         * 1 文字 2 图片 3 Flash 4 视频
         * </pre>
         *
         * <code>optional int32 creative_type = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearCreativeType() {
          bitField0_ = (bitField0_ & ~0x00000002);
          creativeType_ = 0;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList impressionTrackingUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureImpressionTrackingUrlIsMutable() {
          if (!impressionTrackingUrl_.isModifiable()) {
            impressionTrackingUrl_ = new com.google.protobuf.LazyStringArrayList(impressionTrackingUrl_);
          }
          bitField0_ |= 0x00000004;
        }
        /**
         * <pre>
         * 展现反馈地址
         * </pre>
         *
         * <code>repeated string impression_tracking_url = 6;</code>
         * @return A list containing the impressionTrackingUrl.
         */
        public com.google.protobuf.ProtocolStringList
            getImpressionTrackingUrlList() {
          impressionTrackingUrl_.makeImmutable();
          return impressionTrackingUrl_;
        }
        /**
         * <pre>
         * 展现反馈地址
         * </pre>
         *
         * <code>repeated string impression_tracking_url = 6;</code>
         * @return The count of impressionTrackingUrl.
         */
        public int getImpressionTrackingUrlCount() {
          return impressionTrackingUrl_.size();
        }
        /**
         * <pre>
         * 展现反馈地址
         * </pre>
         *
         * <code>repeated string impression_tracking_url = 6;</code>
         * @param index The index of the element to return.
         * @return The impressionTrackingUrl at the given index.
         */
        public java.lang.String getImpressionTrackingUrl(int index) {
          return impressionTrackingUrl_.get(index);
        }
        /**
         * <pre>
         * 展现反馈地址
         * </pre>
         *
         * <code>repeated string impression_tracking_url = 6;</code>
         * @param index The index of the value to return.
         * @return The bytes of the impressionTrackingUrl at the given index.
         */
        public com.google.protobuf.ByteString
            getImpressionTrackingUrlBytes(int index) {
          return impressionTrackingUrl_.getByteString(index);
        }
        /**
         * <pre>
         * 展现反馈地址
         * </pre>
         *
         * <code>repeated string impression_tracking_url = 6;</code>
         * @param index The index to set the value at.
         * @param value The impressionTrackingUrl to set.
         * @return This builder for chaining.
         */
        public Builder setImpressionTrackingUrl(
            int index, java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureImpressionTrackingUrlIsMutable();
          impressionTrackingUrl_.set(index, value);
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 展现反馈地址
         * </pre>
         *
         * <code>repeated string impression_tracking_url = 6;</code>
         * @param value The impressionTrackingUrl to add.
         * @return This builder for chaining.
         */
        public Builder addImpressionTrackingUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureImpressionTrackingUrlIsMutable();
          impressionTrackingUrl_.add(value);
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 展现反馈地址
         * </pre>
         *
         * <code>repeated string impression_tracking_url = 6;</code>
         * @param values The impressionTrackingUrl to add.
         * @return This builder for chaining.
         */
        public Builder addAllImpressionTrackingUrl(
            java.lang.Iterable<java.lang.String> values) {
          ensureImpressionTrackingUrlIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, impressionTrackingUrl_);
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 展现反馈地址
         * </pre>
         *
         * <code>repeated string impression_tracking_url = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearImpressionTrackingUrl() {
          impressionTrackingUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 展现反馈地址
         * </pre>
         *
         * <code>repeated string impression_tracking_url = 6;</code>
         * @param value The bytes of the impressionTrackingUrl to add.
         * @return This builder for chaining.
         */
        public Builder addImpressionTrackingUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureImpressionTrackingUrlIsMutable();
          impressionTrackingUrl_.add(value);
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }

        private java.lang.Object clickThroughUrl_ = "";
        /**
         * <pre>
         * 点击跳转地址(落地页)
         * </pre>
         *
         * <code>optional string click_through_url = 7;</code>
         * @return Whether the clickThroughUrl field is set.
         */
        public boolean hasClickThroughUrl() {
          return ((bitField0_ & 0x00000008) != 0);
        }
        /**
         * <pre>
         * 点击跳转地址(落地页)
         * </pre>
         *
         * <code>optional string click_through_url = 7;</code>
         * @return The clickThroughUrl.
         */
        public java.lang.String getClickThroughUrl() {
          java.lang.Object ref = clickThroughUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            clickThroughUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 点击跳转地址(落地页)
         * </pre>
         *
         * <code>optional string click_through_url = 7;</code>
         * @return The bytes for clickThroughUrl.
         */
        public com.google.protobuf.ByteString
            getClickThroughUrlBytes() {
          java.lang.Object ref = clickThroughUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            clickThroughUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 点击跳转地址(落地页)
         * </pre>
         *
         * <code>optional string click_through_url = 7;</code>
         * @param value The clickThroughUrl to set.
         * @return This builder for chaining.
         */
        public Builder setClickThroughUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          clickThroughUrl_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 点击跳转地址(落地页)
         * </pre>
         *
         * <code>optional string click_through_url = 7;</code>
         * @return This builder for chaining.
         */
        public Builder clearClickThroughUrl() {
          clickThroughUrl_ = getDefaultInstance().getClickThroughUrl();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 点击跳转地址(落地页)
         * </pre>
         *
         * <code>optional string click_through_url = 7;</code>
         * @param value The bytes for clickThroughUrl to set.
         * @return This builder for chaining.
         */
        public Builder setClickThroughUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          clickThroughUrl_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList clickTrackingUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureClickTrackingUrlIsMutable() {
          if (!clickTrackingUrl_.isModifiable()) {
            clickTrackingUrl_ = new com.google.protobuf.LazyStringArrayList(clickTrackingUrl_);
          }
          bitField0_ |= 0x00000010;
        }
        /**
         * <pre>
         * 点击跟踪地址
         * </pre>
         *
         * <code>repeated string click_tracking_url = 8;</code>
         * @return A list containing the clickTrackingUrl.
         */
        public com.google.protobuf.ProtocolStringList
            getClickTrackingUrlList() {
          clickTrackingUrl_.makeImmutable();
          return clickTrackingUrl_;
        }
        /**
         * <pre>
         * 点击跟踪地址
         * </pre>
         *
         * <code>repeated string click_tracking_url = 8;</code>
         * @return The count of clickTrackingUrl.
         */
        public int getClickTrackingUrlCount() {
          return clickTrackingUrl_.size();
        }
        /**
         * <pre>
         * 点击跟踪地址
         * </pre>
         *
         * <code>repeated string click_tracking_url = 8;</code>
         * @param index The index of the element to return.
         * @return The clickTrackingUrl at the given index.
         */
        public java.lang.String getClickTrackingUrl(int index) {
          return clickTrackingUrl_.get(index);
        }
        /**
         * <pre>
         * 点击跟踪地址
         * </pre>
         *
         * <code>repeated string click_tracking_url = 8;</code>
         * @param index The index of the value to return.
         * @return The bytes of the clickTrackingUrl at the given index.
         */
        public com.google.protobuf.ByteString
            getClickTrackingUrlBytes(int index) {
          return clickTrackingUrl_.getByteString(index);
        }
        /**
         * <pre>
         * 点击跟踪地址
         * </pre>
         *
         * <code>repeated string click_tracking_url = 8;</code>
         * @param index The index to set the value at.
         * @param value The clickTrackingUrl to set.
         * @return This builder for chaining.
         */
        public Builder setClickTrackingUrl(
            int index, java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureClickTrackingUrlIsMutable();
          clickTrackingUrl_.set(index, value);
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 点击跟踪地址
         * </pre>
         *
         * <code>repeated string click_tracking_url = 8;</code>
         * @param value The clickTrackingUrl to add.
         * @return This builder for chaining.
         */
        public Builder addClickTrackingUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureClickTrackingUrlIsMutable();
          clickTrackingUrl_.add(value);
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 点击跟踪地址
         * </pre>
         *
         * <code>repeated string click_tracking_url = 8;</code>
         * @param values The clickTrackingUrl to add.
         * @return This builder for chaining.
         */
        public Builder addAllClickTrackingUrl(
            java.lang.Iterable<java.lang.String> values) {
          ensureClickTrackingUrlIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, clickTrackingUrl_);
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 点击跟踪地址
         * </pre>
         *
         * <code>repeated string click_tracking_url = 8;</code>
         * @return This builder for chaining.
         */
        public Builder clearClickTrackingUrl() {
          clickTrackingUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 点击跟踪地址
         * </pre>
         *
         * <code>repeated string click_tracking_url = 8;</code>
         * @param value The bytes of the clickTrackingUrl to add.
         * @return This builder for chaining.
         */
        public Builder addClickTrackingUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureClickTrackingUrlIsMutable();
          clickTrackingUrl_.add(value);
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }

        private cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd nativeAd_;
        private com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAdOrBuilder> nativeAdBuilder_;
        /**
         * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
         * @return Whether the nativeAd field is set.
         */
        public boolean hasNativeAd() {
          return ((bitField0_ & 0x00000020) != 0);
        }
        /**
         * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
         * @return The nativeAd.
         */
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd getNativeAd() {
          if (nativeAdBuilder_ == null) {
            return nativeAd_ == null ? cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.getDefaultInstance() : nativeAd_;
          } else {
            return nativeAdBuilder_.getMessage();
          }
        }
        /**
         * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
         */
        public Builder setNativeAd(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd value) {
          if (nativeAdBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            nativeAd_ = value;
          } else {
            nativeAdBuilder_.setMessage(value);
          }
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
         */
        public Builder setNativeAd(
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Builder builderForValue) {
          if (nativeAdBuilder_ == null) {
            nativeAd_ = builderForValue.build();
          } else {
            nativeAdBuilder_.setMessage(builderForValue.build());
          }
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
         */
        public Builder mergeNativeAd(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd value) {
          if (nativeAdBuilder_ == null) {
            if (((bitField0_ & 0x00000020) != 0) &&
              nativeAd_ != null &&
              nativeAd_ != cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.getDefaultInstance()) {
              getNativeAdBuilder().mergeFrom(value);
            } else {
              nativeAd_ = value;
            }
          } else {
            nativeAdBuilder_.mergeFrom(value);
          }
          if (nativeAd_ != null) {
            bitField0_ |= 0x00000020;
            onChanged();
          }
          return this;
        }
        /**
         * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
         */
        public Builder clearNativeAd() {
          bitField0_ = (bitField0_ & ~0x00000020);
          nativeAd_ = null;
          if (nativeAdBuilder_ != null) {
            nativeAdBuilder_.dispose();
            nativeAdBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
         */
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Builder getNativeAdBuilder() {
          bitField0_ |= 0x00000020;
          onChanged();
          return getNativeAdFieldBuilder().getBuilder();
        }
        /**
         * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
         */
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAdOrBuilder getNativeAdOrBuilder() {
          if (nativeAdBuilder_ != null) {
            return nativeAdBuilder_.getMessageOrBuilder();
          } else {
            return nativeAd_ == null ?
                cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.getDefaultInstance() : nativeAd_;
          }
        }
        /**
         * <code>optional .Response.Seat.Ad.NativeAd native_ad = 10;</code>
         */
        private com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAdOrBuilder> 
            getNativeAdFieldBuilder() {
          if (nativeAdBuilder_ == null) {
            nativeAdBuilder_ = new com.google.protobuf.SingleFieldBuilder<
                cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAd.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.NativeAdOrBuilder>(
                    getNativeAd(),
                    getParentForChildren(),
                    isClean());
            nativeAd_ = null;
          }
          return nativeAdBuilder_;
        }

        private java.lang.Object creativeId_ = "";
        /**
         * <pre>
         * 广告创意的唯一标识
         * </pre>
         *
         * <code>optional string creative_id = 11;</code>
         * @return Whether the creativeId field is set.
         */
        public boolean hasCreativeId() {
          return ((bitField0_ & 0x00000040) != 0);
        }
        /**
         * <pre>
         * 广告创意的唯一标识
         * </pre>
         *
         * <code>optional string creative_id = 11;</code>
         * @return The creativeId.
         */
        public java.lang.String getCreativeId() {
          java.lang.Object ref = creativeId_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            creativeId_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 广告创意的唯一标识
         * </pre>
         *
         * <code>optional string creative_id = 11;</code>
         * @return The bytes for creativeId.
         */
        public com.google.protobuf.ByteString
            getCreativeIdBytes() {
          java.lang.Object ref = creativeId_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            creativeId_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 广告创意的唯一标识
         * </pre>
         *
         * <code>optional string creative_id = 11;</code>
         * @param value The creativeId to set.
         * @return This builder for chaining.
         */
        public Builder setCreativeId(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          creativeId_ = value;
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 广告创意的唯一标识
         * </pre>
         *
         * <code>optional string creative_id = 11;</code>
         * @return This builder for chaining.
         */
        public Builder clearCreativeId() {
          creativeId_ = getDefaultInstance().getCreativeId();
          bitField0_ = (bitField0_ & ~0x00000040);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 广告创意的唯一标识
         * </pre>
         *
         * <code>optional string creative_id = 11;</code>
         * @param value The bytes for creativeId to set.
         * @return This builder for chaining.
         */
        public Builder setCreativeIdBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          creativeId_ = value;
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }

        private java.lang.Object adSource_ = "";
        /**
         * <pre>
         * 广告来源
         * </pre>
         *
         * <code>optional string ad_source = 12;</code>
         * @return Whether the adSource field is set.
         */
        public boolean hasAdSource() {
          return ((bitField0_ & 0x00000080) != 0);
        }
        /**
         * <pre>
         * 广告来源
         * </pre>
         *
         * <code>optional string ad_source = 12;</code>
         * @return The adSource.
         */
        public java.lang.String getAdSource() {
          java.lang.Object ref = adSource_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            adSource_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 广告来源
         * </pre>
         *
         * <code>optional string ad_source = 12;</code>
         * @return The bytes for adSource.
         */
        public com.google.protobuf.ByteString
            getAdSourceBytes() {
          java.lang.Object ref = adSource_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            adSource_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 广告来源
         * </pre>
         *
         * <code>optional string ad_source = 12;</code>
         * @param value The adSource to set.
         * @return This builder for chaining.
         */
        public Builder setAdSource(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          adSource_ = value;
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 广告来源
         * </pre>
         *
         * <code>optional string ad_source = 12;</code>
         * @return This builder for chaining.
         */
        public Builder clearAdSource() {
          adSource_ = getDefaultInstance().getAdSource();
          bitField0_ = (bitField0_ & ~0x00000080);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 广告来源
         * </pre>
         *
         * <code>optional string ad_source = 12;</code>
         * @param value The bytes for adSource to set.
         * @return This builder for chaining.
         */
        public Builder setAdSourceBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          adSource_ = value;
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }

        private java.lang.Object deeplinkUrl_ = "";
        /**
         * <pre>
         * APP唤醒地址
         * </pre>
         *
         * <code>optional string deeplink_url = 13;</code>
         * @return Whether the deeplinkUrl field is set.
         */
        public boolean hasDeeplinkUrl() {
          return ((bitField0_ & 0x00000100) != 0);
        }
        /**
         * <pre>
         * APP唤醒地址
         * </pre>
         *
         * <code>optional string deeplink_url = 13;</code>
         * @return The deeplinkUrl.
         */
        public java.lang.String getDeeplinkUrl() {
          java.lang.Object ref = deeplinkUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            deeplinkUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * APP唤醒地址
         * </pre>
         *
         * <code>optional string deeplink_url = 13;</code>
         * @return The bytes for deeplinkUrl.
         */
        public com.google.protobuf.ByteString
            getDeeplinkUrlBytes() {
          java.lang.Object ref = deeplinkUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            deeplinkUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * APP唤醒地址
         * </pre>
         *
         * <code>optional string deeplink_url = 13;</code>
         * @param value The deeplinkUrl to set.
         * @return This builder for chaining.
         */
        public Builder setDeeplinkUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          deeplinkUrl_ = value;
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * APP唤醒地址
         * </pre>
         *
         * <code>optional string deeplink_url = 13;</code>
         * @return This builder for chaining.
         */
        public Builder clearDeeplinkUrl() {
          deeplinkUrl_ = getDefaultInstance().getDeeplinkUrl();
          bitField0_ = (bitField0_ & ~0x00000100);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * APP唤醒地址
         * </pre>
         *
         * <code>optional string deeplink_url = 13;</code>
         * @param value The bytes for deeplinkUrl to set.
         * @return This builder for chaining.
         */
        public Builder setDeeplinkUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          deeplinkUrl_ = value;
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }

        private java.lang.Object downloadUrl_ = "";
        /**
         * <pre>
         * APP下载地址
         * </pre>
         *
         * <code>optional string download_url = 14;</code>
         * @return Whether the downloadUrl field is set.
         */
        public boolean hasDownloadUrl() {
          return ((bitField0_ & 0x00000200) != 0);
        }
        /**
         * <pre>
         * APP下载地址
         * </pre>
         *
         * <code>optional string download_url = 14;</code>
         * @return The downloadUrl.
         */
        public java.lang.String getDownloadUrl() {
          java.lang.Object ref = downloadUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            downloadUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * APP下载地址
         * </pre>
         *
         * <code>optional string download_url = 14;</code>
         * @return The bytes for downloadUrl.
         */
        public com.google.protobuf.ByteString
            getDownloadUrlBytes() {
          java.lang.Object ref = downloadUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            downloadUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * APP下载地址
         * </pre>
         *
         * <code>optional string download_url = 14;</code>
         * @param value The downloadUrl to set.
         * @return This builder for chaining.
         */
        public Builder setDownloadUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          downloadUrl_ = value;
          bitField0_ |= 0x00000200;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * APP下载地址
         * </pre>
         *
         * <code>optional string download_url = 14;</code>
         * @return This builder for chaining.
         */
        public Builder clearDownloadUrl() {
          downloadUrl_ = getDefaultInstance().getDownloadUrl();
          bitField0_ = (bitField0_ & ~0x00000200);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * APP下载地址
         * </pre>
         *
         * <code>optional string download_url = 14;</code>
         * @param value The bytes for downloadUrl to set.
         * @return This builder for chaining.
         */
        public Builder setDownloadUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          downloadUrl_ = value;
          bitField0_ |= 0x00000200;
          onChanged();
          return this;
        }

        private int bidPrice_ ;
        /**
         * <pre>
         * 返回报价供上游adx竞价, 单位(分)
         * </pre>
         *
         * <code>optional int32 bid_price = 15;</code>
         * @return Whether the bidPrice field is set.
         */
        @java.lang.Override
        public boolean hasBidPrice() {
          return ((bitField0_ & 0x00000400) != 0);
        }
        /**
         * <pre>
         * 返回报价供上游adx竞价, 单位(分)
         * </pre>
         *
         * <code>optional int32 bid_price = 15;</code>
         * @return The bidPrice.
         */
        @java.lang.Override
        public int getBidPrice() {
          return bidPrice_;
        }
        /**
         * <pre>
         * 返回报价供上游adx竞价, 单位(分)
         * </pre>
         *
         * <code>optional int32 bid_price = 15;</code>
         * @param value The bidPrice to set.
         * @return This builder for chaining.
         */
        public Builder setBidPrice(int value) {

          bidPrice_ = value;
          bitField0_ |= 0x00000400;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 返回报价供上游adx竞价, 单位(分)
         * </pre>
         *
         * <code>optional int32 bid_price = 15;</code>
         * @return This builder for chaining.
         */
        public Builder clearBidPrice() {
          bitField0_ = (bitField0_ & ~0x00000400);
          bidPrice_ = 0;
          onChanged();
          return this;
        }

        private java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack> eventTrack_ =
          java.util.Collections.emptyList();
        private void ensureEventTrackIsMutable() {
          if (!((bitField0_ & 0x00000800) != 0)) {
            eventTrack_ = new java.util.ArrayList<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack>(eventTrack_);
            bitField0_ |= 0x00000800;
           }
        }

        private com.google.protobuf.RepeatedFieldBuilder<
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder> eventTrackBuilder_;

        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack> getEventTrackList() {
          if (eventTrackBuilder_ == null) {
            return java.util.Collections.unmodifiableList(eventTrack_);
          } else {
            return eventTrackBuilder_.getMessageList();
          }
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public int getEventTrackCount() {
          if (eventTrackBuilder_ == null) {
            return eventTrack_.size();
          } else {
            return eventTrackBuilder_.getCount();
          }
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack getEventTrack(int index) {
          if (eventTrackBuilder_ == null) {
            return eventTrack_.get(index);
          } else {
            return eventTrackBuilder_.getMessage(index);
          }
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public Builder setEventTrack(
            int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack value) {
          if (eventTrackBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureEventTrackIsMutable();
            eventTrack_.set(index, value);
            onChanged();
          } else {
            eventTrackBuilder_.setMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public Builder setEventTrack(
            int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder builderForValue) {
          if (eventTrackBuilder_ == null) {
            ensureEventTrackIsMutable();
            eventTrack_.set(index, builderForValue.build());
            onChanged();
          } else {
            eventTrackBuilder_.setMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public Builder addEventTrack(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack value) {
          if (eventTrackBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureEventTrackIsMutable();
            eventTrack_.add(value);
            onChanged();
          } else {
            eventTrackBuilder_.addMessage(value);
          }
          return this;
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public Builder addEventTrack(
            int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack value) {
          if (eventTrackBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureEventTrackIsMutable();
            eventTrack_.add(index, value);
            onChanged();
          } else {
            eventTrackBuilder_.addMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public Builder addEventTrack(
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder builderForValue) {
          if (eventTrackBuilder_ == null) {
            ensureEventTrackIsMutable();
            eventTrack_.add(builderForValue.build());
            onChanged();
          } else {
            eventTrackBuilder_.addMessage(builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public Builder addEventTrack(
            int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder builderForValue) {
          if (eventTrackBuilder_ == null) {
            ensureEventTrackIsMutable();
            eventTrack_.add(index, builderForValue.build());
            onChanged();
          } else {
            eventTrackBuilder_.addMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public Builder addAllEventTrack(
            java.lang.Iterable<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack> values) {
          if (eventTrackBuilder_ == null) {
            ensureEventTrackIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, eventTrack_);
            onChanged();
          } else {
            eventTrackBuilder_.addAllMessages(values);
          }
          return this;
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public Builder clearEventTrack() {
          if (eventTrackBuilder_ == null) {
            eventTrack_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000800);
            onChanged();
          } else {
            eventTrackBuilder_.clear();
          }
          return this;
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public Builder removeEventTrack(int index) {
          if (eventTrackBuilder_ == null) {
            ensureEventTrackIsMutable();
            eventTrack_.remove(index);
            onChanged();
          } else {
            eventTrackBuilder_.remove(index);
          }
          return this;
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder getEventTrackBuilder(
            int index) {
          return getEventTrackFieldBuilder().getBuilder(index);
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder getEventTrackOrBuilder(
            int index) {
          if (eventTrackBuilder_ == null) {
            return eventTrack_.get(index);  } else {
            return eventTrackBuilder_.getMessageOrBuilder(index);
          }
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder> 
             getEventTrackOrBuilderList() {
          if (eventTrackBuilder_ != null) {
            return eventTrackBuilder_.getMessageOrBuilderList();
          } else {
            return java.util.Collections.unmodifiableList(eventTrack_);
          }
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder addEventTrackBuilder() {
          return getEventTrackFieldBuilder().addBuilder(
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.getDefaultInstance());
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder addEventTrackBuilder(
            int index) {
          return getEventTrackFieldBuilder().addBuilder(
              index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.getDefaultInstance());
        }
        /**
         * <code>repeated .Response.Seat.Ad.EventTrack event_track = 16;</code>
         */
        public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder> 
             getEventTrackBuilderList() {
          return getEventTrackFieldBuilder().getBuilderList();
        }
        private com.google.protobuf.RepeatedFieldBuilder<
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder> 
            getEventTrackFieldBuilder() {
          if (eventTrackBuilder_ == null) {
            eventTrackBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
                cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrack.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.EventTrackOrBuilder>(
                    eventTrack_,
                    ((bitField0_ & 0x00000800) != 0),
                    getParentForChildren(),
                    isClean());
            eventTrack_ = null;
          }
          return eventTrackBuilder_;
        }

        private int openType_ ;
        /**
         * <pre>
         * 落地页打开方式：1是打开网页(包含deeplink) 2 点击下载
         * </pre>
         *
         * <code>optional int32 open_type = 17;</code>
         * @return Whether the openType field is set.
         */
        @java.lang.Override
        public boolean hasOpenType() {
          return ((bitField0_ & 0x00001000) != 0);
        }
        /**
         * <pre>
         * 落地页打开方式：1是打开网页(包含deeplink) 2 点击下载
         * </pre>
         *
         * <code>optional int32 open_type = 17;</code>
         * @return The openType.
         */
        @java.lang.Override
        public int getOpenType() {
          return openType_;
        }
        /**
         * <pre>
         * 落地页打开方式：1是打开网页(包含deeplink) 2 点击下载
         * </pre>
         *
         * <code>optional int32 open_type = 17;</code>
         * @param value The openType to set.
         * @return This builder for chaining.
         */
        public Builder setOpenType(int value) {

          openType_ = value;
          bitField0_ |= 0x00001000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 落地页打开方式：1是打开网页(包含deeplink) 2 点击下载
         * </pre>
         *
         * <code>optional int32 open_type = 17;</code>
         * @return This builder for chaining.
         */
        public Builder clearOpenType() {
          bitField0_ = (bitField0_ & ~0x00001000);
          openType_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object winnoticeUrl_ = "";
        /**
         * <pre>
         * 竞价成功通知，服务端发送
         * </pre>
         *
         * <code>optional string winnotice_url = 18;</code>
         * @return Whether the winnoticeUrl field is set.
         */
        public boolean hasWinnoticeUrl() {
          return ((bitField0_ & 0x00002000) != 0);
        }
        /**
         * <pre>
         * 竞价成功通知，服务端发送
         * </pre>
         *
         * <code>optional string winnotice_url = 18;</code>
         * @return The winnoticeUrl.
         */
        public java.lang.String getWinnoticeUrl() {
          java.lang.Object ref = winnoticeUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            winnoticeUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 竞价成功通知，服务端发送
         * </pre>
         *
         * <code>optional string winnotice_url = 18;</code>
         * @return The bytes for winnoticeUrl.
         */
        public com.google.protobuf.ByteString
            getWinnoticeUrlBytes() {
          java.lang.Object ref = winnoticeUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            winnoticeUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 竞价成功通知，服务端发送
         * </pre>
         *
         * <code>optional string winnotice_url = 18;</code>
         * @param value The winnoticeUrl to set.
         * @return This builder for chaining.
         */
        public Builder setWinnoticeUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          winnoticeUrl_ = value;
          bitField0_ |= 0x00002000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 竞价成功通知，服务端发送
         * </pre>
         *
         * <code>optional string winnotice_url = 18;</code>
         * @return This builder for chaining.
         */
        public Builder clearWinnoticeUrl() {
          winnoticeUrl_ = getDefaultInstance().getWinnoticeUrl();
          bitField0_ = (bitField0_ & ~0x00002000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 竞价成功通知，服务端发送
         * </pre>
         *
         * <code>optional string winnotice_url = 18;</code>
         * @param value The bytes for winnoticeUrl to set.
         * @return This builder for chaining.
         */
        public Builder setWinnoticeUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          winnoticeUrl_ = value;
          bitField0_ |= 0x00002000;
          onChanged();
          return this;
        }

        private java.lang.Object packageName_ = "";
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>optional string package_name = 19;</code>
         * @return Whether the packageName field is set.
         */
        public boolean hasPackageName() {
          return ((bitField0_ & 0x00004000) != 0);
        }
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>optional string package_name = 19;</code>
         * @return The packageName.
         */
        public java.lang.String getPackageName() {
          java.lang.Object ref = packageName_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            packageName_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>optional string package_name = 19;</code>
         * @return The bytes for packageName.
         */
        public com.google.protobuf.ByteString
            getPackageNameBytes() {
          java.lang.Object ref = packageName_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            packageName_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>optional string package_name = 19;</code>
         * @param value The packageName to set.
         * @return This builder for chaining.
         */
        public Builder setPackageName(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          packageName_ = value;
          bitField0_ |= 0x00004000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>optional string package_name = 19;</code>
         * @return This builder for chaining.
         */
        public Builder clearPackageName() {
          packageName_ = getDefaultInstance().getPackageName();
          bitField0_ = (bitField0_ & ~0x00004000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>optional string package_name = 19;</code>
         * @param value The bytes for packageName to set.
         * @return This builder for chaining.
         */
        public Builder setPackageNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          packageName_ = value;
          bitField0_ |= 0x00004000;
          onChanged();
          return this;
        }

        private java.lang.Object appName_ = "";
        /**
         * <pre>
         * 应用名
         * </pre>
         *
         * <code>optional string app_name = 20;</code>
         * @return Whether the appName field is set.
         */
        public boolean hasAppName() {
          return ((bitField0_ & 0x00008000) != 0);
        }
        /**
         * <pre>
         * 应用名
         * </pre>
         *
         * <code>optional string app_name = 20;</code>
         * @return The appName.
         */
        public java.lang.String getAppName() {
          java.lang.Object ref = appName_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            appName_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 应用名
         * </pre>
         *
         * <code>optional string app_name = 20;</code>
         * @return The bytes for appName.
         */
        public com.google.protobuf.ByteString
            getAppNameBytes() {
          java.lang.Object ref = appName_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            appName_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 应用名
         * </pre>
         *
         * <code>optional string app_name = 20;</code>
         * @param value The appName to set.
         * @return This builder for chaining.
         */
        public Builder setAppName(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          appName_ = value;
          bitField0_ |= 0x00008000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用名
         * </pre>
         *
         * <code>optional string app_name = 20;</code>
         * @return This builder for chaining.
         */
        public Builder clearAppName() {
          appName_ = getDefaultInstance().getAppName();
          bitField0_ = (bitField0_ & ~0x00008000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用名
         * </pre>
         *
         * <code>optional string app_name = 20;</code>
         * @param value The bytes for appName to set.
         * @return This builder for chaining.
         */
        public Builder setAppNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          appName_ = value;
          bitField0_ |= 0x00008000;
          onChanged();
          return this;
        }

        private java.lang.Object appDesc_ = "";
        /**
         * <pre>
         * *
         * 下载类相关信息
         * </pre>
         *
         * <code>optional string app_desc = 21;</code>
         * @return Whether the appDesc field is set.
         */
        public boolean hasAppDesc() {
          return ((bitField0_ & 0x00010000) != 0);
        }
        /**
         * <pre>
         * *
         * 下载类相关信息
         * </pre>
         *
         * <code>optional string app_desc = 21;</code>
         * @return The appDesc.
         */
        public java.lang.String getAppDesc() {
          java.lang.Object ref = appDesc_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            appDesc_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * *
         * 下载类相关信息
         * </pre>
         *
         * <code>optional string app_desc = 21;</code>
         * @return The bytes for appDesc.
         */
        public com.google.protobuf.ByteString
            getAppDescBytes() {
          java.lang.Object ref = appDesc_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            appDesc_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * *
         * 下载类相关信息
         * </pre>
         *
         * <code>optional string app_desc = 21;</code>
         * @param value The appDesc to set.
         * @return This builder for chaining.
         */
        public Builder setAppDesc(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          appDesc_ = value;
          bitField0_ |= 0x00010000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * *
         * 下载类相关信息
         * </pre>
         *
         * <code>optional string app_desc = 21;</code>
         * @return This builder for chaining.
         */
        public Builder clearAppDesc() {
          appDesc_ = getDefaultInstance().getAppDesc();
          bitField0_ = (bitField0_ & ~0x00010000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * *
         * 下载类相关信息
         * </pre>
         *
         * <code>optional string app_desc = 21;</code>
         * @param value The bytes for appDesc to set.
         * @return This builder for chaining.
         */
        public Builder setAppDescBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          appDesc_ = value;
          bitField0_ |= 0x00010000;
          onChanged();
          return this;
        }

        private java.lang.Object appDownloadUrl_ = "";
        /**
         * <pre>
         * 下载地址
         * </pre>
         *
         * <code>optional string app_download_url = 22;</code>
         * @return Whether the appDownloadUrl field is set.
         */
        public boolean hasAppDownloadUrl() {
          return ((bitField0_ & 0x00020000) != 0);
        }
        /**
         * <pre>
         * 下载地址
         * </pre>
         *
         * <code>optional string app_download_url = 22;</code>
         * @return The appDownloadUrl.
         */
        public java.lang.String getAppDownloadUrl() {
          java.lang.Object ref = appDownloadUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            appDownloadUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 下载地址
         * </pre>
         *
         * <code>optional string app_download_url = 22;</code>
         * @return The bytes for appDownloadUrl.
         */
        public com.google.protobuf.ByteString
            getAppDownloadUrlBytes() {
          java.lang.Object ref = appDownloadUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            appDownloadUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 下载地址
         * </pre>
         *
         * <code>optional string app_download_url = 22;</code>
         * @param value The appDownloadUrl to set.
         * @return This builder for chaining.
         */
        public Builder setAppDownloadUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          appDownloadUrl_ = value;
          bitField0_ |= 0x00020000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 下载地址
         * </pre>
         *
         * <code>optional string app_download_url = 22;</code>
         * @return This builder for chaining.
         */
        public Builder clearAppDownloadUrl() {
          appDownloadUrl_ = getDefaultInstance().getAppDownloadUrl();
          bitField0_ = (bitField0_ & ~0x00020000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 下载地址
         * </pre>
         *
         * <code>optional string app_download_url = 22;</code>
         * @param value The bytes for appDownloadUrl to set.
         * @return This builder for chaining.
         */
        public Builder setAppDownloadUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          appDownloadUrl_ = value;
          bitField0_ |= 0x00020000;
          onChanged();
          return this;
        }

        private java.lang.Object permissionsUrl_ = "";
        /**
         * <pre>
         * 权限名称及权限描述列表
         * </pre>
         *
         * <code>optional string permissions_url = 23;</code>
         * @return Whether the permissionsUrl field is set.
         */
        public boolean hasPermissionsUrl() {
          return ((bitField0_ & 0x00040000) != 0);
        }
        /**
         * <pre>
         * 权限名称及权限描述列表
         * </pre>
         *
         * <code>optional string permissions_url = 23;</code>
         * @return The permissionsUrl.
         */
        public java.lang.String getPermissionsUrl() {
          java.lang.Object ref = permissionsUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            permissionsUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 权限名称及权限描述列表
         * </pre>
         *
         * <code>optional string permissions_url = 23;</code>
         * @return The bytes for permissionsUrl.
         */
        public com.google.protobuf.ByteString
            getPermissionsUrlBytes() {
          java.lang.Object ref = permissionsUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            permissionsUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 权限名称及权限描述列表
         * </pre>
         *
         * <code>optional string permissions_url = 23;</code>
         * @param value The permissionsUrl to set.
         * @return This builder for chaining.
         */
        public Builder setPermissionsUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          permissionsUrl_ = value;
          bitField0_ |= 0x00040000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 权限名称及权限描述列表
         * </pre>
         *
         * <code>optional string permissions_url = 23;</code>
         * @return This builder for chaining.
         */
        public Builder clearPermissionsUrl() {
          permissionsUrl_ = getDefaultInstance().getPermissionsUrl();
          bitField0_ = (bitField0_ & ~0x00040000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 权限名称及权限描述列表
         * </pre>
         *
         * <code>optional string permissions_url = 23;</code>
         * @param value The bytes for permissionsUrl to set.
         * @return This builder for chaining.
         */
        public Builder setPermissionsUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          permissionsUrl_ = value;
          bitField0_ |= 0x00040000;
          onChanged();
          return this;
        }

        private java.lang.Object functionDescUrl_ = "";
        /**
         * <pre>
         * 产品功能url
         * </pre>
         *
         * <code>optional string function_desc_url = 24;</code>
         * @return Whether the functionDescUrl field is set.
         */
        public boolean hasFunctionDescUrl() {
          return ((bitField0_ & 0x00080000) != 0);
        }
        /**
         * <pre>
         * 产品功能url
         * </pre>
         *
         * <code>optional string function_desc_url = 24;</code>
         * @return The functionDescUrl.
         */
        public java.lang.String getFunctionDescUrl() {
          java.lang.Object ref = functionDescUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            functionDescUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 产品功能url
         * </pre>
         *
         * <code>optional string function_desc_url = 24;</code>
         * @return The bytes for functionDescUrl.
         */
        public com.google.protobuf.ByteString
            getFunctionDescUrlBytes() {
          java.lang.Object ref = functionDescUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            functionDescUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 产品功能url
         * </pre>
         *
         * <code>optional string function_desc_url = 24;</code>
         * @param value The functionDescUrl to set.
         * @return This builder for chaining.
         */
        public Builder setFunctionDescUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          functionDescUrl_ = value;
          bitField0_ |= 0x00080000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 产品功能url
         * </pre>
         *
         * <code>optional string function_desc_url = 24;</code>
         * @return This builder for chaining.
         */
        public Builder clearFunctionDescUrl() {
          functionDescUrl_ = getDefaultInstance().getFunctionDescUrl();
          bitField0_ = (bitField0_ & ~0x00080000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 产品功能url
         * </pre>
         *
         * <code>optional string function_desc_url = 24;</code>
         * @param value The bytes for functionDescUrl to set.
         * @return This builder for chaining.
         */
        public Builder setFunctionDescUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          functionDescUrl_ = value;
          bitField0_ |= 0x00080000;
          onChanged();
          return this;
        }

        private java.lang.Object privacyUrl_ = "";
        /**
         * <pre>
         * 隐私协议
         * </pre>
         *
         * <code>optional string privacy_url = 25;</code>
         * @return Whether the privacyUrl field is set.
         */
        public boolean hasPrivacyUrl() {
          return ((bitField0_ & 0x00100000) != 0);
        }
        /**
         * <pre>
         * 隐私协议
         * </pre>
         *
         * <code>optional string privacy_url = 25;</code>
         * @return The privacyUrl.
         */
        public java.lang.String getPrivacyUrl() {
          java.lang.Object ref = privacyUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            privacyUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 隐私协议
         * </pre>
         *
         * <code>optional string privacy_url = 25;</code>
         * @return The bytes for privacyUrl.
         */
        public com.google.protobuf.ByteString
            getPrivacyUrlBytes() {
          java.lang.Object ref = privacyUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            privacyUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 隐私协议
         * </pre>
         *
         * <code>optional string privacy_url = 25;</code>
         * @param value The privacyUrl to set.
         * @return This builder for chaining.
         */
        public Builder setPrivacyUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          privacyUrl_ = value;
          bitField0_ |= 0x00100000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 隐私协议
         * </pre>
         *
         * <code>optional string privacy_url = 25;</code>
         * @return This builder for chaining.
         */
        public Builder clearPrivacyUrl() {
          privacyUrl_ = getDefaultInstance().getPrivacyUrl();
          bitField0_ = (bitField0_ & ~0x00100000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 隐私协议
         * </pre>
         *
         * <code>optional string privacy_url = 25;</code>
         * @param value The bytes for privacyUrl to set.
         * @return This builder for chaining.
         */
        public Builder setPrivacyUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          privacyUrl_ = value;
          bitField0_ |= 0x00100000;
          onChanged();
          return this;
        }

        private java.lang.Object developerName_ = "";
        /**
         * <pre>
         * 开发者公司名称
         * </pre>
         *
         * <code>optional string developer_name = 26;</code>
         * @return Whether the developerName field is set.
         */
        public boolean hasDeveloperName() {
          return ((bitField0_ & 0x00200000) != 0);
        }
        /**
         * <pre>
         * 开发者公司名称
         * </pre>
         *
         * <code>optional string developer_name = 26;</code>
         * @return The developerName.
         */
        public java.lang.String getDeveloperName() {
          java.lang.Object ref = developerName_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            developerName_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 开发者公司名称
         * </pre>
         *
         * <code>optional string developer_name = 26;</code>
         * @return The bytes for developerName.
         */
        public com.google.protobuf.ByteString
            getDeveloperNameBytes() {
          java.lang.Object ref = developerName_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            developerName_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 开发者公司名称
         * </pre>
         *
         * <code>optional string developer_name = 26;</code>
         * @param value The developerName to set.
         * @return This builder for chaining.
         */
        public Builder setDeveloperName(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          developerName_ = value;
          bitField0_ |= 0x00200000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 开发者公司名称
         * </pre>
         *
         * <code>optional string developer_name = 26;</code>
         * @return This builder for chaining.
         */
        public Builder clearDeveloperName() {
          developerName_ = getDefaultInstance().getDeveloperName();
          bitField0_ = (bitField0_ & ~0x00200000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 开发者公司名称
         * </pre>
         *
         * <code>optional string developer_name = 26;</code>
         * @param value The bytes for developerName to set.
         * @return This builder for chaining.
         */
        public Builder setDeveloperNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          developerName_ = value;
          bitField0_ |= 0x00200000;
          onChanged();
          return this;
        }

        private java.lang.Object appVersion_ = "";
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string app_version = 27;</code>
         * @return Whether the appVersion field is set.
         */
        public boolean hasAppVersion() {
          return ((bitField0_ & 0x00400000) != 0);
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string app_version = 27;</code>
         * @return The appVersion.
         */
        public java.lang.String getAppVersion() {
          java.lang.Object ref = appVersion_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            appVersion_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string app_version = 27;</code>
         * @return The bytes for appVersion.
         */
        public com.google.protobuf.ByteString
            getAppVersionBytes() {
          java.lang.Object ref = appVersion_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            appVersion_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string app_version = 27;</code>
         * @param value The appVersion to set.
         * @return This builder for chaining.
         */
        public Builder setAppVersion(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          appVersion_ = value;
          bitField0_ |= 0x00400000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string app_version = 27;</code>
         * @return This builder for chaining.
         */
        public Builder clearAppVersion() {
          appVersion_ = getDefaultInstance().getAppVersion();
          bitField0_ = (bitField0_ & ~0x00400000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string app_version = 27;</code>
         * @param value The bytes for appVersion to set.
         * @return This builder for chaining.
         */
        public Builder setAppVersionBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          appVersion_ = value;
          bitField0_ |= 0x00400000;
          onChanged();
          return this;
        }

        private java.lang.Object appIconUrl_ = "";
        /**
         * <pre>
         * App图标链接
         * </pre>
         *
         * <code>optional string app_icon_url = 28;</code>
         * @return Whether the appIconUrl field is set.
         */
        public boolean hasAppIconUrl() {
          return ((bitField0_ & 0x00800000) != 0);
        }
        /**
         * <pre>
         * App图标链接
         * </pre>
         *
         * <code>optional string app_icon_url = 28;</code>
         * @return The appIconUrl.
         */
        public java.lang.String getAppIconUrl() {
          java.lang.Object ref = appIconUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            appIconUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * App图标链接
         * </pre>
         *
         * <code>optional string app_icon_url = 28;</code>
         * @return The bytes for appIconUrl.
         */
        public com.google.protobuf.ByteString
            getAppIconUrlBytes() {
          java.lang.Object ref = appIconUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            appIconUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * App图标链接
         * </pre>
         *
         * <code>optional string app_icon_url = 28;</code>
         * @param value The appIconUrl to set.
         * @return This builder for chaining.
         */
        public Builder setAppIconUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          appIconUrl_ = value;
          bitField0_ |= 0x00800000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * App图标链接
         * </pre>
         *
         * <code>optional string app_icon_url = 28;</code>
         * @return This builder for chaining.
         */
        public Builder clearAppIconUrl() {
          appIconUrl_ = getDefaultInstance().getAppIconUrl();
          bitField0_ = (bitField0_ & ~0x00800000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * App图标链接
         * </pre>
         *
         * <code>optional string app_icon_url = 28;</code>
         * @param value The bytes for appIconUrl to set.
         * @return This builder for chaining.
         */
        public Builder setAppIconUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          appIconUrl_ = value;
          bitField0_ |= 0x00800000;
          onChanged();
          return this;
        }

        private long appSize_ ;
        /**
         * <pre>
         * app大小
         * </pre>
         *
         * <code>optional int64 app_size = 29;</code>
         * @return Whether the appSize field is set.
         */
        @java.lang.Override
        public boolean hasAppSize() {
          return ((bitField0_ & 0x01000000) != 0);
        }
        /**
         * <pre>
         * app大小
         * </pre>
         *
         * <code>optional int64 app_size = 29;</code>
         * @return The appSize.
         */
        @java.lang.Override
        public long getAppSize() {
          return appSize_;
        }
        /**
         * <pre>
         * app大小
         * </pre>
         *
         * <code>optional int64 app_size = 29;</code>
         * @param value The appSize to set.
         * @return This builder for chaining.
         */
        public Builder setAppSize(long value) {

          appSize_ = value;
          bitField0_ |= 0x01000000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * app大小
         * </pre>
         *
         * <code>optional int64 app_size = 29;</code>
         * @return This builder for chaining.
         */
        public Builder clearAppSize() {
          bitField0_ = (bitField0_ & ~0x01000000);
          appSize_ = 0L;
          onChanged();
          return this;
        }

        private java.lang.Object fileMd5_ = "";
        /**
         * <pre>
         * app文件md5
         * </pre>
         *
         * <code>optional string file_md5 = 30;</code>
         * @return Whether the fileMd5 field is set.
         */
        public boolean hasFileMd5() {
          return ((bitField0_ & 0x02000000) != 0);
        }
        /**
         * <pre>
         * app文件md5
         * </pre>
         *
         * <code>optional string file_md5 = 30;</code>
         * @return The fileMd5.
         */
        public java.lang.String getFileMd5() {
          java.lang.Object ref = fileMd5_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            fileMd5_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * app文件md5
         * </pre>
         *
         * <code>optional string file_md5 = 30;</code>
         * @return The bytes for fileMd5.
         */
        public com.google.protobuf.ByteString
            getFileMd5Bytes() {
          java.lang.Object ref = fileMd5_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            fileMd5_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * app文件md5
         * </pre>
         *
         * <code>optional string file_md5 = 30;</code>
         * @param value The fileMd5 to set.
         * @return This builder for chaining.
         */
        public Builder setFileMd5(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          fileMd5_ = value;
          bitField0_ |= 0x02000000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * app文件md5
         * </pre>
         *
         * <code>optional string file_md5 = 30;</code>
         * @return This builder for chaining.
         */
        public Builder clearFileMd5() {
          fileMd5_ = getDefaultInstance().getFileMd5();
          bitField0_ = (bitField0_ & ~0x02000000);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * app文件md5
         * </pre>
         *
         * <code>optional string file_md5 = 30;</code>
         * @param value The bytes for fileMd5 to set.
         * @return This builder for chaining.
         */
        public Builder setFileMd5Bytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          fileMd5_ = value;
          bitField0_ |= 0x02000000;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:Response.Seat.Ad)
      }

      // @@protoc_insertion_point(class_scope:Response.Seat.Ad)
      private static final cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad();
      }

      public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Ad>
          PARSER = new com.google.protobuf.AbstractParser<Ad>() {
        @java.lang.Override
        public Ad parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Ad> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Ad> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    private int bitField0_;
    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <pre>
     * 指定请求里的impression id
     * </pre>
     *
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 指定请求里的impression id
     * </pre>
     *
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int AD_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad> ad_;
    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    @java.lang.Override
    public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad> getAdList() {
      return ad_;
    }
    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder> 
        getAdOrBuilderList() {
      return ad_;
    }
    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    @java.lang.Override
    public int getAdCount() {
      return ad_.size();
    }
    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad getAd(int index) {
      return ad_.get(index);
    }
    /**
     * <code>repeated .Response.Seat.Ad ad = 2;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder getAdOrBuilder(
        int index) {
      return ad_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, id_);
      }
      for (int i = 0; i < ad_.size(); i++) {
        output.writeMessage(2, ad_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      for (int i = 0; i < ad_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, ad_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat)) {
        return super.equals(obj);
      }
      cn.taken.ad.logic.media.zhangyu.dto.Response.Seat other = (cn.taken.ad.logic.media.zhangyu.dto.Response.Seat) obj;

      if (hasId() != other.hasId()) return false;
      if (hasId()) {
        if (getId()
            != other.getId()) return false;
      }
      if (!getAdList()
          .equals(other.getAdList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasId()) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + getId();
      }
      if (getAdCount() > 0) {
        hash = (37 * hash) + AD_FIELD_NUMBER;
        hash = (53 * hash) + getAdList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 一个位置上的广告
     * </pre>
     *
     * Protobuf type {@code Response.Seat}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Response.Seat)
        cn.taken.ad.logic.media.zhangyu.dto.Response.SeatOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder.class);
      }

      // Construct using cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        if (adBuilder_ == null) {
          ad_ = java.util.Collections.emptyList();
        } else {
          ad_ = null;
          adBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_Seat_descriptor;
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat getDefaultInstanceForType() {
        return cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.getDefaultInstance();
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat build() {
        cn.taken.ad.logic.media.zhangyu.dto.Response.Seat result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat buildPartial() {
        cn.taken.ad.logic.media.zhangyu.dto.Response.Seat result = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat result) {
        if (adBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            ad_ = java.util.Collections.unmodifiableList(ad_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.ad_ = ad_;
        } else {
          result.ad_ = adBuilder_.build();
        }
      }

      private void buildPartial0(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.taken.ad.logic.media.zhangyu.dto.Response.Seat) {
          return mergeFrom((cn.taken.ad.logic.media.zhangyu.dto.Response.Seat)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat other) {
        if (other == cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (adBuilder_ == null) {
          if (!other.ad_.isEmpty()) {
            if (ad_.isEmpty()) {
              ad_ = other.ad_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureAdIsMutable();
              ad_.addAll(other.ad_);
            }
            onChanged();
          }
        } else {
          if (!other.ad_.isEmpty()) {
            if (adBuilder_.isEmpty()) {
              adBuilder_.dispose();
              adBuilder_ = null;
              ad_ = other.ad_;
              bitField0_ = (bitField0_ & ~0x00000002);
              adBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getAdFieldBuilder() : null;
            } else {
              adBuilder_.addAllMessages(other.ad_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad m =
                    input.readMessage(
                        cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.parser(),
                        extensionRegistry);
                if (adBuilder_ == null) {
                  ensureAdIsMutable();
                  ad_.add(m);
                } else {
                  adBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <pre>
       * 指定请求里的impression id
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 指定请求里的impression id
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <pre>
       * 指定请求里的impression id
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定请求里的impression id
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad> ad_ =
        java.util.Collections.emptyList();
      private void ensureAdIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          ad_ = new java.util.ArrayList<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad>(ad_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder> adBuilder_;

      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad> getAdList() {
        if (adBuilder_ == null) {
          return java.util.Collections.unmodifiableList(ad_);
        } else {
          return adBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public int getAdCount() {
        if (adBuilder_ == null) {
          return ad_.size();
        } else {
          return adBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad getAd(int index) {
        if (adBuilder_ == null) {
          return ad_.get(index);
        } else {
          return adBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public Builder setAd(
          int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad value) {
        if (adBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAdIsMutable();
          ad_.set(index, value);
          onChanged();
        } else {
          adBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public Builder setAd(
          int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder builderForValue) {
        if (adBuilder_ == null) {
          ensureAdIsMutable();
          ad_.set(index, builderForValue.build());
          onChanged();
        } else {
          adBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public Builder addAd(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad value) {
        if (adBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAdIsMutable();
          ad_.add(value);
          onChanged();
        } else {
          adBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public Builder addAd(
          int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad value) {
        if (adBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAdIsMutable();
          ad_.add(index, value);
          onChanged();
        } else {
          adBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public Builder addAd(
          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder builderForValue) {
        if (adBuilder_ == null) {
          ensureAdIsMutable();
          ad_.add(builderForValue.build());
          onChanged();
        } else {
          adBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public Builder addAd(
          int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder builderForValue) {
        if (adBuilder_ == null) {
          ensureAdIsMutable();
          ad_.add(index, builderForValue.build());
          onChanged();
        } else {
          adBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public Builder addAllAd(
          java.lang.Iterable<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad> values) {
        if (adBuilder_ == null) {
          ensureAdIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, ad_);
          onChanged();
        } else {
          adBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public Builder clearAd() {
        if (adBuilder_ == null) {
          ad_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          adBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public Builder removeAd(int index) {
        if (adBuilder_ == null) {
          ensureAdIsMutable();
          ad_.remove(index);
          onChanged();
        } else {
          adBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder getAdBuilder(
          int index) {
        return getAdFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder getAdOrBuilder(
          int index) {
        if (adBuilder_ == null) {
          return ad_.get(index);  } else {
          return adBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder> 
           getAdOrBuilderList() {
        if (adBuilder_ != null) {
          return adBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(ad_);
        }
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder addAdBuilder() {
        return getAdFieldBuilder().addBuilder(
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.getDefaultInstance());
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder addAdBuilder(
          int index) {
        return getAdFieldBuilder().addBuilder(
            index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.getDefaultInstance());
      }
      /**
       * <code>repeated .Response.Seat.Ad ad = 2;</code>
       */
      public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder> 
           getAdBuilderList() {
        return getAdFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder> 
          getAdFieldBuilder() {
        if (adBuilder_ == null) {
          adBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Ad.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.AdOrBuilder>(
                  ad_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          ad_ = null;
        }
        return adBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Response.Seat)
    }

    // @@protoc_insertion_point(class_scope:Response.Seat)
    private static final cn.taken.ad.logic.media.zhangyu.dto.Response.Seat DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.taken.ad.logic.media.zhangyu.dto.Response.Seat();
    }

    public static cn.taken.ad.logic.media.zhangyu.dto.Response.Seat getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Seat>
        PARSER = new com.google.protobuf.AbstractParser<Seat>() {
      @java.lang.Override
      public Seat parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Seat> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Seat> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <pre>
   * 对应Request中的id
   * </pre>
   *
   * <code>optional string id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <pre>
   * 对应Request中的id
   * </pre>
   *
   * <code>optional string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 对应Request中的id
   * </pre>
   *
   * <code>optional string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 2;
  private int status_ = 0;
  /**
   * <pre>
   * 0-ok，其他值表示无广告返回
   * </pre>
   *
   * <code>optional int32 status = 2;</code>
   * @return Whether the status field is set.
   */
  @java.lang.Override
  public boolean hasStatus() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <pre>
   * 0-ok，其他值表示无广告返回
   * </pre>
   *
   * <code>optional int32 status = 2;</code>
   * @return The status.
   */
  @java.lang.Override
  public int getStatus() {
    return status_;
  }

  public static final int SEAT_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat> seat_;
  /**
   * <code>repeated .Response.Seat seat = 3;</code>
   */
  @java.lang.Override
  public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat> getSeatList() {
    return seat_;
  }
  /**
   * <code>repeated .Response.Seat seat = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.SeatOrBuilder> 
      getSeatOrBuilderList() {
    return seat_;
  }
  /**
   * <code>repeated .Response.Seat seat = 3;</code>
   */
  @java.lang.Override
  public int getSeatCount() {
    return seat_.size();
  }
  /**
   * <code>repeated .Response.Seat seat = 3;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat getSeat(int index) {
    return seat_.get(index);
  }
  /**
   * <code>repeated .Response.Seat seat = 3;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.media.zhangyu.dto.Response.SeatOrBuilder getSeatOrBuilder(
      int index) {
    return seat_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, status_);
    }
    for (int i = 0; i < seat_.size(); i++) {
      output.writeMessage(3, seat_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, status_);
    }
    for (int i = 0; i < seat_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, seat_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.taken.ad.logic.media.zhangyu.dto.Response)) {
      return super.equals(obj);
    }
    cn.taken.ad.logic.media.zhangyu.dto.Response other = (cn.taken.ad.logic.media.zhangyu.dto.Response) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (!getId()
          .equals(other.getId())) return false;
    }
    if (hasStatus() != other.hasStatus()) return false;
    if (hasStatus()) {
      if (getStatus()
          != other.getStatus()) return false;
    }
    if (!getSeatList()
        .equals(other.getSeatList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
    }
    if (hasStatus()) {
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
    }
    if (getSeatCount() > 0) {
      hash = (37 * hash) + SEAT_FIELD_NUMBER;
      hash = (53 * hash) + getSeatList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.media.zhangyu.dto.Response parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.taken.ad.logic.media.zhangyu.dto.Response prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Response}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Response)
      cn.taken.ad.logic.media.zhangyu.dto.ResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.media.zhangyu.dto.Response.class, cn.taken.ad.logic.media.zhangyu.dto.Response.Builder.class);
    }

    // Construct using cn.taken.ad.logic.media.zhangyu.dto.Response.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      status_ = 0;
      if (seatBuilder_ == null) {
        seat_ = java.util.Collections.emptyList();
      } else {
        seat_ = null;
        seatBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.taken.ad.logic.media.zhangyu.dto.ZhangYuMediaRequest.internal_static_Response_descriptor;
    }

    @java.lang.Override
    public cn.taken.ad.logic.media.zhangyu.dto.Response getDefaultInstanceForType() {
      return cn.taken.ad.logic.media.zhangyu.dto.Response.getDefaultInstance();
    }

    @java.lang.Override
    public cn.taken.ad.logic.media.zhangyu.dto.Response build() {
      cn.taken.ad.logic.media.zhangyu.dto.Response result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.taken.ad.logic.media.zhangyu.dto.Response buildPartial() {
      cn.taken.ad.logic.media.zhangyu.dto.Response result = new cn.taken.ad.logic.media.zhangyu.dto.Response(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(cn.taken.ad.logic.media.zhangyu.dto.Response result) {
      if (seatBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          seat_ = java.util.Collections.unmodifiableList(seat_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.seat_ = seat_;
      } else {
        result.seat_ = seatBuilder_.build();
      }
    }

    private void buildPartial0(cn.taken.ad.logic.media.zhangyu.dto.Response result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.status_ = status_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.taken.ad.logic.media.zhangyu.dto.Response) {
        return mergeFrom((cn.taken.ad.logic.media.zhangyu.dto.Response)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.taken.ad.logic.media.zhangyu.dto.Response other) {
      if (other == cn.taken.ad.logic.media.zhangyu.dto.Response.getDefaultInstance()) return this;
      if (other.hasId()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasStatus()) {
        setStatus(other.getStatus());
      }
      if (seatBuilder_ == null) {
        if (!other.seat_.isEmpty()) {
          if (seat_.isEmpty()) {
            seat_ = other.seat_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureSeatIsMutable();
            seat_.addAll(other.seat_);
          }
          onChanged();
        }
      } else {
        if (!other.seat_.isEmpty()) {
          if (seatBuilder_.isEmpty()) {
            seatBuilder_.dispose();
            seatBuilder_ = null;
            seat_ = other.seat_;
            bitField0_ = (bitField0_ & ~0x00000004);
            seatBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 getSeatFieldBuilder() : null;
          } else {
            seatBuilder_.addAllMessages(other.seat_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              status_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              cn.taken.ad.logic.media.zhangyu.dto.Response.Seat m =
                  input.readMessage(
                      cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.parser(),
                      extensionRegistry);
              if (seatBuilder_ == null) {
                ensureSeatIsMutable();
                seat_.add(m);
              } else {
                seatBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     * 对应Request中的id
     * </pre>
     *
     * <code>optional string id = 1;</code>
     * @return Whether the id field is set.
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 对应Request中的id
     * </pre>
     *
     * <code>optional string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 对应Request中的id
     * </pre>
     *
     * <code>optional string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 对应Request中的id
     * </pre>
     *
     * <code>optional string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 对应Request中的id
     * </pre>
     *
     * <code>optional string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 对应Request中的id
     * </pre>
     *
     * <code>optional string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int status_ ;
    /**
     * <pre>
     * 0-ok，其他值表示无广告返回
     * </pre>
     *
     * <code>optional int32 status = 2;</code>
     * @return Whether the status field is set.
     */
    @java.lang.Override
    public boolean hasStatus() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 0-ok，其他值表示无广告返回
     * </pre>
     *
     * <code>optional int32 status = 2;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }
    /**
     * <pre>
     * 0-ok，其他值表示无广告返回
     * </pre>
     *
     * <code>optional int32 status = 2;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(int value) {

      status_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 0-ok，其他值表示无广告返回
     * </pre>
     *
     * <code>optional int32 status = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000002);
      status_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat> seat_ =
      java.util.Collections.emptyList();
    private void ensureSeatIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        seat_ = new java.util.ArrayList<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat>(seat_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        cn.taken.ad.logic.media.zhangyu.dto.Response.Seat, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.SeatOrBuilder> seatBuilder_;

    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat> getSeatList() {
      if (seatBuilder_ == null) {
        return java.util.Collections.unmodifiableList(seat_);
      } else {
        return seatBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public int getSeatCount() {
      if (seatBuilder_ == null) {
        return seat_.size();
      } else {
        return seatBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat getSeat(int index) {
      if (seatBuilder_ == null) {
        return seat_.get(index);
      } else {
        return seatBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public Builder setSeat(
        int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat value) {
      if (seatBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSeatIsMutable();
        seat_.set(index, value);
        onChanged();
      } else {
        seatBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public Builder setSeat(
        int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder builderForValue) {
      if (seatBuilder_ == null) {
        ensureSeatIsMutable();
        seat_.set(index, builderForValue.build());
        onChanged();
      } else {
        seatBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public Builder addSeat(cn.taken.ad.logic.media.zhangyu.dto.Response.Seat value) {
      if (seatBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSeatIsMutable();
        seat_.add(value);
        onChanged();
      } else {
        seatBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public Builder addSeat(
        int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat value) {
      if (seatBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSeatIsMutable();
        seat_.add(index, value);
        onChanged();
      } else {
        seatBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public Builder addSeat(
        cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder builderForValue) {
      if (seatBuilder_ == null) {
        ensureSeatIsMutable();
        seat_.add(builderForValue.build());
        onChanged();
      } else {
        seatBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public Builder addSeat(
        int index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder builderForValue) {
      if (seatBuilder_ == null) {
        ensureSeatIsMutable();
        seat_.add(index, builderForValue.build());
        onChanged();
      } else {
        seatBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public Builder addAllSeat(
        java.lang.Iterable<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.Seat> values) {
      if (seatBuilder_ == null) {
        ensureSeatIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, seat_);
        onChanged();
      } else {
        seatBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public Builder clearSeat() {
      if (seatBuilder_ == null) {
        seat_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        seatBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public Builder removeSeat(int index) {
      if (seatBuilder_ == null) {
        ensureSeatIsMutable();
        seat_.remove(index);
        onChanged();
      } else {
        seatBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder getSeatBuilder(
        int index) {
      return getSeatFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public cn.taken.ad.logic.media.zhangyu.dto.Response.SeatOrBuilder getSeatOrBuilder(
        int index) {
      if (seatBuilder_ == null) {
        return seat_.get(index);  } else {
        return seatBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public java.util.List<? extends cn.taken.ad.logic.media.zhangyu.dto.Response.SeatOrBuilder> 
         getSeatOrBuilderList() {
      if (seatBuilder_ != null) {
        return seatBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(seat_);
      }
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder addSeatBuilder() {
      return getSeatFieldBuilder().addBuilder(
          cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.getDefaultInstance());
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder addSeatBuilder(
        int index) {
      return getSeatFieldBuilder().addBuilder(
          index, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.getDefaultInstance());
    }
    /**
     * <code>repeated .Response.Seat seat = 3;</code>
     */
    public java.util.List<cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder> 
         getSeatBuilderList() {
      return getSeatFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        cn.taken.ad.logic.media.zhangyu.dto.Response.Seat, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.SeatOrBuilder> 
        getSeatFieldBuilder() {
      if (seatBuilder_ == null) {
        seatBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            cn.taken.ad.logic.media.zhangyu.dto.Response.Seat, cn.taken.ad.logic.media.zhangyu.dto.Response.Seat.Builder, cn.taken.ad.logic.media.zhangyu.dto.Response.SeatOrBuilder>(
                seat_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        seat_ = null;
      }
      return seatBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:Response)
  }

  // @@protoc_insertion_point(class_scope:Response)
  private static final cn.taken.ad.logic.media.zhangyu.dto.Response DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.taken.ad.logic.media.zhangyu.dto.Response();
  }

  public static cn.taken.ad.logic.media.zhangyu.dto.Response getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Response>
      PARSER = new com.google.protobuf.AbstractParser<Response>() {
    @java.lang.Override
    public Response parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Response> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Response> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.taken.ad.logic.media.zhangyu.dto.Response getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

