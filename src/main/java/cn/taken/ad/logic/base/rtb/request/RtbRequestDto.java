package cn.taken.ad.logic.base.rtb.request;

import cn.taken.ad.logic.base.rtb.request.dto.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class RtbRequestDto implements Serializable {

    private static final long serialVersionUID = -2498697273336572428L;

    /**
     * 媒体请求唯一标识
     */
    private String reqId;
    /**
     * APP信息
     */
    private RequestAppDto app;
    /**
     * 广告位信息
     */
    private RequestTagDto tag;
    /**
     * 设备信息
     */
    private RequestDeviceDto device;
    /**
     * 网络信息
     */
    private RequestNetworkDto network;
    /**
     * 地理位置信息
     */
    private RequestGeoDto geo;
    /**
     * 用户信息
     */
    private RequestUserDto user;

    private List<RequestPageInfoDto> pageInfos;

    public RtbRequestDto() {
    }

    public RtbRequestDto(String reqId, RtbRequestDto requestDto) {
        this.reqId = reqId;
        this.app = new RequestAppDto(requestDto.getApp());
        this.tag = new RequestTagDto(requestDto.getTag());
        this.device = new RequestDeviceDto(requestDto.getDevice());
        this.network = new RequestNetworkDto(requestDto.getNetwork());
        this.geo = new RequestGeoDto(requestDto.getGeo());
        this.user = new RequestUserDto(requestDto.getUser());
        this.pageInfos = new ArrayList<>();
        if (requestDto.getPageInfos() != null && !requestDto.getPageInfos().isEmpty()) {
            for (RequestPageInfoDto pageInfoDto : requestDto.getPageInfos()) {
                pageInfos.add(new RequestPageInfoDto(pageInfoDto));
            }
        }
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public RequestAppDto getApp() {
        return app;
    }

    public void setApp(RequestAppDto app) {
        this.app = app;
    }

    public RequestTagDto getTag() {
        return tag;
    }

    public void setTag(RequestTagDto tag) {
        this.tag = tag;
    }

    public RequestDeviceDto getDevice() {
        return device;
    }

    public void setDevice(RequestDeviceDto device) {
        this.device = device;
    }

    public RequestNetworkDto getNetwork() {
        return network;
    }

    public void setNetwork(RequestNetworkDto network) {
        this.network = network;
    }

    public RequestGeoDto getGeo() {
        return geo;
    }

    public void setGeo(RequestGeoDto geo) {
        this.geo = geo;
    }

    public RequestUserDto getUser() {
        return user;
    }

    public void setUser(RequestUserDto user) {
        this.user = user;
    }

    public List<RequestPageInfoDto> getPageInfos() {
        return pageInfos;
    }

    public void setPageInfos(List<RequestPageInfoDto> pageInfos) {
        this.pageInfos = pageInfos;
    }
}
