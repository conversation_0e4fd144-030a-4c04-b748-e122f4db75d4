package cn.taken.ad.logic.adv.metoo.dto;

import java.io.Serializable;
import java.util.List;

public class ResponseAdm implements Serializable {

    private Integer infoType;//	广告素材类型：
    private Integer interactionType;//	广告操作行为：
    private String title;//	广告标题
    private String desc;//	广告详情
    private String deeplink;//	Deeplink地址
    private String clickUrl	;//	点击行为地址
    private String downloadUrl;//	下载链接
    private String marketUrl;//	应用商店下载链接
    private List<String > adLogo;//	广告图标列表
    private List<ResponseAdmImage> images;//	素材图片列表

    public Integer getInfoType() {
        return infoType;
    }

    public void setInfoType(Integer infoType) {
        this.infoType = infoType;
    }

    public Integer getInteractionType() {
        return interactionType;
    }

    public void setInteractionType(Integer interactionType) {
        this.interactionType = interactionType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }

    public String getClickUrl() {
        return clickUrl;
    }

    public void setClickUrl(String clickUrl) {
        this.clickUrl = clickUrl;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getMarketUrl() {
        return marketUrl;
    }

    public void setMarketUrl(String marketUrl) {
        this.marketUrl = marketUrl;
    }

    public List<String> getAdLogo() {
        return adLogo;
    }

    public void setAdLogo(List<String> adLogo) {
        this.adLogo = adLogo;
    }

    public List<ResponseAdmImage> getImages() {
        return images;
    }

    public void setImages(List<ResponseAdmImage> images) {
        this.images = images;
    }
}
