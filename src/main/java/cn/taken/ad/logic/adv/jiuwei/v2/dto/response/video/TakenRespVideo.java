// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: ResponseVideo.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.jiuwei.v2.dto.response.video;

public final class TakenRespVideo {
  private TakenRespVideo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      TakenRespVideo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_resp_TakenResponseVideo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_resp_TakenResponseVideo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\023ResponseVideo.proto\022\004resp\"\266\003\n\022TakenRes" +
      "ponseVideo\022\020\n\010videoUrl\030\001 \001(\t\022\020\n\010duration" +
      "\030\002 \001(\005\022\021\n\tvideoSize\030\003 \001(\003\022\022\n\nvideoWidth\030" +
      "\004 \001(\005\022\023\n\013videoHeight\030\005 \001(\005\022\022\n\nresolution" +
      "\030\006 \001(\t\022\027\n\017orientationType\030\007 \001(\005\022\024\n\014cover" +
      "ImgUrls\030\010 \003(\t\022\022\n\ncoverWidth\030\t \001(\005\022\023\n\013cov" +
      "erHeight\030\n \001(\005\022\022\n\nbuttonText\030\013 \001(\t\022\025\n\ren" +
      "dButtonText\030\014 \001(\t\022\022\n\nendImgUrls\030\r \003(\t\022\022\n" +
      "\nendIconUrl\030\016 \001(\t\022\020\n\010endTitle\030\017 \001(\t\022\017\n\007e" +
      "ndDesc\030\020 \001(\t\022\017\n\007endHtml\030\021 \001(\t\022\023\n\013autoLan" +
      "ding\030\022 \001(\005\022\020\n\010prefetch\030\023 \001(\005\022\023\n\013skipSeco" +
      "nds\030\024 \001(\005\022\021\n\tclickAble\030\025 \001(\005BI\n5cn.taken" +
      ".ad.logic.prossor.taken.v2.dto.response." +
      "videoB\016TakenRespVideoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_resp_TakenResponseVideo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_resp_TakenResponseVideo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_resp_TakenResponseVideo_descriptor,
        new String[] { "VideoUrl", "Duration", "VideoSize", "VideoWidth", "VideoHeight", "Resolution", "OrientationType", "CoverImgUrls", "CoverWidth", "CoverHeight", "ButtonText", "EndButtonText", "EndImgUrls", "EndIconUrl", "EndTitle", "EndDesc", "EndHtml", "AutoLanding", "Prefetch", "SkipSeconds", "ClickAble", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
