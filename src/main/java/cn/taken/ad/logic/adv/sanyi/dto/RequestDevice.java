package cn.taken.ad.logic.adv.sanyi.dto;

import java.io.Serializable;

public class RequestDevice implements Serializable {

    // 设备类型 1=PHONE 2=TABLET 3=OTT
    private Integer dtype;
    // 系统类型 1=ANDROID 2=IOS
    private Integer os;
    // 系统版本号
    private String osv;
    // 屏幕尺寸
    private Integer w;  // 宽度
    private Integer h;  // 高度
    // IOS设备标识
    private String idfa;  // 广告标识符
    private String idfa_md5;  // 广告标识符
    private String idfv;  // 厂商标识符
    // 设备硬件标识
    private String imei;  // IMEI码
    private String imei_md5;  // IMEI码
    private String androidid;  // Android ID
    private String androidid_md5;  // Android ID
    // UserAgent
    private String ua;  // 浏览器UA
    // 网络信息
    private String mac;  // MAC地址
    private String mac_md5;  // MAC地址
    // 设备信息
    private String vendor;  // 厂商
    private String brand;  // 品牌
    private String model;  // 型号
    private String imsi;  // IMSI码(选填)
    private String ip;  // 公网IP
    private Integer network;  // 网络类型 0-5
    private Integer carrier;  // 运营商 0-3
    private String meid;  // MEID码(电信设备)
    // Android特有
    private Integer av;  // API Level
    private String sn;  // 序列号
    private String oaid;  // 匿名设备标识
    private String oaid_md5;  // 匿名设备标识
    // 无线网络
    private String ssid;  // SSID名称(选填)
    private String bssid;  // BSSID地址(选填)
    // 硬件信息
    private Float density;  // 屏幕密度
    private Integer ppi;  // 像素密度
    // 系统信息
    private String bootmark;  // 启动标识
    private String updatemark;  // 更新标识
    // IOS特有
    private String aaid;  // 阿里广告ID
    private String caid;  // CAID标识
    private String caidv;  // CAID版本
    // 位置信息
    private Float lon;  // 经度(选填)
    private Float lat;  // 纬度(选填)
    private Integer ts;  // 定位时间戳(选填)
    // 设备名称
    private String dname;  // 设备名称(选填)
    private String mem;  // 内存大小(选填)
    private String disk;  // 存储大小(选填)
    private String hmsv;  // HMS Core版本
    private String appstorev;  // 应用商店版本
    private String udid;  // OpenUDID
    private String language;  // 设备语言
    private String romv;  // ROM版本
    // IOS硬件信息
    private Long compilingtime;  // 编译时间(选填)
    private Long uptime;  // 更新时间(选填)
    private Long bootime;  // 启动时间(选填)
    private String hwmodel;  // 硬件型号(选填)
    private String hwmachine;  // 系统型号(选填)
    private String hwv;  // 硬件版本(选填)
    private Integer cpu;  // CPU核心数(选填)
    private Float cpufreq;  // CPU频率(选填)
    // 电池状态
    private Integer battery;  // 充电状态(选填)
    private Integer batterystatus;  // 电量百分比(选填)
    // 其他
    private Integer orien;  // 屏幕方向
    private String timezone;  // 时区
    private Integer idfapolicy;  // IDFA授权策略
    private String modelcode;  // 设备型号代码
    // 厂商特有
    private String xiaomitokenid;  // 小米设备标识
    private String vaid;  // 厂商匿名ID
    private String miuiVersion;  // MIUI版本


    public Integer getDtype() {
        return dtype;
    }

    public void setDtype(Integer dtype) {
        this.dtype = dtype;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public String getOsv() {
        return osv;
    }

    public void setOsv(String osv) {
        this.osv = osv;
    }

    public Integer getW() {
        return w;
    }

    public void setW(Integer w) {
        this.w = w;
    }

    public Integer getH() {
        return h;
    }

    public void setH(Integer h) {
        this.h = h;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getIdfv() {
        return idfv;
    }

    public void setIdfv(String idfv) {
        this.idfv = idfv;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getAndroidid() {
        return androidid;
    }

    public void setAndroidid(String androidid) {
        this.androidid = androidid;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getNetwork() {
        return network;
    }

    public void setNetwork(Integer network) {
        this.network = network;
    }

    public Integer getCarrier() {
        return carrier;
    }

    public void setCarrier(Integer carrier) {
        this.carrier = carrier;
    }

    public String getMeid() {
        return meid;
    }

    public void setMeid(String meid) {
        this.meid = meid;
    }

    public Integer getAv() {
        return av;
    }

    public void setAv(Integer av) {
        this.av = av;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public String getBssid() {
        return bssid;
    }

    public void setBssid(String bssid) {
        this.bssid = bssid;
    }

    public Float getDensity() {
        return density;
    }

    public void setDensity(Float density) {
        this.density = density;
    }

    public Integer getPpi() {
        return ppi;
    }

    public void setPpi(Integer ppi) {
        this.ppi = ppi;
    }

    public String getBootmark() {
        return bootmark;
    }

    public void setBootmark(String bootmark) {
        this.bootmark = bootmark;
    }

    public String getUpdatemark() {
        return updatemark;
    }

    public void setUpdatemark(String updatemark) {
        this.updatemark = updatemark;
    }

    public String getAaid() {
        return aaid;
    }

    public void setAaid(String aaid) {
        this.aaid = aaid;
    }

    public String getCaid() {
        return caid;
    }

    public void setCaid(String caid) {
        this.caid = caid;
    }

    public String getCaidv() {
        return caidv;
    }

    public void setCaidv(String caidv) {
        this.caidv = caidv;
    }

    public Float getLon() {
        return lon;
    }

    public void setLon(Float lon) {
        this.lon = lon;
    }

    public Float getLat() {
        return lat;
    }

    public void setLat(Float lat) {
        this.lat = lat;
    }

    public Integer getTs() {
        return ts;
    }

    public void setTs(Integer ts) {
        this.ts = ts;
    }

    public String getDname() {
        return dname;
    }

    public void setDname(String dname) {
        this.dname = dname;
    }

    public String getMem() {
        return mem;
    }

    public void setMem(String mem) {
        this.mem = mem;
    }

    public String getDisk() {
        return disk;
    }

    public void setDisk(String disk) {
        this.disk = disk;
    }

    public String getHmsv() {
        return hmsv;
    }

    public void setHmsv(String hmsv) {
        this.hmsv = hmsv;
    }

    public String getAppstorev() {
        return appstorev;
    }

    public void setAppstorev(String appstorev) {
        this.appstorev = appstorev;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getRomv() {
        return romv;
    }

    public void setRomv(String romv) {
        this.romv = romv;
    }

    public Long getCompilingtime() {
        return compilingtime;
    }

    public void setCompilingtime(Long compilingtime) {
        this.compilingtime = compilingtime;
    }

    public Long getUptime() {
        return uptime;
    }

    public void setUptime(Long uptime) {
        this.uptime = uptime;
    }

    public Long getBootime() {
        return bootime;
    }

    public void setBootime(Long bootime) {
        this.bootime = bootime;
    }

    public String getHwmodel() {
        return hwmodel;
    }

    public void setHwmodel(String hwmodel) {
        this.hwmodel = hwmodel;
    }

    public String getHwmachine() {
        return hwmachine;
    }

    public void setHwmachine(String hwmachine) {
        this.hwmachine = hwmachine;
    }

    public String getHwv() {
        return hwv;
    }

    public void setHwv(String hwv) {
        this.hwv = hwv;
    }

    public Integer getCpu() {
        return cpu;
    }

    public void setCpu(Integer cpu) {
        this.cpu = cpu;
    }

    public Float getCpufreq() {
        return cpufreq;
    }

    public void setCpufreq(Float cpufreq) {
        this.cpufreq = cpufreq;
    }

    public Integer getBattery() {
        return battery;
    }

    public void setBattery(Integer battery) {
        this.battery = battery;
    }

    public Integer getBatterystatus() {
        return batterystatus;
    }

    public void setBatterystatus(Integer batterystatus) {
        this.batterystatus = batterystatus;
    }

    public Integer getOrien() {
        return orien;
    }

    public void setOrien(Integer orien) {
        this.orien = orien;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public Integer getIdfapolicy() {
        return idfapolicy;
    }

    public void setIdfapolicy(Integer idfapolicy) {
        this.idfapolicy = idfapolicy;
    }

    public String getModelcode() {
        return modelcode;
    }

    public void setModelcode(String modelcode) {
        this.modelcode = modelcode;
    }

    public String getXiaomitokenid() {
        return xiaomitokenid;
    }

    public void setXiaomitokenid(String xiaomitokenid) {
        this.xiaomitokenid = xiaomitokenid;
    }

    public String getVaid() {
        return vaid;
    }

    public void setVaid(String vaid) {
        this.vaid = vaid;
    }

    public String getMiuiVersion() {
        return miuiVersion;
    }

    public void setMiuiVersion(String miuiVersion) {
        this.miuiVersion = miuiVersion;
    }

    public String getIdfa_md5() {
        return idfa_md5;
    }

    public void setIdfa_md5(String idfa_md5) {
        this.idfa_md5 = idfa_md5;
    }

    public String getImei_md5() {
        return imei_md5;
    }

    public void setImei_md5(String imei_md5) {
        this.imei_md5 = imei_md5;
    }

    public String getAndroidid_md5() {
        return androidid_md5;
    }

    public void setAndroidid_md5(String androidid_md5) {
        this.androidid_md5 = androidid_md5;
    }

    public String getMac_md5() {
        return mac_md5;
    }

    public void setMac_md5(String mac_md5) {
        this.mac_md5 = mac_md5;
    }

    public String getOaid_md5() {
        return oaid_md5;
    }

    public void setOaid_md5(String oaid_md5) {
        this.oaid_md5 = oaid_md5;
    }
}
