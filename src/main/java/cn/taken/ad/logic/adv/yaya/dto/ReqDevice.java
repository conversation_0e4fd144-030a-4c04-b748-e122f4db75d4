// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

/**
 * Protobuf type {@code ReqDevice}
 */
public final class ReqDevice extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:ReqDevice)
    ReqDeviceOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      ReqDevice.class.getName());
  }
  // Use ReqDevice.newBuilder() to construct.
  private ReqDevice(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ReqDevice() {
    ua_ = "";
    ip_ = "";
    ipv6_ = "";
    deviceType_ = 0;
    make_ = "";
    mode_ = "";
    os_ = 0;
    osv_ = "";
    carrier_ = 0;
    connectionType_ = 0;
    ifa_ = "";
    imeimd5_ = "";
    idfv_ = "";
    adidmd5_ = "";
    mac_ = "";
    macmd5_ = "";
    adid_ = "";
    oaid_ = "";
    brand_ = "";
    caid_ = "";
    caidVersion_ = "";
    bootMark_ = "";
    updateMark_ = "";
    romVersion_ = "";
    hmscore_ = "";
    paid_ = "";
    bssid_ = "";
    checkedApps_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    hmsVer_ = "";
    hwagVer_ = "";
    deviceName_ = "";
    timeZone_ = "";
    appStoreVer_ = "";
    apiLevel_ = "";
    wifiMac_ = "";
    startupTime_ = "";
    bootTime_ = "";
    updateTime_ = "";
    birthTime_ = "";
    idfaMd5_ = "";
    oaidMd5_ = "";
    aliAaid_ = "";
    openudid_ = "";
    miuiVersion_ = "";
    con_ = "";
    lan_ = "";
    hardwareModel_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqDevice_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqDevice_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.taken.ad.logic.adv.yaya.dto.ReqDevice.class, cn.taken.ad.logic.adv.yaya.dto.ReqDevice.Builder.class);
  }

  private int bitField0_;
  public static final int UA_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object ua_ = "";
  /**
   * <code>string ua = 1;</code>
   * @return The ua.
   */
  @java.lang.Override
  public java.lang.String getUa() {
    java.lang.Object ref = ua_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ua_ = s;
      return s;
    }
  }
  /**
   * <code>string ua = 1;</code>
   * @return The bytes for ua.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUaBytes() {
    java.lang.Object ref = ua_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ua_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GEO_FIELD_NUMBER = 2;
  private cn.taken.ad.logic.adv.yaya.dto.ReqGeo geo_;
  /**
   * <code>.ReqGeo geo = 2;</code>
   * @return Whether the geo field is set.
   */
  @java.lang.Override
  public boolean hasGeo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.ReqGeo geo = 2;</code>
   * @return The geo.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqGeo getGeo() {
    return geo_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqGeo.getDefaultInstance() : geo_;
  }
  /**
   * <code>.ReqGeo geo = 2;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqGeoOrBuilder getGeoOrBuilder() {
    return geo_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqGeo.getDefaultInstance() : geo_;
  }

  public static final int IP_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object ip_ = "";
  /**
   * <code>string ip = 3;</code>
   * @return The ip.
   */
  @java.lang.Override
  public java.lang.String getIp() {
    java.lang.Object ref = ip_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ip_ = s;
      return s;
    }
  }
  /**
   * <code>string ip = 3;</code>
   * @return The bytes for ip.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIpBytes() {
    java.lang.Object ref = ip_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ip_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IPV6_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object ipv6_ = "";
  /**
   * <code>string ipv6 = 4;</code>
   * @return The ipv6.
   */
  @java.lang.Override
  public java.lang.String getIpv6() {
    java.lang.Object ref = ipv6_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ipv6_ = s;
      return s;
    }
  }
  /**
   * <code>string ipv6 = 4;</code>
   * @return The bytes for ipv6.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIpv6Bytes() {
    java.lang.Object ref = ipv6_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ipv6_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DEVICETYPE_FIELD_NUMBER = 5;
  private int deviceType_ = 0;
  /**
   * <code>.MobReqDeviceType deviceType = 5;</code>
   * @return The enum numeric value on the wire for deviceType.
   */
  @java.lang.Override public int getDeviceTypeValue() {
    return deviceType_;
  }
  /**
   * <code>.MobReqDeviceType deviceType = 5;</code>
   * @return The deviceType.
   */
  @java.lang.Override public cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType getDeviceType() {
    cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType result = cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType.forNumber(deviceType_);
    return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType.UNRECOGNIZED : result;
  }

  public static final int MAKE_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object make_ = "";
  /**
   * <code>string make = 6;</code>
   * @return The make.
   */
  @java.lang.Override
  public java.lang.String getMake() {
    java.lang.Object ref = make_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      make_ = s;
      return s;
    }
  }
  /**
   * <code>string make = 6;</code>
   * @return The bytes for make.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMakeBytes() {
    java.lang.Object ref = make_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      make_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MODE_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object mode_ = "";
  /**
   * <code>string mode = 7;</code>
   * @return The mode.
   */
  @java.lang.Override
  public java.lang.String getMode() {
    java.lang.Object ref = mode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      mode_ = s;
      return s;
    }
  }
  /**
   * <code>string mode = 7;</code>
   * @return The bytes for mode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getModeBytes() {
    java.lang.Object ref = mode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      mode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OS_FIELD_NUMBER = 8;
  private int os_ = 0;
  /**
   * <code>.MobReqDeviceOsType os = 8;</code>
   * @return The enum numeric value on the wire for os.
   */
  @java.lang.Override public int getOsValue() {
    return os_;
  }
  /**
   * <code>.MobReqDeviceOsType os = 8;</code>
   * @return The os.
   */
  @java.lang.Override public cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType getOs() {
    cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType result = cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType.forNumber(os_);
    return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType.UNRECOGNIZED : result;
  }

  public static final int OSV_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object osv_ = "";
  /**
   * <code>string osv = 9;</code>
   * @return The osv.
   */
  @java.lang.Override
  public java.lang.String getOsv() {
    java.lang.Object ref = osv_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      osv_ = s;
      return s;
    }
  }
  /**
   * <code>string osv = 9;</code>
   * @return The bytes for osv.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOsvBytes() {
    java.lang.Object ref = osv_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      osv_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CARRIER_FIELD_NUMBER = 10;
  private int carrier_ = 0;
  /**
   * <code>.MobReqDeviceCarrierType carrier = 10;</code>
   * @return The enum numeric value on the wire for carrier.
   */
  @java.lang.Override public int getCarrierValue() {
    return carrier_;
  }
  /**
   * <code>.MobReqDeviceCarrierType carrier = 10;</code>
   * @return The carrier.
   */
  @java.lang.Override public cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType getCarrier() {
    cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType result = cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType.forNumber(carrier_);
    return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType.UNRECOGNIZED : result;
  }

  public static final int CONNECTIONTYPE_FIELD_NUMBER = 11;
  private int connectionType_ = 0;
  /**
   * <code>.MobDeviceConnectionType connectionType = 11;</code>
   * @return The enum numeric value on the wire for connectionType.
   */
  @java.lang.Override public int getConnectionTypeValue() {
    return connectionType_;
  }
  /**
   * <code>.MobDeviceConnectionType connectionType = 11;</code>
   * @return The connectionType.
   */
  @java.lang.Override public cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType getConnectionType() {
    cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType result = cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType.forNumber(connectionType_);
    return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType.UNRECOGNIZED : result;
  }

  public static final int IFA_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private volatile java.lang.Object ifa_ = "";
  /**
   * <code>string ifa = 12;</code>
   * @return The ifa.
   */
  @java.lang.Override
  public java.lang.String getIfa() {
    java.lang.Object ref = ifa_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ifa_ = s;
      return s;
    }
  }
  /**
   * <code>string ifa = 12;</code>
   * @return The bytes for ifa.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIfaBytes() {
    java.lang.Object ref = ifa_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ifa_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMEIMD5_FIELD_NUMBER = 13;
  @SuppressWarnings("serial")
  private volatile java.lang.Object imeimd5_ = "";
  /**
   * <code>string imeimd5 = 13;</code>
   * @return The imeimd5.
   */
  @java.lang.Override
  public java.lang.String getImeimd5() {
    java.lang.Object ref = imeimd5_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      imeimd5_ = s;
      return s;
    }
  }
  /**
   * <code>string imeimd5 = 13;</code>
   * @return The bytes for imeimd5.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getImeimd5Bytes() {
    java.lang.Object ref = imeimd5_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      imeimd5_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IDFV_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private volatile java.lang.Object idfv_ = "";
  /**
   * <code>string idfv = 14;</code>
   * @return The idfv.
   */
  @java.lang.Override
  public java.lang.String getIdfv() {
    java.lang.Object ref = idfv_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      idfv_ = s;
      return s;
    }
  }
  /**
   * <code>string idfv = 14;</code>
   * @return The bytes for idfv.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdfvBytes() {
    java.lang.Object ref = idfv_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      idfv_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ADIDMD5_FIELD_NUMBER = 15;
  @SuppressWarnings("serial")
  private volatile java.lang.Object adidmd5_ = "";
  /**
   * <code>string adidmd5 = 15;</code>
   * @return The adidmd5.
   */
  @java.lang.Override
  public java.lang.String getAdidmd5() {
    java.lang.Object ref = adidmd5_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      adidmd5_ = s;
      return s;
    }
  }
  /**
   * <code>string adidmd5 = 15;</code>
   * @return The bytes for adidmd5.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAdidmd5Bytes() {
    java.lang.Object ref = adidmd5_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      adidmd5_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MAC_FIELD_NUMBER = 16;
  @SuppressWarnings("serial")
  private volatile java.lang.Object mac_ = "";
  /**
   * <code>string mac = 16;</code>
   * @return The mac.
   */
  @java.lang.Override
  public java.lang.String getMac() {
    java.lang.Object ref = mac_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      mac_ = s;
      return s;
    }
  }
  /**
   * <code>string mac = 16;</code>
   * @return The bytes for mac.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMacBytes() {
    java.lang.Object ref = mac_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      mac_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MACMD5_FIELD_NUMBER = 17;
  @SuppressWarnings("serial")
  private volatile java.lang.Object macmd5_ = "";
  /**
   * <code>string macmd5 = 17;</code>
   * @return The macmd5.
   */
  @java.lang.Override
  public java.lang.String getMacmd5() {
    java.lang.Object ref = macmd5_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      macmd5_ = s;
      return s;
    }
  }
  /**
   * <code>string macmd5 = 17;</code>
   * @return The bytes for macmd5.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMacmd5Bytes() {
    java.lang.Object ref = macmd5_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      macmd5_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ADID_FIELD_NUMBER = 18;
  @SuppressWarnings("serial")
  private volatile java.lang.Object adid_ = "";
  /**
   * <code>string adid = 18;</code>
   * @return The adid.
   */
  @java.lang.Override
  public java.lang.String getAdid() {
    java.lang.Object ref = adid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      adid_ = s;
      return s;
    }
  }
  /**
   * <code>string adid = 18;</code>
   * @return The bytes for adid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAdidBytes() {
    java.lang.Object ref = adid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      adid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int W_FIELD_NUMBER = 19;
  private int w_ = 0;
  /**
   * <code>int32 w = 19;</code>
   * @return The w.
   */
  @java.lang.Override
  public int getW() {
    return w_;
  }

  public static final int H_FIELD_NUMBER = 20;
  private int h_ = 0;
  /**
   * <code>int32 h = 20;</code>
   * @return The h.
   */
  @java.lang.Override
  public int getH() {
    return h_;
  }

  public static final int ASW_FIELD_NUMBER = 21;
  private int asw_ = 0;
  /**
   * <code>int32 asw = 21;</code>
   * @return The asw.
   */
  @java.lang.Override
  public int getAsw() {
    return asw_;
  }

  public static final int ASH_FIELD_NUMBER = 22;
  private int ash_ = 0;
  /**
   * <code>int32 ash = 22;</code>
   * @return The ash.
   */
  @java.lang.Override
  public int getAsh() {
    return ash_;
  }

  public static final int OAID_FIELD_NUMBER = 23;
  @SuppressWarnings("serial")
  private volatile java.lang.Object oaid_ = "";
  /**
   * <code>string oaid = 23;</code>
   * @return The oaid.
   */
  @java.lang.Override
  public java.lang.String getOaid() {
    java.lang.Object ref = oaid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      oaid_ = s;
      return s;
    }
  }
  /**
   * <code>string oaid = 23;</code>
   * @return The bytes for oaid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOaidBytes() {
    java.lang.Object ref = oaid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      oaid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BRAND_FIELD_NUMBER = 24;
  @SuppressWarnings("serial")
  private volatile java.lang.Object brand_ = "";
  /**
   * <code>string brand = 24;</code>
   * @return The brand.
   */
  @java.lang.Override
  public java.lang.String getBrand() {
    java.lang.Object ref = brand_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      brand_ = s;
      return s;
    }
  }
  /**
   * <code>string brand = 24;</code>
   * @return The bytes for brand.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBrandBytes() {
    java.lang.Object ref = brand_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      brand_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DPI_FIELD_NUMBER = 25;
  private float dpi_ = 0F;
  /**
   * <code>float dpi = 25;</code>
   * @return The dpi.
   */
  @java.lang.Override
  public float getDpi() {
    return dpi_;
  }

  public static final int PPI_FIELD_NUMBER = 26;
  private int ppi_ = 0;
  /**
   * <code>int32 ppi = 26;</code>
   * @return The ppi.
   */
  @java.lang.Override
  public int getPpi() {
    return ppi_;
  }

  public static final int CAID_FIELD_NUMBER = 27;
  @SuppressWarnings("serial")
  private volatile java.lang.Object caid_ = "";
  /**
   * <code>string caid = 27;</code>
   * @return The caid.
   */
  @java.lang.Override
  public java.lang.String getCaid() {
    java.lang.Object ref = caid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      caid_ = s;
      return s;
    }
  }
  /**
   * <code>string caid = 27;</code>
   * @return The bytes for caid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCaidBytes() {
    java.lang.Object ref = caid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      caid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CAIDVERSION_FIELD_NUMBER = 28;
  @SuppressWarnings("serial")
  private volatile java.lang.Object caidVersion_ = "";
  /**
   * <code>string caidVersion = 28;</code>
   * @return The caidVersion.
   */
  @java.lang.Override
  public java.lang.String getCaidVersion() {
    java.lang.Object ref = caidVersion_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      caidVersion_ = s;
      return s;
    }
  }
  /**
   * <code>string caidVersion = 28;</code>
   * @return The bytes for caidVersion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCaidVersionBytes() {
    java.lang.Object ref = caidVersion_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      caidVersion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BOOTMARK_FIELD_NUMBER = 29;
  @SuppressWarnings("serial")
  private volatile java.lang.Object bootMark_ = "";
  /**
   * <code>string bootMark = 29;</code>
   * @return The bootMark.
   */
  @java.lang.Override
  public java.lang.String getBootMark() {
    java.lang.Object ref = bootMark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      bootMark_ = s;
      return s;
    }
  }
  /**
   * <code>string bootMark = 29;</code>
   * @return The bytes for bootMark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBootMarkBytes() {
    java.lang.Object ref = bootMark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      bootMark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPDATEMARK_FIELD_NUMBER = 30;
  @SuppressWarnings("serial")
  private volatile java.lang.Object updateMark_ = "";
  /**
   * <code>string updateMark = 30;</code>
   * @return The updateMark.
   */
  @java.lang.Override
  public java.lang.String getUpdateMark() {
    java.lang.Object ref = updateMark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      updateMark_ = s;
      return s;
    }
  }
  /**
   * <code>string updateMark = 30;</code>
   * @return The bytes for updateMark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUpdateMarkBytes() {
    java.lang.Object ref = updateMark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      updateMark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ROMVERSION_FIELD_NUMBER = 31;
  @SuppressWarnings("serial")
  private volatile java.lang.Object romVersion_ = "";
  /**
   * <code>string romVersion = 31;</code>
   * @return The romVersion.
   */
  @java.lang.Override
  public java.lang.String getRomVersion() {
    java.lang.Object ref = romVersion_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      romVersion_ = s;
      return s;
    }
  }
  /**
   * <code>string romVersion = 31;</code>
   * @return The bytes for romVersion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRomVersionBytes() {
    java.lang.Object ref = romVersion_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      romVersion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SYSCOMPILETS_FIELD_NUMBER = 32;
  private long sysCompileTs_ = 0L;
  /**
   * <code>int64 sysCompileTs = 32;</code>
   * @return The sysCompileTs.
   */
  @java.lang.Override
  public long getSysCompileTs() {
    return sysCompileTs_;
  }

  public static final int HMSCORE_FIELD_NUMBER = 33;
  @SuppressWarnings("serial")
  private volatile java.lang.Object hmscore_ = "";
  /**
   * <code>string hmscore = 33;</code>
   * @return The hmscore.
   */
  @java.lang.Override
  public java.lang.String getHmscore() {
    java.lang.Object ref = hmscore_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      hmscore_ = s;
      return s;
    }
  }
  /**
   * <code>string hmscore = 33;</code>
   * @return The bytes for hmscore.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHmscoreBytes() {
    java.lang.Object ref = hmscore_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      hmscore_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAID_FIELD_NUMBER = 34;
  @SuppressWarnings("serial")
  private volatile java.lang.Object paid_ = "";
  /**
   * <code>string paid = 34;</code>
   * @return The paid.
   */
  @java.lang.Override
  public java.lang.String getPaid() {
    java.lang.Object ref = paid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      paid_ = s;
      return s;
    }
  }
  /**
   * <code>string paid = 34;</code>
   * @return The bytes for paid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPaidBytes() {
    java.lang.Object ref = paid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      paid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BSSID_FIELD_NUMBER = 35;
  @SuppressWarnings("serial")
  private volatile java.lang.Object bssid_ = "";
  /**
   * <code>string bssid = 35;</code>
   * @return The bssid.
   */
  @java.lang.Override
  public java.lang.String getBssid() {
    java.lang.Object ref = bssid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      bssid_ = s;
      return s;
    }
  }
  /**
   * <code>string bssid = 35;</code>
   * @return The bytes for bssid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBssidBytes() {
    java.lang.Object ref = bssid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      bssid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SCREENTYPE_FIELD_NUMBER = 36;
  private int screenType_ = 0;
  /**
   * <code>int32 screenType = 36;</code>
   * @return The screenType.
   */
  @java.lang.Override
  public int getScreenType() {
    return screenType_;
  }

  public static final int CHECKEDAPPS_FIELD_NUMBER = 37;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList checkedApps_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <code>repeated string checkedApps = 37;</code>
   * @return A list containing the checkedApps.
   */
  public com.google.protobuf.ProtocolStringList
      getCheckedAppsList() {
    return checkedApps_;
  }
  /**
   * <code>repeated string checkedApps = 37;</code>
   * @return The count of checkedApps.
   */
  public int getCheckedAppsCount() {
    return checkedApps_.size();
  }
  /**
   * <code>repeated string checkedApps = 37;</code>
   * @param index The index of the element to return.
   * @return The checkedApps at the given index.
   */
  public java.lang.String getCheckedApps(int index) {
    return checkedApps_.get(index);
  }
  /**
   * <code>repeated string checkedApps = 37;</code>
   * @param index The index of the value to return.
   * @return The bytes of the checkedApps at the given index.
   */
  public com.google.protobuf.ByteString
      getCheckedAppsBytes(int index) {
    return checkedApps_.getByteString(index);
  }

  public static final int HMSVER_FIELD_NUMBER = 38;
  @SuppressWarnings("serial")
  private volatile java.lang.Object hmsVer_ = "";
  /**
   * <code>string hmsVer = 38;</code>
   * @return The hmsVer.
   */
  @java.lang.Override
  public java.lang.String getHmsVer() {
    java.lang.Object ref = hmsVer_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      hmsVer_ = s;
      return s;
    }
  }
  /**
   * <code>string hmsVer = 38;</code>
   * @return The bytes for hmsVer.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHmsVerBytes() {
    java.lang.Object ref = hmsVer_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      hmsVer_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HWAGVER_FIELD_NUMBER = 39;
  @SuppressWarnings("serial")
  private volatile java.lang.Object hwagVer_ = "";
  /**
   * <code>string hwagVer = 39;</code>
   * @return The hwagVer.
   */
  @java.lang.Override
  public java.lang.String getHwagVer() {
    java.lang.Object ref = hwagVer_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      hwagVer_ = s;
      return s;
    }
  }
  /**
   * <code>string hwagVer = 39;</code>
   * @return The bytes for hwagVer.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHwagVerBytes() {
    java.lang.Object ref = hwagVer_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      hwagVer_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DEVICENAME_FIELD_NUMBER = 40;
  @SuppressWarnings("serial")
  private volatile java.lang.Object deviceName_ = "";
  /**
   * <code>string deviceName = 40;</code>
   * @return The deviceName.
   */
  @java.lang.Override
  public java.lang.String getDeviceName() {
    java.lang.Object ref = deviceName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceName_ = s;
      return s;
    }
  }
  /**
   * <code>string deviceName = 40;</code>
   * @return The bytes for deviceName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeviceNameBytes() {
    java.lang.Object ref = deviceName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIMEZONE_FIELD_NUMBER = 41;
  @SuppressWarnings("serial")
  private volatile java.lang.Object timeZone_ = "";
  /**
   * <code>string timeZone = 41;</code>
   * @return The timeZone.
   */
  @java.lang.Override
  public java.lang.String getTimeZone() {
    java.lang.Object ref = timeZone_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      timeZone_ = s;
      return s;
    }
  }
  /**
   * <code>string timeZone = 41;</code>
   * @return The bytes for timeZone.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTimeZoneBytes() {
    java.lang.Object ref = timeZone_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      timeZone_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MEMORYSIZE_FIELD_NUMBER = 42;
  private long memorySize_ = 0L;
  /**
   * <code>int64 memorySize = 42;</code>
   * @return The memorySize.
   */
  @java.lang.Override
  public long getMemorySize() {
    return memorySize_;
  }

  public static final int HARDDISKSIZE_FIELD_NUMBER = 43;
  private long hardDiskSize_ = 0L;
  /**
   * <code>int64 hardDiskSize = 43;</code>
   * @return The hardDiskSize.
   */
  @java.lang.Override
  public long getHardDiskSize() {
    return hardDiskSize_;
  }

  public static final int APPSTOREVER_FIELD_NUMBER = 44;
  @SuppressWarnings("serial")
  private volatile java.lang.Object appStoreVer_ = "";
  /**
   * <code>string appStoreVer = 44;</code>
   * @return The appStoreVer.
   */
  @java.lang.Override
  public java.lang.String getAppStoreVer() {
    java.lang.Object ref = appStoreVer_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      appStoreVer_ = s;
      return s;
    }
  }
  /**
   * <code>string appStoreVer = 44;</code>
   * @return The bytes for appStoreVer.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppStoreVerBytes() {
    java.lang.Object ref = appStoreVer_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appStoreVer_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APILEVEL_FIELD_NUMBER = 45;
  @SuppressWarnings("serial")
  private volatile java.lang.Object apiLevel_ = "";
  /**
   * <code>string apiLevel = 45;</code>
   * @return The apiLevel.
   */
  @java.lang.Override
  public java.lang.String getApiLevel() {
    java.lang.Object ref = apiLevel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      apiLevel_ = s;
      return s;
    }
  }
  /**
   * <code>string apiLevel = 45;</code>
   * @return The bytes for apiLevel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getApiLevelBytes() {
    java.lang.Object ref = apiLevel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      apiLevel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int WIFIMAC_FIELD_NUMBER = 46;
  @SuppressWarnings("serial")
  private volatile java.lang.Object wifiMac_ = "";
  /**
   * <code>string wifiMac = 46;</code>
   * @return The wifiMac.
   */
  @java.lang.Override
  public java.lang.String getWifiMac() {
    java.lang.Object ref = wifiMac_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      wifiMac_ = s;
      return s;
    }
  }
  /**
   * <code>string wifiMac = 46;</code>
   * @return The bytes for wifiMac.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWifiMacBytes() {
    java.lang.Object ref = wifiMac_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      wifiMac_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STARTUPTIME_FIELD_NUMBER = 47;
  @SuppressWarnings("serial")
  private volatile java.lang.Object startupTime_ = "";
  /**
   * <code>string startupTime = 47;</code>
   * @return The startupTime.
   */
  @java.lang.Override
  public java.lang.String getStartupTime() {
    java.lang.Object ref = startupTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      startupTime_ = s;
      return s;
    }
  }
  /**
   * <code>string startupTime = 47;</code>
   * @return The bytes for startupTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartupTimeBytes() {
    java.lang.Object ref = startupTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      startupTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BOOTTIME_FIELD_NUMBER = 48;
  @SuppressWarnings("serial")
  private volatile java.lang.Object bootTime_ = "";
  /**
   * <code>string bootTime = 48;</code>
   * @return The bootTime.
   */
  @java.lang.Override
  public java.lang.String getBootTime() {
    java.lang.Object ref = bootTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      bootTime_ = s;
      return s;
    }
  }
  /**
   * <code>string bootTime = 48;</code>
   * @return The bytes for bootTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBootTimeBytes() {
    java.lang.Object ref = bootTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      bootTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPDATETIME_FIELD_NUMBER = 49;
  @SuppressWarnings("serial")
  private volatile java.lang.Object updateTime_ = "";
  /**
   * <code>string updateTime = 49;</code>
   * @return The updateTime.
   */
  @java.lang.Override
  public java.lang.String getUpdateTime() {
    java.lang.Object ref = updateTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      updateTime_ = s;
      return s;
    }
  }
  /**
   * <code>string updateTime = 49;</code>
   * @return The bytes for updateTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUpdateTimeBytes() {
    java.lang.Object ref = updateTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      updateTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BIRTHTIME_FIELD_NUMBER = 50;
  @SuppressWarnings("serial")
  private volatile java.lang.Object birthTime_ = "";
  /**
   * <code>string birthTime = 50;</code>
   * @return The birthTime.
   */
  @java.lang.Override
  public java.lang.String getBirthTime() {
    java.lang.Object ref = birthTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      birthTime_ = s;
      return s;
    }
  }
  /**
   * <code>string birthTime = 50;</code>
   * @return The bytes for birthTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBirthTimeBytes() {
    java.lang.Object ref = birthTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      birthTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IDFAMD5_FIELD_NUMBER = 51;
  @SuppressWarnings("serial")
  private volatile java.lang.Object idfaMd5_ = "";
  /**
   * <code>string idfaMd5 = 51;</code>
   * @return The idfaMd5.
   */
  @java.lang.Override
  public java.lang.String getIdfaMd5() {
    java.lang.Object ref = idfaMd5_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      idfaMd5_ = s;
      return s;
    }
  }
  /**
   * <code>string idfaMd5 = 51;</code>
   * @return The bytes for idfaMd5.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdfaMd5Bytes() {
    java.lang.Object ref = idfaMd5_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      idfaMd5_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OAIDMD5_FIELD_NUMBER = 52;
  @SuppressWarnings("serial")
  private volatile java.lang.Object oaidMd5_ = "";
  /**
   * <code>string oaidMd5 = 52;</code>
   * @return The oaidMd5.
   */
  @java.lang.Override
  public java.lang.String getOaidMd5() {
    java.lang.Object ref = oaidMd5_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      oaidMd5_ = s;
      return s;
    }
  }
  /**
   * <code>string oaidMd5 = 52;</code>
   * @return The bytes for oaidMd5.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOaidMd5Bytes() {
    java.lang.Object ref = oaidMd5_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      oaidMd5_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ALIAAID_FIELD_NUMBER = 53;
  @SuppressWarnings("serial")
  private volatile java.lang.Object aliAaid_ = "";
  /**
   * <code>string aliAaid = 53;</code>
   * @return The aliAaid.
   */
  @java.lang.Override
  public java.lang.String getAliAaid() {
    java.lang.Object ref = aliAaid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      aliAaid_ = s;
      return s;
    }
  }
  /**
   * <code>string aliAaid = 53;</code>
   * @return The bytes for aliAaid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAliAaidBytes() {
    java.lang.Object ref = aliAaid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      aliAaid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OPENUDID_FIELD_NUMBER = 54;
  @SuppressWarnings("serial")
  private volatile java.lang.Object openudid_ = "";
  /**
   * <code>string openudid = 54;</code>
   * @return The openudid.
   */
  @java.lang.Override
  public java.lang.String getOpenudid() {
    java.lang.Object ref = openudid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      openudid_ = s;
      return s;
    }
  }
  /**
   * <code>string openudid = 54;</code>
   * @return The bytes for openudid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOpenudidBytes() {
    java.lang.Object ref = openudid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      openudid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MIUIVERSION_FIELD_NUMBER = 55;
  @SuppressWarnings("serial")
  private volatile java.lang.Object miuiVersion_ = "";
  /**
   * <code>string miuiVersion = 55;</code>
   * @return The miuiVersion.
   */
  @java.lang.Override
  public java.lang.String getMiuiVersion() {
    java.lang.Object ref = miuiVersion_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      miuiVersion_ = s;
      return s;
    }
  }
  /**
   * <code>string miuiVersion = 55;</code>
   * @return The bytes for miuiVersion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMiuiVersionBytes() {
    java.lang.Object ref = miuiVersion_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      miuiVersion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CPUFREQ_FIELD_NUMBER = 56;
  private float cpuFreq_ = 0F;
  /**
   * <code>float cpuFreq = 56;</code>
   * @return The cpuFreq.
   */
  @java.lang.Override
  public float getCpuFreq() {
    return cpuFreq_;
  }

  public static final int CPUNUMBER_FIELD_NUMBER = 57;
  private int cpuNumber_ = 0;
  /**
   * <code>int32 cpuNumber = 57;</code>
   * @return The cpuNumber.
   */
  @java.lang.Override
  public int getCpuNumber() {
    return cpuNumber_;
  }

  public static final int BATTERYSTATUS_FIELD_NUMBER = 58;
  private int batteryStatus_ = 0;
  /**
   * <code>int32 batteryStatus = 58;</code>
   * @return The batteryStatus.
   */
  @java.lang.Override
  public int getBatteryStatus() {
    return batteryStatus_;
  }

  public static final int BATTERYPOWER_FIELD_NUMBER = 59;
  private int batteryPower_ = 0;
  /**
   * <code>int32 batteryPower = 59;</code>
   * @return The batteryPower.
   */
  @java.lang.Override
  public int getBatteryPower() {
    return batteryPower_;
  }

  public static final int CON_FIELD_NUMBER = 60;
  @SuppressWarnings("serial")
  private volatile java.lang.Object con_ = "";
  /**
   * <code>string con = 60;</code>
   * @return The con.
   */
  @java.lang.Override
  public java.lang.String getCon() {
    java.lang.Object ref = con_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      con_ = s;
      return s;
    }
  }
  /**
   * <code>string con = 60;</code>
   * @return The bytes for con.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConBytes() {
    java.lang.Object ref = con_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      con_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LAN_FIELD_NUMBER = 61;
  @SuppressWarnings("serial")
  private volatile java.lang.Object lan_ = "";
  /**
   * <code>string lan = 61;</code>
   * @return The lan.
   */
  @java.lang.Override
  public java.lang.String getLan() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      lan_ = s;
      return s;
    }
  }
  /**
   * <code>string lan = 61;</code>
   * @return The bytes for lan.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLanBytes() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      lan_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HARDWAREMODEL_FIELD_NUMBER = 62;
  @SuppressWarnings("serial")
  private volatile java.lang.Object hardwareModel_ = "";
  /**
   * <code>string hardwareModel = 62;</code>
   * @return The hardwareModel.
   */
  @java.lang.Override
  public java.lang.String getHardwareModel() {
    java.lang.Object ref = hardwareModel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      hardwareModel_ = s;
      return s;
    }
  }
  /**
   * <code>string hardwareModel = 62;</code>
   * @return The bytes for hardwareModel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHardwareModelBytes() {
    java.lang.Object ref = hardwareModel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      hardwareModel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AUTHSTATUS_FIELD_NUMBER = 63;
  private int authStatus_ = 0;
  /**
   * <code>int32 authStatus = 63;</code>
   * @return The authStatus.
   */
  @java.lang.Override
  public int getAuthStatus() {
    return authStatus_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ua_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, ua_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(2, getGeo());
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ip_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, ip_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ipv6_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, ipv6_);
    }
    if (deviceType_ != cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType.MobReqDeviceType_UnKnow.getNumber()) {
      output.writeEnum(5, deviceType_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(make_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, make_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(mode_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, mode_);
    }
    if (os_ != cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType.MobReqDeviceOsType_UnKnow.getNumber()) {
      output.writeEnum(8, os_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(osv_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, osv_);
    }
    if (carrier_ != cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType.MobReqDeviceCarrierType_UnKnow.getNumber()) {
      output.writeEnum(10, carrier_);
    }
    if (connectionType_ != cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType.MobReqDeviceConnectionType_UnKnow.getNumber()) {
      output.writeEnum(11, connectionType_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ifa_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 12, ifa_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(imeimd5_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 13, imeimd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(idfv_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 14, idfv_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(adidmd5_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 15, adidmd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(mac_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 16, mac_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(macmd5_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 17, macmd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(adid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 18, adid_);
    }
    if (w_ != 0) {
      output.writeInt32(19, w_);
    }
    if (h_ != 0) {
      output.writeInt32(20, h_);
    }
    if (asw_ != 0) {
      output.writeInt32(21, asw_);
    }
    if (ash_ != 0) {
      output.writeInt32(22, ash_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(oaid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 23, oaid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(brand_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 24, brand_);
    }
    if (java.lang.Float.floatToRawIntBits(dpi_) != 0) {
      output.writeFloat(25, dpi_);
    }
    if (ppi_ != 0) {
      output.writeInt32(26, ppi_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(caid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 27, caid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(caidVersion_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 28, caidVersion_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bootMark_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 29, bootMark_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(updateMark_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 30, updateMark_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(romVersion_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 31, romVersion_);
    }
    if (sysCompileTs_ != 0L) {
      output.writeInt64(32, sysCompileTs_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(hmscore_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 33, hmscore_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(paid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 34, paid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bssid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 35, bssid_);
    }
    if (screenType_ != 0) {
      output.writeInt32(36, screenType_);
    }
    for (int i = 0; i < checkedApps_.size(); i++) {
      com.google.protobuf.GeneratedMessage.writeString(output, 37, checkedApps_.getRaw(i));
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(hmsVer_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 38, hmsVer_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(hwagVer_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 39, hwagVer_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(deviceName_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 40, deviceName_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(timeZone_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 41, timeZone_);
    }
    if (memorySize_ != 0L) {
      output.writeInt64(42, memorySize_);
    }
    if (hardDiskSize_ != 0L) {
      output.writeInt64(43, hardDiskSize_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appStoreVer_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 44, appStoreVer_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(apiLevel_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 45, apiLevel_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(wifiMac_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 46, wifiMac_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(startupTime_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 47, startupTime_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bootTime_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 48, bootTime_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(updateTime_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 49, updateTime_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(birthTime_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 50, birthTime_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(idfaMd5_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 51, idfaMd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(oaidMd5_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 52, oaidMd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(aliAaid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 53, aliAaid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(openudid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 54, openudid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(miuiVersion_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 55, miuiVersion_);
    }
    if (java.lang.Float.floatToRawIntBits(cpuFreq_) != 0) {
      output.writeFloat(56, cpuFreq_);
    }
    if (cpuNumber_ != 0) {
      output.writeInt32(57, cpuNumber_);
    }
    if (batteryStatus_ != 0) {
      output.writeInt32(58, batteryStatus_);
    }
    if (batteryPower_ != 0) {
      output.writeInt32(59, batteryPower_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(con_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 60, con_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(lan_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 61, lan_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(hardwareModel_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 62, hardwareModel_);
    }
    if (authStatus_ != 0) {
      output.writeInt32(63, authStatus_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ua_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, ua_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getGeo());
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ip_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, ip_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ipv6_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, ipv6_);
    }
    if (deviceType_ != cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType.MobReqDeviceType_UnKnow.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(5, deviceType_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(make_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, make_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(mode_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, mode_);
    }
    if (os_ != cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType.MobReqDeviceOsType_UnKnow.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(8, os_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(osv_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, osv_);
    }
    if (carrier_ != cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType.MobReqDeviceCarrierType_UnKnow.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(10, carrier_);
    }
    if (connectionType_ != cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType.MobReqDeviceConnectionType_UnKnow.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(11, connectionType_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ifa_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(12, ifa_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(imeimd5_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(13, imeimd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(idfv_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(14, idfv_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(adidmd5_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(15, adidmd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(mac_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(16, mac_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(macmd5_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(17, macmd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(adid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(18, adid_);
    }
    if (w_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(19, w_);
    }
    if (h_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(20, h_);
    }
    if (asw_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(21, asw_);
    }
    if (ash_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(22, ash_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(oaid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(23, oaid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(brand_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(24, brand_);
    }
    if (java.lang.Float.floatToRawIntBits(dpi_) != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(25, dpi_);
    }
    if (ppi_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(26, ppi_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(caid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(27, caid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(caidVersion_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(28, caidVersion_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bootMark_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(29, bootMark_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(updateMark_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(30, updateMark_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(romVersion_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(31, romVersion_);
    }
    if (sysCompileTs_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(32, sysCompileTs_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(hmscore_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(33, hmscore_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(paid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(34, paid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bssid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(35, bssid_);
    }
    if (screenType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(36, screenType_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < checkedApps_.size(); i++) {
        dataSize += computeStringSizeNoTag(checkedApps_.getRaw(i));
      }
      size += dataSize;
      size += 2 * getCheckedAppsList().size();
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(hmsVer_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(38, hmsVer_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(hwagVer_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(39, hwagVer_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(deviceName_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(40, deviceName_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(timeZone_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(41, timeZone_);
    }
    if (memorySize_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(42, memorySize_);
    }
    if (hardDiskSize_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(43, hardDiskSize_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appStoreVer_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(44, appStoreVer_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(apiLevel_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(45, apiLevel_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(wifiMac_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(46, wifiMac_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(startupTime_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(47, startupTime_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bootTime_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(48, bootTime_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(updateTime_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(49, updateTime_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(birthTime_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(50, birthTime_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(idfaMd5_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(51, idfaMd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(oaidMd5_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(52, oaidMd5_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(aliAaid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(53, aliAaid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(openudid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(54, openudid_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(miuiVersion_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(55, miuiVersion_);
    }
    if (java.lang.Float.floatToRawIntBits(cpuFreq_) != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(56, cpuFreq_);
    }
    if (cpuNumber_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(57, cpuNumber_);
    }
    if (batteryStatus_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(58, batteryStatus_);
    }
    if (batteryPower_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(59, batteryPower_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(con_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(60, con_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(lan_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(61, lan_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(hardwareModel_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(62, hardwareModel_);
    }
    if (authStatus_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(63, authStatus_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.taken.ad.logic.adv.yaya.dto.ReqDevice)) {
      return super.equals(obj);
    }
    cn.taken.ad.logic.adv.yaya.dto.ReqDevice other = (cn.taken.ad.logic.adv.yaya.dto.ReqDevice) obj;

    if (!getUa()
        .equals(other.getUa())) return false;
    if (hasGeo() != other.hasGeo()) return false;
    if (hasGeo()) {
      if (!getGeo()
          .equals(other.getGeo())) return false;
    }
    if (!getIp()
        .equals(other.getIp())) return false;
    if (!getIpv6()
        .equals(other.getIpv6())) return false;
    if (deviceType_ != other.deviceType_) return false;
    if (!getMake()
        .equals(other.getMake())) return false;
    if (!getMode()
        .equals(other.getMode())) return false;
    if (os_ != other.os_) return false;
    if (!getOsv()
        .equals(other.getOsv())) return false;
    if (carrier_ != other.carrier_) return false;
    if (connectionType_ != other.connectionType_) return false;
    if (!getIfa()
        .equals(other.getIfa())) return false;
    if (!getImeimd5()
        .equals(other.getImeimd5())) return false;
    if (!getIdfv()
        .equals(other.getIdfv())) return false;
    if (!getAdidmd5()
        .equals(other.getAdidmd5())) return false;
    if (!getMac()
        .equals(other.getMac())) return false;
    if (!getMacmd5()
        .equals(other.getMacmd5())) return false;
    if (!getAdid()
        .equals(other.getAdid())) return false;
    if (getW()
        != other.getW()) return false;
    if (getH()
        != other.getH()) return false;
    if (getAsw()
        != other.getAsw()) return false;
    if (getAsh()
        != other.getAsh()) return false;
    if (!getOaid()
        .equals(other.getOaid())) return false;
    if (!getBrand()
        .equals(other.getBrand())) return false;
    if (java.lang.Float.floatToIntBits(getDpi())
        != java.lang.Float.floatToIntBits(
            other.getDpi())) return false;
    if (getPpi()
        != other.getPpi()) return false;
    if (!getCaid()
        .equals(other.getCaid())) return false;
    if (!getCaidVersion()
        .equals(other.getCaidVersion())) return false;
    if (!getBootMark()
        .equals(other.getBootMark())) return false;
    if (!getUpdateMark()
        .equals(other.getUpdateMark())) return false;
    if (!getRomVersion()
        .equals(other.getRomVersion())) return false;
    if (getSysCompileTs()
        != other.getSysCompileTs()) return false;
    if (!getHmscore()
        .equals(other.getHmscore())) return false;
    if (!getPaid()
        .equals(other.getPaid())) return false;
    if (!getBssid()
        .equals(other.getBssid())) return false;
    if (getScreenType()
        != other.getScreenType()) return false;
    if (!getCheckedAppsList()
        .equals(other.getCheckedAppsList())) return false;
    if (!getHmsVer()
        .equals(other.getHmsVer())) return false;
    if (!getHwagVer()
        .equals(other.getHwagVer())) return false;
    if (!getDeviceName()
        .equals(other.getDeviceName())) return false;
    if (!getTimeZone()
        .equals(other.getTimeZone())) return false;
    if (getMemorySize()
        != other.getMemorySize()) return false;
    if (getHardDiskSize()
        != other.getHardDiskSize()) return false;
    if (!getAppStoreVer()
        .equals(other.getAppStoreVer())) return false;
    if (!getApiLevel()
        .equals(other.getApiLevel())) return false;
    if (!getWifiMac()
        .equals(other.getWifiMac())) return false;
    if (!getStartupTime()
        .equals(other.getStartupTime())) return false;
    if (!getBootTime()
        .equals(other.getBootTime())) return false;
    if (!getUpdateTime()
        .equals(other.getUpdateTime())) return false;
    if (!getBirthTime()
        .equals(other.getBirthTime())) return false;
    if (!getIdfaMd5()
        .equals(other.getIdfaMd5())) return false;
    if (!getOaidMd5()
        .equals(other.getOaidMd5())) return false;
    if (!getAliAaid()
        .equals(other.getAliAaid())) return false;
    if (!getOpenudid()
        .equals(other.getOpenudid())) return false;
    if (!getMiuiVersion()
        .equals(other.getMiuiVersion())) return false;
    if (java.lang.Float.floatToIntBits(getCpuFreq())
        != java.lang.Float.floatToIntBits(
            other.getCpuFreq())) return false;
    if (getCpuNumber()
        != other.getCpuNumber()) return false;
    if (getBatteryStatus()
        != other.getBatteryStatus()) return false;
    if (getBatteryPower()
        != other.getBatteryPower()) return false;
    if (!getCon()
        .equals(other.getCon())) return false;
    if (!getLan()
        .equals(other.getLan())) return false;
    if (!getHardwareModel()
        .equals(other.getHardwareModel())) return false;
    if (getAuthStatus()
        != other.getAuthStatus()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + UA_FIELD_NUMBER;
    hash = (53 * hash) + getUa().hashCode();
    if (hasGeo()) {
      hash = (37 * hash) + GEO_FIELD_NUMBER;
      hash = (53 * hash) + getGeo().hashCode();
    }
    hash = (37 * hash) + IP_FIELD_NUMBER;
    hash = (53 * hash) + getIp().hashCode();
    hash = (37 * hash) + IPV6_FIELD_NUMBER;
    hash = (53 * hash) + getIpv6().hashCode();
    hash = (37 * hash) + DEVICETYPE_FIELD_NUMBER;
    hash = (53 * hash) + deviceType_;
    hash = (37 * hash) + MAKE_FIELD_NUMBER;
    hash = (53 * hash) + getMake().hashCode();
    hash = (37 * hash) + MODE_FIELD_NUMBER;
    hash = (53 * hash) + getMode().hashCode();
    hash = (37 * hash) + OS_FIELD_NUMBER;
    hash = (53 * hash) + os_;
    hash = (37 * hash) + OSV_FIELD_NUMBER;
    hash = (53 * hash) + getOsv().hashCode();
    hash = (37 * hash) + CARRIER_FIELD_NUMBER;
    hash = (53 * hash) + carrier_;
    hash = (37 * hash) + CONNECTIONTYPE_FIELD_NUMBER;
    hash = (53 * hash) + connectionType_;
    hash = (37 * hash) + IFA_FIELD_NUMBER;
    hash = (53 * hash) + getIfa().hashCode();
    hash = (37 * hash) + IMEIMD5_FIELD_NUMBER;
    hash = (53 * hash) + getImeimd5().hashCode();
    hash = (37 * hash) + IDFV_FIELD_NUMBER;
    hash = (53 * hash) + getIdfv().hashCode();
    hash = (37 * hash) + ADIDMD5_FIELD_NUMBER;
    hash = (53 * hash) + getAdidmd5().hashCode();
    hash = (37 * hash) + MAC_FIELD_NUMBER;
    hash = (53 * hash) + getMac().hashCode();
    hash = (37 * hash) + MACMD5_FIELD_NUMBER;
    hash = (53 * hash) + getMacmd5().hashCode();
    hash = (37 * hash) + ADID_FIELD_NUMBER;
    hash = (53 * hash) + getAdid().hashCode();
    hash = (37 * hash) + W_FIELD_NUMBER;
    hash = (53 * hash) + getW();
    hash = (37 * hash) + H_FIELD_NUMBER;
    hash = (53 * hash) + getH();
    hash = (37 * hash) + ASW_FIELD_NUMBER;
    hash = (53 * hash) + getAsw();
    hash = (37 * hash) + ASH_FIELD_NUMBER;
    hash = (53 * hash) + getAsh();
    hash = (37 * hash) + OAID_FIELD_NUMBER;
    hash = (53 * hash) + getOaid().hashCode();
    hash = (37 * hash) + BRAND_FIELD_NUMBER;
    hash = (53 * hash) + getBrand().hashCode();
    hash = (37 * hash) + DPI_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getDpi());
    hash = (37 * hash) + PPI_FIELD_NUMBER;
    hash = (53 * hash) + getPpi();
    hash = (37 * hash) + CAID_FIELD_NUMBER;
    hash = (53 * hash) + getCaid().hashCode();
    hash = (37 * hash) + CAIDVERSION_FIELD_NUMBER;
    hash = (53 * hash) + getCaidVersion().hashCode();
    hash = (37 * hash) + BOOTMARK_FIELD_NUMBER;
    hash = (53 * hash) + getBootMark().hashCode();
    hash = (37 * hash) + UPDATEMARK_FIELD_NUMBER;
    hash = (53 * hash) + getUpdateMark().hashCode();
    hash = (37 * hash) + ROMVERSION_FIELD_NUMBER;
    hash = (53 * hash) + getRomVersion().hashCode();
    hash = (37 * hash) + SYSCOMPILETS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSysCompileTs());
    hash = (37 * hash) + HMSCORE_FIELD_NUMBER;
    hash = (53 * hash) + getHmscore().hashCode();
    hash = (37 * hash) + PAID_FIELD_NUMBER;
    hash = (53 * hash) + getPaid().hashCode();
    hash = (37 * hash) + BSSID_FIELD_NUMBER;
    hash = (53 * hash) + getBssid().hashCode();
    hash = (37 * hash) + SCREENTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getScreenType();
    if (getCheckedAppsCount() > 0) {
      hash = (37 * hash) + CHECKEDAPPS_FIELD_NUMBER;
      hash = (53 * hash) + getCheckedAppsList().hashCode();
    }
    hash = (37 * hash) + HMSVER_FIELD_NUMBER;
    hash = (53 * hash) + getHmsVer().hashCode();
    hash = (37 * hash) + HWAGVER_FIELD_NUMBER;
    hash = (53 * hash) + getHwagVer().hashCode();
    hash = (37 * hash) + DEVICENAME_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceName().hashCode();
    hash = (37 * hash) + TIMEZONE_FIELD_NUMBER;
    hash = (53 * hash) + getTimeZone().hashCode();
    hash = (37 * hash) + MEMORYSIZE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getMemorySize());
    hash = (37 * hash) + HARDDISKSIZE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getHardDiskSize());
    hash = (37 * hash) + APPSTOREVER_FIELD_NUMBER;
    hash = (53 * hash) + getAppStoreVer().hashCode();
    hash = (37 * hash) + APILEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getApiLevel().hashCode();
    hash = (37 * hash) + WIFIMAC_FIELD_NUMBER;
    hash = (53 * hash) + getWifiMac().hashCode();
    hash = (37 * hash) + STARTUPTIME_FIELD_NUMBER;
    hash = (53 * hash) + getStartupTime().hashCode();
    hash = (37 * hash) + BOOTTIME_FIELD_NUMBER;
    hash = (53 * hash) + getBootTime().hashCode();
    hash = (37 * hash) + UPDATETIME_FIELD_NUMBER;
    hash = (53 * hash) + getUpdateTime().hashCode();
    hash = (37 * hash) + BIRTHTIME_FIELD_NUMBER;
    hash = (53 * hash) + getBirthTime().hashCode();
    hash = (37 * hash) + IDFAMD5_FIELD_NUMBER;
    hash = (53 * hash) + getIdfaMd5().hashCode();
    hash = (37 * hash) + OAIDMD5_FIELD_NUMBER;
    hash = (53 * hash) + getOaidMd5().hashCode();
    hash = (37 * hash) + ALIAAID_FIELD_NUMBER;
    hash = (53 * hash) + getAliAaid().hashCode();
    hash = (37 * hash) + OPENUDID_FIELD_NUMBER;
    hash = (53 * hash) + getOpenudid().hashCode();
    hash = (37 * hash) + MIUIVERSION_FIELD_NUMBER;
    hash = (53 * hash) + getMiuiVersion().hashCode();
    hash = (37 * hash) + CPUFREQ_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getCpuFreq());
    hash = (37 * hash) + CPUNUMBER_FIELD_NUMBER;
    hash = (53 * hash) + getCpuNumber();
    hash = (37 * hash) + BATTERYSTATUS_FIELD_NUMBER;
    hash = (53 * hash) + getBatteryStatus();
    hash = (37 * hash) + BATTERYPOWER_FIELD_NUMBER;
    hash = (53 * hash) + getBatteryPower();
    hash = (37 * hash) + CON_FIELD_NUMBER;
    hash = (53 * hash) + getCon().hashCode();
    hash = (37 * hash) + LAN_FIELD_NUMBER;
    hash = (53 * hash) + getLan().hashCode();
    hash = (37 * hash) + HARDWAREMODEL_FIELD_NUMBER;
    hash = (53 * hash) + getHardwareModel().hashCode();
    hash = (37 * hash) + AUTHSTATUS_FIELD_NUMBER;
    hash = (53 * hash) + getAuthStatus();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.taken.ad.logic.adv.yaya.dto.ReqDevice prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code ReqDevice}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:ReqDevice)
      cn.taken.ad.logic.adv.yaya.dto.ReqDeviceOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqDevice_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqDevice_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.adv.yaya.dto.ReqDevice.class, cn.taken.ad.logic.adv.yaya.dto.ReqDevice.Builder.class);
    }

    // Construct using cn.taken.ad.logic.adv.yaya.dto.ReqDevice.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getGeoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      bitField1_ = 0;
      ua_ = "";
      geo_ = null;
      if (geoBuilder_ != null) {
        geoBuilder_.dispose();
        geoBuilder_ = null;
      }
      ip_ = "";
      ipv6_ = "";
      deviceType_ = 0;
      make_ = "";
      mode_ = "";
      os_ = 0;
      osv_ = "";
      carrier_ = 0;
      connectionType_ = 0;
      ifa_ = "";
      imeimd5_ = "";
      idfv_ = "";
      adidmd5_ = "";
      mac_ = "";
      macmd5_ = "";
      adid_ = "";
      w_ = 0;
      h_ = 0;
      asw_ = 0;
      ash_ = 0;
      oaid_ = "";
      brand_ = "";
      dpi_ = 0F;
      ppi_ = 0;
      caid_ = "";
      caidVersion_ = "";
      bootMark_ = "";
      updateMark_ = "";
      romVersion_ = "";
      sysCompileTs_ = 0L;
      hmscore_ = "";
      paid_ = "";
      bssid_ = "";
      screenType_ = 0;
      checkedApps_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      hmsVer_ = "";
      hwagVer_ = "";
      deviceName_ = "";
      timeZone_ = "";
      memorySize_ = 0L;
      hardDiskSize_ = 0L;
      appStoreVer_ = "";
      apiLevel_ = "";
      wifiMac_ = "";
      startupTime_ = "";
      bootTime_ = "";
      updateTime_ = "";
      birthTime_ = "";
      idfaMd5_ = "";
      oaidMd5_ = "";
      aliAaid_ = "";
      openudid_ = "";
      miuiVersion_ = "";
      cpuFreq_ = 0F;
      cpuNumber_ = 0;
      batteryStatus_ = 0;
      batteryPower_ = 0;
      con_ = "";
      lan_ = "";
      hardwareModel_ = "";
      authStatus_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqDevice_descriptor;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.ReqDevice getDefaultInstanceForType() {
      return cn.taken.ad.logic.adv.yaya.dto.ReqDevice.getDefaultInstance();
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.ReqDevice build() {
      cn.taken.ad.logic.adv.yaya.dto.ReqDevice result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.ReqDevice buildPartial() {
      cn.taken.ad.logic.adv.yaya.dto.ReqDevice result = new cn.taken.ad.logic.adv.yaya.dto.ReqDevice(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      if (bitField1_ != 0) { buildPartial1(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(cn.taken.ad.logic.adv.yaya.dto.ReqDevice result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ua_ = ua_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.geo_ = geoBuilder_ == null
            ? geo_
            : geoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.ip_ = ip_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.ipv6_ = ipv6_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.deviceType_ = deviceType_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.make_ = make_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.mode_ = mode_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.os_ = os_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.osv_ = osv_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.carrier_ = carrier_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.connectionType_ = connectionType_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.ifa_ = ifa_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.imeimd5_ = imeimd5_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.idfv_ = idfv_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.adidmd5_ = adidmd5_;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.mac_ = mac_;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.macmd5_ = macmd5_;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.adid_ = adid_;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.w_ = w_;
      }
      if (((from_bitField0_ & 0x00080000) != 0)) {
        result.h_ = h_;
      }
      if (((from_bitField0_ & 0x00100000) != 0)) {
        result.asw_ = asw_;
      }
      if (((from_bitField0_ & 0x00200000) != 0)) {
        result.ash_ = ash_;
      }
      if (((from_bitField0_ & 0x00400000) != 0)) {
        result.oaid_ = oaid_;
      }
      if (((from_bitField0_ & 0x00800000) != 0)) {
        result.brand_ = brand_;
      }
      if (((from_bitField0_ & 0x01000000) != 0)) {
        result.dpi_ = dpi_;
      }
      if (((from_bitField0_ & 0x02000000) != 0)) {
        result.ppi_ = ppi_;
      }
      if (((from_bitField0_ & 0x04000000) != 0)) {
        result.caid_ = caid_;
      }
      if (((from_bitField0_ & 0x08000000) != 0)) {
        result.caidVersion_ = caidVersion_;
      }
      if (((from_bitField0_ & 0x10000000) != 0)) {
        result.bootMark_ = bootMark_;
      }
      if (((from_bitField0_ & 0x20000000) != 0)) {
        result.updateMark_ = updateMark_;
      }
      if (((from_bitField0_ & 0x40000000) != 0)) {
        result.romVersion_ = romVersion_;
      }
      if (((from_bitField0_ & 0x80000000) != 0)) {
        result.sysCompileTs_ = sysCompileTs_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    private void buildPartial1(cn.taken.ad.logic.adv.yaya.dto.ReqDevice result) {
      int from_bitField1_ = bitField1_;
      if (((from_bitField1_ & 0x00000001) != 0)) {
        result.hmscore_ = hmscore_;
      }
      if (((from_bitField1_ & 0x00000002) != 0)) {
        result.paid_ = paid_;
      }
      if (((from_bitField1_ & 0x00000004) != 0)) {
        result.bssid_ = bssid_;
      }
      if (((from_bitField1_ & 0x00000008) != 0)) {
        result.screenType_ = screenType_;
      }
      if (((from_bitField1_ & 0x00000010) != 0)) {
        checkedApps_.makeImmutable();
        result.checkedApps_ = checkedApps_;
      }
      if (((from_bitField1_ & 0x00000020) != 0)) {
        result.hmsVer_ = hmsVer_;
      }
      if (((from_bitField1_ & 0x00000040) != 0)) {
        result.hwagVer_ = hwagVer_;
      }
      if (((from_bitField1_ & 0x00000080) != 0)) {
        result.deviceName_ = deviceName_;
      }
      if (((from_bitField1_ & 0x00000100) != 0)) {
        result.timeZone_ = timeZone_;
      }
      if (((from_bitField1_ & 0x00000200) != 0)) {
        result.memorySize_ = memorySize_;
      }
      if (((from_bitField1_ & 0x00000400) != 0)) {
        result.hardDiskSize_ = hardDiskSize_;
      }
      if (((from_bitField1_ & 0x00000800) != 0)) {
        result.appStoreVer_ = appStoreVer_;
      }
      if (((from_bitField1_ & 0x00001000) != 0)) {
        result.apiLevel_ = apiLevel_;
      }
      if (((from_bitField1_ & 0x00002000) != 0)) {
        result.wifiMac_ = wifiMac_;
      }
      if (((from_bitField1_ & 0x00004000) != 0)) {
        result.startupTime_ = startupTime_;
      }
      if (((from_bitField1_ & 0x00008000) != 0)) {
        result.bootTime_ = bootTime_;
      }
      if (((from_bitField1_ & 0x00010000) != 0)) {
        result.updateTime_ = updateTime_;
      }
      if (((from_bitField1_ & 0x00020000) != 0)) {
        result.birthTime_ = birthTime_;
      }
      if (((from_bitField1_ & 0x00040000) != 0)) {
        result.idfaMd5_ = idfaMd5_;
      }
      if (((from_bitField1_ & 0x00080000) != 0)) {
        result.oaidMd5_ = oaidMd5_;
      }
      if (((from_bitField1_ & 0x00100000) != 0)) {
        result.aliAaid_ = aliAaid_;
      }
      if (((from_bitField1_ & 0x00200000) != 0)) {
        result.openudid_ = openudid_;
      }
      if (((from_bitField1_ & 0x00400000) != 0)) {
        result.miuiVersion_ = miuiVersion_;
      }
      if (((from_bitField1_ & 0x00800000) != 0)) {
        result.cpuFreq_ = cpuFreq_;
      }
      if (((from_bitField1_ & 0x01000000) != 0)) {
        result.cpuNumber_ = cpuNumber_;
      }
      if (((from_bitField1_ & 0x02000000) != 0)) {
        result.batteryStatus_ = batteryStatus_;
      }
      if (((from_bitField1_ & 0x04000000) != 0)) {
        result.batteryPower_ = batteryPower_;
      }
      if (((from_bitField1_ & 0x08000000) != 0)) {
        result.con_ = con_;
      }
      if (((from_bitField1_ & 0x10000000) != 0)) {
        result.lan_ = lan_;
      }
      if (((from_bitField1_ & 0x20000000) != 0)) {
        result.hardwareModel_ = hardwareModel_;
      }
      if (((from_bitField1_ & 0x40000000) != 0)) {
        result.authStatus_ = authStatus_;
      }
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.taken.ad.logic.adv.yaya.dto.ReqDevice) {
        return mergeFrom((cn.taken.ad.logic.adv.yaya.dto.ReqDevice)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.taken.ad.logic.adv.yaya.dto.ReqDevice other) {
      if (other == cn.taken.ad.logic.adv.yaya.dto.ReqDevice.getDefaultInstance()) return this;
      if (!other.getUa().isEmpty()) {
        ua_ = other.ua_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasGeo()) {
        mergeGeo(other.getGeo());
      }
      if (!other.getIp().isEmpty()) {
        ip_ = other.ip_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getIpv6().isEmpty()) {
        ipv6_ = other.ipv6_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.deviceType_ != 0) {
        setDeviceTypeValue(other.getDeviceTypeValue());
      }
      if (!other.getMake().isEmpty()) {
        make_ = other.make_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getMode().isEmpty()) {
        mode_ = other.mode_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.os_ != 0) {
        setOsValue(other.getOsValue());
      }
      if (!other.getOsv().isEmpty()) {
        osv_ = other.osv_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (other.carrier_ != 0) {
        setCarrierValue(other.getCarrierValue());
      }
      if (other.connectionType_ != 0) {
        setConnectionTypeValue(other.getConnectionTypeValue());
      }
      if (!other.getIfa().isEmpty()) {
        ifa_ = other.ifa_;
        bitField0_ |= 0x00000800;
        onChanged();
      }
      if (!other.getImeimd5().isEmpty()) {
        imeimd5_ = other.imeimd5_;
        bitField0_ |= 0x00001000;
        onChanged();
      }
      if (!other.getIdfv().isEmpty()) {
        idfv_ = other.idfv_;
        bitField0_ |= 0x00002000;
        onChanged();
      }
      if (!other.getAdidmd5().isEmpty()) {
        adidmd5_ = other.adidmd5_;
        bitField0_ |= 0x00004000;
        onChanged();
      }
      if (!other.getMac().isEmpty()) {
        mac_ = other.mac_;
        bitField0_ |= 0x00008000;
        onChanged();
      }
      if (!other.getMacmd5().isEmpty()) {
        macmd5_ = other.macmd5_;
        bitField0_ |= 0x00010000;
        onChanged();
      }
      if (!other.getAdid().isEmpty()) {
        adid_ = other.adid_;
        bitField0_ |= 0x00020000;
        onChanged();
      }
      if (other.getW() != 0) {
        setW(other.getW());
      }
      if (other.getH() != 0) {
        setH(other.getH());
      }
      if (other.getAsw() != 0) {
        setAsw(other.getAsw());
      }
      if (other.getAsh() != 0) {
        setAsh(other.getAsh());
      }
      if (!other.getOaid().isEmpty()) {
        oaid_ = other.oaid_;
        bitField0_ |= 0x00400000;
        onChanged();
      }
      if (!other.getBrand().isEmpty()) {
        brand_ = other.brand_;
        bitField0_ |= 0x00800000;
        onChanged();
      }
      if (other.getDpi() != 0F) {
        setDpi(other.getDpi());
      }
      if (other.getPpi() != 0) {
        setPpi(other.getPpi());
      }
      if (!other.getCaid().isEmpty()) {
        caid_ = other.caid_;
        bitField0_ |= 0x04000000;
        onChanged();
      }
      if (!other.getCaidVersion().isEmpty()) {
        caidVersion_ = other.caidVersion_;
        bitField0_ |= 0x08000000;
        onChanged();
      }
      if (!other.getBootMark().isEmpty()) {
        bootMark_ = other.bootMark_;
        bitField0_ |= 0x10000000;
        onChanged();
      }
      if (!other.getUpdateMark().isEmpty()) {
        updateMark_ = other.updateMark_;
        bitField0_ |= 0x20000000;
        onChanged();
      }
      if (!other.getRomVersion().isEmpty()) {
        romVersion_ = other.romVersion_;
        bitField0_ |= 0x40000000;
        onChanged();
      }
      if (other.getSysCompileTs() != 0L) {
        setSysCompileTs(other.getSysCompileTs());
      }
      if (!other.getHmscore().isEmpty()) {
        hmscore_ = other.hmscore_;
        bitField1_ |= 0x00000001;
        onChanged();
      }
      if (!other.getPaid().isEmpty()) {
        paid_ = other.paid_;
        bitField1_ |= 0x00000002;
        onChanged();
      }
      if (!other.getBssid().isEmpty()) {
        bssid_ = other.bssid_;
        bitField1_ |= 0x00000004;
        onChanged();
      }
      if (other.getScreenType() != 0) {
        setScreenType(other.getScreenType());
      }
      if (!other.checkedApps_.isEmpty()) {
        if (checkedApps_.isEmpty()) {
          checkedApps_ = other.checkedApps_;
          bitField1_ |= 0x00000010;
        } else {
          ensureCheckedAppsIsMutable();
          checkedApps_.addAll(other.checkedApps_);
        }
        onChanged();
      }
      if (!other.getHmsVer().isEmpty()) {
        hmsVer_ = other.hmsVer_;
        bitField1_ |= 0x00000020;
        onChanged();
      }
      if (!other.getHwagVer().isEmpty()) {
        hwagVer_ = other.hwagVer_;
        bitField1_ |= 0x00000040;
        onChanged();
      }
      if (!other.getDeviceName().isEmpty()) {
        deviceName_ = other.deviceName_;
        bitField1_ |= 0x00000080;
        onChanged();
      }
      if (!other.getTimeZone().isEmpty()) {
        timeZone_ = other.timeZone_;
        bitField1_ |= 0x00000100;
        onChanged();
      }
      if (other.getMemorySize() != 0L) {
        setMemorySize(other.getMemorySize());
      }
      if (other.getHardDiskSize() != 0L) {
        setHardDiskSize(other.getHardDiskSize());
      }
      if (!other.getAppStoreVer().isEmpty()) {
        appStoreVer_ = other.appStoreVer_;
        bitField1_ |= 0x00000800;
        onChanged();
      }
      if (!other.getApiLevel().isEmpty()) {
        apiLevel_ = other.apiLevel_;
        bitField1_ |= 0x00001000;
        onChanged();
      }
      if (!other.getWifiMac().isEmpty()) {
        wifiMac_ = other.wifiMac_;
        bitField1_ |= 0x00002000;
        onChanged();
      }
      if (!other.getStartupTime().isEmpty()) {
        startupTime_ = other.startupTime_;
        bitField1_ |= 0x00004000;
        onChanged();
      }
      if (!other.getBootTime().isEmpty()) {
        bootTime_ = other.bootTime_;
        bitField1_ |= 0x00008000;
        onChanged();
      }
      if (!other.getUpdateTime().isEmpty()) {
        updateTime_ = other.updateTime_;
        bitField1_ |= 0x00010000;
        onChanged();
      }
      if (!other.getBirthTime().isEmpty()) {
        birthTime_ = other.birthTime_;
        bitField1_ |= 0x00020000;
        onChanged();
      }
      if (!other.getIdfaMd5().isEmpty()) {
        idfaMd5_ = other.idfaMd5_;
        bitField1_ |= 0x00040000;
        onChanged();
      }
      if (!other.getOaidMd5().isEmpty()) {
        oaidMd5_ = other.oaidMd5_;
        bitField1_ |= 0x00080000;
        onChanged();
      }
      if (!other.getAliAaid().isEmpty()) {
        aliAaid_ = other.aliAaid_;
        bitField1_ |= 0x00100000;
        onChanged();
      }
      if (!other.getOpenudid().isEmpty()) {
        openudid_ = other.openudid_;
        bitField1_ |= 0x00200000;
        onChanged();
      }
      if (!other.getMiuiVersion().isEmpty()) {
        miuiVersion_ = other.miuiVersion_;
        bitField1_ |= 0x00400000;
        onChanged();
      }
      if (other.getCpuFreq() != 0F) {
        setCpuFreq(other.getCpuFreq());
      }
      if (other.getCpuNumber() != 0) {
        setCpuNumber(other.getCpuNumber());
      }
      if (other.getBatteryStatus() != 0) {
        setBatteryStatus(other.getBatteryStatus());
      }
      if (other.getBatteryPower() != 0) {
        setBatteryPower(other.getBatteryPower());
      }
      if (!other.getCon().isEmpty()) {
        con_ = other.con_;
        bitField1_ |= 0x08000000;
        onChanged();
      }
      if (!other.getLan().isEmpty()) {
        lan_ = other.lan_;
        bitField1_ |= 0x10000000;
        onChanged();
      }
      if (!other.getHardwareModel().isEmpty()) {
        hardwareModel_ = other.hardwareModel_;
        bitField1_ |= 0x20000000;
        onChanged();
      }
      if (other.getAuthStatus() != 0) {
        setAuthStatus(other.getAuthStatus());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              ua_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getGeoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              ip_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              ipv6_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              deviceType_ = input.readEnum();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              make_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              mode_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 64: {
              os_ = input.readEnum();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              osv_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 80: {
              carrier_ = input.readEnum();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 88: {
              connectionType_ = input.readEnum();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 98: {
              ifa_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000800;
              break;
            } // case 98
            case 106: {
              imeimd5_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00001000;
              break;
            } // case 106
            case 114: {
              idfv_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00002000;
              break;
            } // case 114
            case 122: {
              adidmd5_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00004000;
              break;
            } // case 122
            case 130: {
              mac_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00008000;
              break;
            } // case 130
            case 138: {
              macmd5_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00010000;
              break;
            } // case 138
            case 146: {
              adid_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00020000;
              break;
            } // case 146
            case 152: {
              w_ = input.readInt32();
              bitField0_ |= 0x00040000;
              break;
            } // case 152
            case 160: {
              h_ = input.readInt32();
              bitField0_ |= 0x00080000;
              break;
            } // case 160
            case 168: {
              asw_ = input.readInt32();
              bitField0_ |= 0x00100000;
              break;
            } // case 168
            case 176: {
              ash_ = input.readInt32();
              bitField0_ |= 0x00200000;
              break;
            } // case 176
            case 186: {
              oaid_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00400000;
              break;
            } // case 186
            case 194: {
              brand_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00800000;
              break;
            } // case 194
            case 205: {
              dpi_ = input.readFloat();
              bitField0_ |= 0x01000000;
              break;
            } // case 205
            case 208: {
              ppi_ = input.readInt32();
              bitField0_ |= 0x02000000;
              break;
            } // case 208
            case 218: {
              caid_ = input.readStringRequireUtf8();
              bitField0_ |= 0x04000000;
              break;
            } // case 218
            case 226: {
              caidVersion_ = input.readStringRequireUtf8();
              bitField0_ |= 0x08000000;
              break;
            } // case 226
            case 234: {
              bootMark_ = input.readStringRequireUtf8();
              bitField0_ |= 0x10000000;
              break;
            } // case 234
            case 242: {
              updateMark_ = input.readStringRequireUtf8();
              bitField0_ |= 0x20000000;
              break;
            } // case 242
            case 250: {
              romVersion_ = input.readStringRequireUtf8();
              bitField0_ |= 0x40000000;
              break;
            } // case 250
            case 256: {
              sysCompileTs_ = input.readInt64();
              bitField0_ |= 0x80000000;
              break;
            } // case 256
            case 266: {
              hmscore_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00000001;
              break;
            } // case 266
            case 274: {
              paid_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00000002;
              break;
            } // case 274
            case 282: {
              bssid_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00000004;
              break;
            } // case 282
            case 288: {
              screenType_ = input.readInt32();
              bitField1_ |= 0x00000008;
              break;
            } // case 288
            case 298: {
              java.lang.String s = input.readStringRequireUtf8();
              ensureCheckedAppsIsMutable();
              checkedApps_.add(s);
              break;
            } // case 298
            case 306: {
              hmsVer_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00000020;
              break;
            } // case 306
            case 314: {
              hwagVer_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00000040;
              break;
            } // case 314
            case 322: {
              deviceName_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00000080;
              break;
            } // case 322
            case 330: {
              timeZone_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00000100;
              break;
            } // case 330
            case 336: {
              memorySize_ = input.readInt64();
              bitField1_ |= 0x00000200;
              break;
            } // case 336
            case 344: {
              hardDiskSize_ = input.readInt64();
              bitField1_ |= 0x00000400;
              break;
            } // case 344
            case 354: {
              appStoreVer_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00000800;
              break;
            } // case 354
            case 362: {
              apiLevel_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00001000;
              break;
            } // case 362
            case 370: {
              wifiMac_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00002000;
              break;
            } // case 370
            case 378: {
              startupTime_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00004000;
              break;
            } // case 378
            case 386: {
              bootTime_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00008000;
              break;
            } // case 386
            case 394: {
              updateTime_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00010000;
              break;
            } // case 394
            case 402: {
              birthTime_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00020000;
              break;
            } // case 402
            case 410: {
              idfaMd5_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00040000;
              break;
            } // case 410
            case 418: {
              oaidMd5_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00080000;
              break;
            } // case 418
            case 426: {
              aliAaid_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00100000;
              break;
            } // case 426
            case 434: {
              openudid_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00200000;
              break;
            } // case 434
            case 442: {
              miuiVersion_ = input.readStringRequireUtf8();
              bitField1_ |= 0x00400000;
              break;
            } // case 442
            case 453: {
              cpuFreq_ = input.readFloat();
              bitField1_ |= 0x00800000;
              break;
            } // case 453
            case 456: {
              cpuNumber_ = input.readInt32();
              bitField1_ |= 0x01000000;
              break;
            } // case 456
            case 464: {
              batteryStatus_ = input.readInt32();
              bitField1_ |= 0x02000000;
              break;
            } // case 464
            case 472: {
              batteryPower_ = input.readInt32();
              bitField1_ |= 0x04000000;
              break;
            } // case 472
            case 482: {
              con_ = input.readStringRequireUtf8();
              bitField1_ |= 0x08000000;
              break;
            } // case 482
            case 490: {
              lan_ = input.readStringRequireUtf8();
              bitField1_ |= 0x10000000;
              break;
            } // case 490
            case 498: {
              hardwareModel_ = input.readStringRequireUtf8();
              bitField1_ |= 0x20000000;
              break;
            } // case 498
            case 504: {
              authStatus_ = input.readInt32();
              bitField1_ |= 0x40000000;
              break;
            } // case 504
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;
    private int bitField1_;

    private java.lang.Object ua_ = "";
    /**
     * <code>string ua = 1;</code>
     * @return The ua.
     */
    public java.lang.String getUa() {
      java.lang.Object ref = ua_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ua_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string ua = 1;</code>
     * @return The bytes for ua.
     */
    public com.google.protobuf.ByteString
        getUaBytes() {
      java.lang.Object ref = ua_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ua_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string ua = 1;</code>
     * @param value The ua to set.
     * @return This builder for chaining.
     */
    public Builder setUa(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ua_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string ua = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUa() {
      ua_ = getDefaultInstance().getUa();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string ua = 1;</code>
     * @param value The bytes for ua to set.
     * @return This builder for chaining.
     */
    public Builder setUaBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ua_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private cn.taken.ad.logic.adv.yaya.dto.ReqGeo geo_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqGeo, cn.taken.ad.logic.adv.yaya.dto.ReqGeo.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqGeoOrBuilder> geoBuilder_;
    /**
     * <code>.ReqGeo geo = 2;</code>
     * @return Whether the geo field is set.
     */
    public boolean hasGeo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.ReqGeo geo = 2;</code>
     * @return The geo.
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqGeo getGeo() {
      if (geoBuilder_ == null) {
        return geo_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqGeo.getDefaultInstance() : geo_;
      } else {
        return geoBuilder_.getMessage();
      }
    }
    /**
     * <code>.ReqGeo geo = 2;</code>
     */
    public Builder setGeo(cn.taken.ad.logic.adv.yaya.dto.ReqGeo value) {
      if (geoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        geo_ = value;
      } else {
        geoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqGeo geo = 2;</code>
     */
    public Builder setGeo(
        cn.taken.ad.logic.adv.yaya.dto.ReqGeo.Builder builderForValue) {
      if (geoBuilder_ == null) {
        geo_ = builderForValue.build();
      } else {
        geoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqGeo geo = 2;</code>
     */
    public Builder mergeGeo(cn.taken.ad.logic.adv.yaya.dto.ReqGeo value) {
      if (geoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          geo_ != null &&
          geo_ != cn.taken.ad.logic.adv.yaya.dto.ReqGeo.getDefaultInstance()) {
          getGeoBuilder().mergeFrom(value);
        } else {
          geo_ = value;
        }
      } else {
        geoBuilder_.mergeFrom(value);
      }
      if (geo_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.ReqGeo geo = 2;</code>
     */
    public Builder clearGeo() {
      bitField0_ = (bitField0_ & ~0x00000002);
      geo_ = null;
      if (geoBuilder_ != null) {
        geoBuilder_.dispose();
        geoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.ReqGeo geo = 2;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqGeo.Builder getGeoBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return getGeoFieldBuilder().getBuilder();
    }
    /**
     * <code>.ReqGeo geo = 2;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqGeoOrBuilder getGeoOrBuilder() {
      if (geoBuilder_ != null) {
        return geoBuilder_.getMessageOrBuilder();
      } else {
        return geo_ == null ?
            cn.taken.ad.logic.adv.yaya.dto.ReqGeo.getDefaultInstance() : geo_;
      }
    }
    /**
     * <code>.ReqGeo geo = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqGeo, cn.taken.ad.logic.adv.yaya.dto.ReqGeo.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqGeoOrBuilder> 
        getGeoFieldBuilder() {
      if (geoBuilder_ == null) {
        geoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.ReqGeo, cn.taken.ad.logic.adv.yaya.dto.ReqGeo.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqGeoOrBuilder>(
                getGeo(),
                getParentForChildren(),
                isClean());
        geo_ = null;
      }
      return geoBuilder_;
    }

    private java.lang.Object ip_ = "";
    /**
     * <code>string ip = 3;</code>
     * @return The ip.
     */
    public java.lang.String getIp() {
      java.lang.Object ref = ip_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ip_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string ip = 3;</code>
     * @return The bytes for ip.
     */
    public com.google.protobuf.ByteString
        getIpBytes() {
      java.lang.Object ref = ip_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ip_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string ip = 3;</code>
     * @param value The ip to set.
     * @return This builder for chaining.
     */
    public Builder setIp(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ip_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string ip = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearIp() {
      ip_ = getDefaultInstance().getIp();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string ip = 3;</code>
     * @param value The bytes for ip to set.
     * @return This builder for chaining.
     */
    public Builder setIpBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ip_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object ipv6_ = "";
    /**
     * <code>string ipv6 = 4;</code>
     * @return The ipv6.
     */
    public java.lang.String getIpv6() {
      java.lang.Object ref = ipv6_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ipv6_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string ipv6 = 4;</code>
     * @return The bytes for ipv6.
     */
    public com.google.protobuf.ByteString
        getIpv6Bytes() {
      java.lang.Object ref = ipv6_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipv6_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string ipv6 = 4;</code>
     * @param value The ipv6 to set.
     * @return This builder for chaining.
     */
    public Builder setIpv6(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ipv6_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string ipv6 = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearIpv6() {
      ipv6_ = getDefaultInstance().getIpv6();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string ipv6 = 4;</code>
     * @param value The bytes for ipv6 to set.
     * @return This builder for chaining.
     */
    public Builder setIpv6Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ipv6_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int deviceType_ = 0;
    /**
     * <code>.MobReqDeviceType deviceType = 5;</code>
     * @return The enum numeric value on the wire for deviceType.
     */
    @java.lang.Override public int getDeviceTypeValue() {
      return deviceType_;
    }
    /**
     * <code>.MobReqDeviceType deviceType = 5;</code>
     * @param value The enum numeric value on the wire for deviceType to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceTypeValue(int value) {
      deviceType_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>.MobReqDeviceType deviceType = 5;</code>
     * @return The deviceType.
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType getDeviceType() {
      cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType result = cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType.forNumber(deviceType_);
      return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType.UNRECOGNIZED : result;
    }
    /**
     * <code>.MobReqDeviceType deviceType = 5;</code>
     * @param value The deviceType to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceType(cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000010;
      deviceType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.MobReqDeviceType deviceType = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeviceType() {
      bitField0_ = (bitField0_ & ~0x00000010);
      deviceType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object make_ = "";
    /**
     * <code>string make = 6;</code>
     * @return The make.
     */
    public java.lang.String getMake() {
      java.lang.Object ref = make_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        make_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string make = 6;</code>
     * @return The bytes for make.
     */
    public com.google.protobuf.ByteString
        getMakeBytes() {
      java.lang.Object ref = make_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        make_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string make = 6;</code>
     * @param value The make to set.
     * @return This builder for chaining.
     */
    public Builder setMake(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      make_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string make = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearMake() {
      make_ = getDefaultInstance().getMake();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string make = 6;</code>
     * @param value The bytes for make to set.
     * @return This builder for chaining.
     */
    public Builder setMakeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      make_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object mode_ = "";
    /**
     * <code>string mode = 7;</code>
     * @return The mode.
     */
    public java.lang.String getMode() {
      java.lang.Object ref = mode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string mode = 7;</code>
     * @return The bytes for mode.
     */
    public com.google.protobuf.ByteString
        getModeBytes() {
      java.lang.Object ref = mode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string mode = 7;</code>
     * @param value The mode to set.
     * @return This builder for chaining.
     */
    public Builder setMode(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      mode_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>string mode = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearMode() {
      mode_ = getDefaultInstance().getMode();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>string mode = 7;</code>
     * @param value The bytes for mode to set.
     * @return This builder for chaining.
     */
    public Builder setModeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      mode_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private int os_ = 0;
    /**
     * <code>.MobReqDeviceOsType os = 8;</code>
     * @return The enum numeric value on the wire for os.
     */
    @java.lang.Override public int getOsValue() {
      return os_;
    }
    /**
     * <code>.MobReqDeviceOsType os = 8;</code>
     * @param value The enum numeric value on the wire for os to set.
     * @return This builder for chaining.
     */
    public Builder setOsValue(int value) {
      os_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>.MobReqDeviceOsType os = 8;</code>
     * @return The os.
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType getOs() {
      cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType result = cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType.forNumber(os_);
      return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType.UNRECOGNIZED : result;
    }
    /**
     * <code>.MobReqDeviceOsType os = 8;</code>
     * @param value The os to set.
     * @return This builder for chaining.
     */
    public Builder setOs(cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceOsType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000080;
      os_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.MobReqDeviceOsType os = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearOs() {
      bitField0_ = (bitField0_ & ~0x00000080);
      os_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object osv_ = "";
    /**
     * <code>string osv = 9;</code>
     * @return The osv.
     */
    public java.lang.String getOsv() {
      java.lang.Object ref = osv_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        osv_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string osv = 9;</code>
     * @return The bytes for osv.
     */
    public com.google.protobuf.ByteString
        getOsvBytes() {
      java.lang.Object ref = osv_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        osv_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string osv = 9;</code>
     * @param value The osv to set.
     * @return This builder for chaining.
     */
    public Builder setOsv(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      osv_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>string osv = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearOsv() {
      osv_ = getDefaultInstance().getOsv();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>string osv = 9;</code>
     * @param value The bytes for osv to set.
     * @return This builder for chaining.
     */
    public Builder setOsvBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      osv_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private int carrier_ = 0;
    /**
     * <code>.MobReqDeviceCarrierType carrier = 10;</code>
     * @return The enum numeric value on the wire for carrier.
     */
    @java.lang.Override public int getCarrierValue() {
      return carrier_;
    }
    /**
     * <code>.MobReqDeviceCarrierType carrier = 10;</code>
     * @param value The enum numeric value on the wire for carrier to set.
     * @return This builder for chaining.
     */
    public Builder setCarrierValue(int value) {
      carrier_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>.MobReqDeviceCarrierType carrier = 10;</code>
     * @return The carrier.
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType getCarrier() {
      cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType result = cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType.forNumber(carrier_);
      return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType.UNRECOGNIZED : result;
    }
    /**
     * <code>.MobReqDeviceCarrierType carrier = 10;</code>
     * @param value The carrier to set.
     * @return This builder for chaining.
     */
    public Builder setCarrier(cn.taken.ad.logic.adv.yaya.dto.MobReqDeviceCarrierType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000200;
      carrier_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.MobReqDeviceCarrierType carrier = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearCarrier() {
      bitField0_ = (bitField0_ & ~0x00000200);
      carrier_ = 0;
      onChanged();
      return this;
    }

    private int connectionType_ = 0;
    /**
     * <code>.MobDeviceConnectionType connectionType = 11;</code>
     * @return The enum numeric value on the wire for connectionType.
     */
    @java.lang.Override public int getConnectionTypeValue() {
      return connectionType_;
    }
    /**
     * <code>.MobDeviceConnectionType connectionType = 11;</code>
     * @param value The enum numeric value on the wire for connectionType to set.
     * @return This builder for chaining.
     */
    public Builder setConnectionTypeValue(int value) {
      connectionType_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>.MobDeviceConnectionType connectionType = 11;</code>
     * @return The connectionType.
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType getConnectionType() {
      cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType result = cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType.forNumber(connectionType_);
      return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType.UNRECOGNIZED : result;
    }
    /**
     * <code>.MobDeviceConnectionType connectionType = 11;</code>
     * @param value The connectionType to set.
     * @return This builder for chaining.
     */
    public Builder setConnectionType(cn.taken.ad.logic.adv.yaya.dto.MobDeviceConnectionType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000400;
      connectionType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.MobDeviceConnectionType connectionType = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearConnectionType() {
      bitField0_ = (bitField0_ & ~0x00000400);
      connectionType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object ifa_ = "";
    /**
     * <code>string ifa = 12;</code>
     * @return The ifa.
     */
    public java.lang.String getIfa() {
      java.lang.Object ref = ifa_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ifa_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string ifa = 12;</code>
     * @return The bytes for ifa.
     */
    public com.google.protobuf.ByteString
        getIfaBytes() {
      java.lang.Object ref = ifa_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ifa_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string ifa = 12;</code>
     * @param value The ifa to set.
     * @return This builder for chaining.
     */
    public Builder setIfa(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ifa_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>string ifa = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearIfa() {
      ifa_ = getDefaultInstance().getIfa();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }
    /**
     * <code>string ifa = 12;</code>
     * @param value The bytes for ifa to set.
     * @return This builder for chaining.
     */
    public Builder setIfaBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ifa_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }

    private java.lang.Object imeimd5_ = "";
    /**
     * <code>string imeimd5 = 13;</code>
     * @return The imeimd5.
     */
    public java.lang.String getImeimd5() {
      java.lang.Object ref = imeimd5_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        imeimd5_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string imeimd5 = 13;</code>
     * @return The bytes for imeimd5.
     */
    public com.google.protobuf.ByteString
        getImeimd5Bytes() {
      java.lang.Object ref = imeimd5_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        imeimd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string imeimd5 = 13;</code>
     * @param value The imeimd5 to set.
     * @return This builder for chaining.
     */
    public Builder setImeimd5(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      imeimd5_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>string imeimd5 = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearImeimd5() {
      imeimd5_ = getDefaultInstance().getImeimd5();
      bitField0_ = (bitField0_ & ~0x00001000);
      onChanged();
      return this;
    }
    /**
     * <code>string imeimd5 = 13;</code>
     * @param value The bytes for imeimd5 to set.
     * @return This builder for chaining.
     */
    public Builder setImeimd5Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      imeimd5_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }

    private java.lang.Object idfv_ = "";
    /**
     * <code>string idfv = 14;</code>
     * @return The idfv.
     */
    public java.lang.String getIdfv() {
      java.lang.Object ref = idfv_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        idfv_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string idfv = 14;</code>
     * @return The bytes for idfv.
     */
    public com.google.protobuf.ByteString
        getIdfvBytes() {
      java.lang.Object ref = idfv_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        idfv_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string idfv = 14;</code>
     * @param value The idfv to set.
     * @return This builder for chaining.
     */
    public Builder setIdfv(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      idfv_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>string idfv = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearIdfv() {
      idfv_ = getDefaultInstance().getIdfv();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }
    /**
     * <code>string idfv = 14;</code>
     * @param value The bytes for idfv to set.
     * @return This builder for chaining.
     */
    public Builder setIdfvBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      idfv_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }

    private java.lang.Object adidmd5_ = "";
    /**
     * <code>string adidmd5 = 15;</code>
     * @return The adidmd5.
     */
    public java.lang.String getAdidmd5() {
      java.lang.Object ref = adidmd5_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        adidmd5_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string adidmd5 = 15;</code>
     * @return The bytes for adidmd5.
     */
    public com.google.protobuf.ByteString
        getAdidmd5Bytes() {
      java.lang.Object ref = adidmd5_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        adidmd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string adidmd5 = 15;</code>
     * @param value The adidmd5 to set.
     * @return This builder for chaining.
     */
    public Builder setAdidmd5(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      adidmd5_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>string adidmd5 = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearAdidmd5() {
      adidmd5_ = getDefaultInstance().getAdidmd5();
      bitField0_ = (bitField0_ & ~0x00004000);
      onChanged();
      return this;
    }
    /**
     * <code>string adidmd5 = 15;</code>
     * @param value The bytes for adidmd5 to set.
     * @return This builder for chaining.
     */
    public Builder setAdidmd5Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      adidmd5_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }

    private java.lang.Object mac_ = "";
    /**
     * <code>string mac = 16;</code>
     * @return The mac.
     */
    public java.lang.String getMac() {
      java.lang.Object ref = mac_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mac_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string mac = 16;</code>
     * @return The bytes for mac.
     */
    public com.google.protobuf.ByteString
        getMacBytes() {
      java.lang.Object ref = mac_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mac_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string mac = 16;</code>
     * @param value The mac to set.
     * @return This builder for chaining.
     */
    public Builder setMac(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      mac_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>string mac = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearMac() {
      mac_ = getDefaultInstance().getMac();
      bitField0_ = (bitField0_ & ~0x00008000);
      onChanged();
      return this;
    }
    /**
     * <code>string mac = 16;</code>
     * @param value The bytes for mac to set.
     * @return This builder for chaining.
     */
    public Builder setMacBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      mac_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }

    private java.lang.Object macmd5_ = "";
    /**
     * <code>string macmd5 = 17;</code>
     * @return The macmd5.
     */
    public java.lang.String getMacmd5() {
      java.lang.Object ref = macmd5_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        macmd5_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string macmd5 = 17;</code>
     * @return The bytes for macmd5.
     */
    public com.google.protobuf.ByteString
        getMacmd5Bytes() {
      java.lang.Object ref = macmd5_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        macmd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string macmd5 = 17;</code>
     * @param value The macmd5 to set.
     * @return This builder for chaining.
     */
    public Builder setMacmd5(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      macmd5_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>string macmd5 = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearMacmd5() {
      macmd5_ = getDefaultInstance().getMacmd5();
      bitField0_ = (bitField0_ & ~0x00010000);
      onChanged();
      return this;
    }
    /**
     * <code>string macmd5 = 17;</code>
     * @param value The bytes for macmd5 to set.
     * @return This builder for chaining.
     */
    public Builder setMacmd5Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      macmd5_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }

    private java.lang.Object adid_ = "";
    /**
     * <code>string adid = 18;</code>
     * @return The adid.
     */
    public java.lang.String getAdid() {
      java.lang.Object ref = adid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        adid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string adid = 18;</code>
     * @return The bytes for adid.
     */
    public com.google.protobuf.ByteString
        getAdidBytes() {
      java.lang.Object ref = adid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        adid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string adid = 18;</code>
     * @param value The adid to set.
     * @return This builder for chaining.
     */
    public Builder setAdid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      adid_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>string adid = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearAdid() {
      adid_ = getDefaultInstance().getAdid();
      bitField0_ = (bitField0_ & ~0x00020000);
      onChanged();
      return this;
    }
    /**
     * <code>string adid = 18;</code>
     * @param value The bytes for adid to set.
     * @return This builder for chaining.
     */
    public Builder setAdidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      adid_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }

    private int w_ ;
    /**
     * <code>int32 w = 19;</code>
     * @return The w.
     */
    @java.lang.Override
    public int getW() {
      return w_;
    }
    /**
     * <code>int32 w = 19;</code>
     * @param value The w to set.
     * @return This builder for chaining.
     */
    public Builder setW(int value) {

      w_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>int32 w = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearW() {
      bitField0_ = (bitField0_ & ~0x00040000);
      w_ = 0;
      onChanged();
      return this;
    }

    private int h_ ;
    /**
     * <code>int32 h = 20;</code>
     * @return The h.
     */
    @java.lang.Override
    public int getH() {
      return h_;
    }
    /**
     * <code>int32 h = 20;</code>
     * @param value The h to set.
     * @return This builder for chaining.
     */
    public Builder setH(int value) {

      h_ = value;
      bitField0_ |= 0x00080000;
      onChanged();
      return this;
    }
    /**
     * <code>int32 h = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearH() {
      bitField0_ = (bitField0_ & ~0x00080000);
      h_ = 0;
      onChanged();
      return this;
    }

    private int asw_ ;
    /**
     * <code>int32 asw = 21;</code>
     * @return The asw.
     */
    @java.lang.Override
    public int getAsw() {
      return asw_;
    }
    /**
     * <code>int32 asw = 21;</code>
     * @param value The asw to set.
     * @return This builder for chaining.
     */
    public Builder setAsw(int value) {

      asw_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <code>int32 asw = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearAsw() {
      bitField0_ = (bitField0_ & ~0x00100000);
      asw_ = 0;
      onChanged();
      return this;
    }

    private int ash_ ;
    /**
     * <code>int32 ash = 22;</code>
     * @return The ash.
     */
    @java.lang.Override
    public int getAsh() {
      return ash_;
    }
    /**
     * <code>int32 ash = 22;</code>
     * @param value The ash to set.
     * @return This builder for chaining.
     */
    public Builder setAsh(int value) {

      ash_ = value;
      bitField0_ |= 0x00200000;
      onChanged();
      return this;
    }
    /**
     * <code>int32 ash = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearAsh() {
      bitField0_ = (bitField0_ & ~0x00200000);
      ash_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object oaid_ = "";
    /**
     * <code>string oaid = 23;</code>
     * @return The oaid.
     */
    public java.lang.String getOaid() {
      java.lang.Object ref = oaid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        oaid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string oaid = 23;</code>
     * @return The bytes for oaid.
     */
    public com.google.protobuf.ByteString
        getOaidBytes() {
      java.lang.Object ref = oaid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        oaid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string oaid = 23;</code>
     * @param value The oaid to set.
     * @return This builder for chaining.
     */
    public Builder setOaid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      oaid_ = value;
      bitField0_ |= 0x00400000;
      onChanged();
      return this;
    }
    /**
     * <code>string oaid = 23;</code>
     * @return This builder for chaining.
     */
    public Builder clearOaid() {
      oaid_ = getDefaultInstance().getOaid();
      bitField0_ = (bitField0_ & ~0x00400000);
      onChanged();
      return this;
    }
    /**
     * <code>string oaid = 23;</code>
     * @param value The bytes for oaid to set.
     * @return This builder for chaining.
     */
    public Builder setOaidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      oaid_ = value;
      bitField0_ |= 0x00400000;
      onChanged();
      return this;
    }

    private java.lang.Object brand_ = "";
    /**
     * <code>string brand = 24;</code>
     * @return The brand.
     */
    public java.lang.String getBrand() {
      java.lang.Object ref = brand_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        brand_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string brand = 24;</code>
     * @return The bytes for brand.
     */
    public com.google.protobuf.ByteString
        getBrandBytes() {
      java.lang.Object ref = brand_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        brand_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string brand = 24;</code>
     * @param value The brand to set.
     * @return This builder for chaining.
     */
    public Builder setBrand(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      brand_ = value;
      bitField0_ |= 0x00800000;
      onChanged();
      return this;
    }
    /**
     * <code>string brand = 24;</code>
     * @return This builder for chaining.
     */
    public Builder clearBrand() {
      brand_ = getDefaultInstance().getBrand();
      bitField0_ = (bitField0_ & ~0x00800000);
      onChanged();
      return this;
    }
    /**
     * <code>string brand = 24;</code>
     * @param value The bytes for brand to set.
     * @return This builder for chaining.
     */
    public Builder setBrandBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      brand_ = value;
      bitField0_ |= 0x00800000;
      onChanged();
      return this;
    }

    private float dpi_ ;
    /**
     * <code>float dpi = 25;</code>
     * @return The dpi.
     */
    @java.lang.Override
    public float getDpi() {
      return dpi_;
    }
    /**
     * <code>float dpi = 25;</code>
     * @param value The dpi to set.
     * @return This builder for chaining.
     */
    public Builder setDpi(float value) {

      dpi_ = value;
      bitField0_ |= 0x01000000;
      onChanged();
      return this;
    }
    /**
     * <code>float dpi = 25;</code>
     * @return This builder for chaining.
     */
    public Builder clearDpi() {
      bitField0_ = (bitField0_ & ~0x01000000);
      dpi_ = 0F;
      onChanged();
      return this;
    }

    private int ppi_ ;
    /**
     * <code>int32 ppi = 26;</code>
     * @return The ppi.
     */
    @java.lang.Override
    public int getPpi() {
      return ppi_;
    }
    /**
     * <code>int32 ppi = 26;</code>
     * @param value The ppi to set.
     * @return This builder for chaining.
     */
    public Builder setPpi(int value) {

      ppi_ = value;
      bitField0_ |= 0x02000000;
      onChanged();
      return this;
    }
    /**
     * <code>int32 ppi = 26;</code>
     * @return This builder for chaining.
     */
    public Builder clearPpi() {
      bitField0_ = (bitField0_ & ~0x02000000);
      ppi_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object caid_ = "";
    /**
     * <code>string caid = 27;</code>
     * @return The caid.
     */
    public java.lang.String getCaid() {
      java.lang.Object ref = caid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        caid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string caid = 27;</code>
     * @return The bytes for caid.
     */
    public com.google.protobuf.ByteString
        getCaidBytes() {
      java.lang.Object ref = caid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        caid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string caid = 27;</code>
     * @param value The caid to set.
     * @return This builder for chaining.
     */
    public Builder setCaid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      caid_ = value;
      bitField0_ |= 0x04000000;
      onChanged();
      return this;
    }
    /**
     * <code>string caid = 27;</code>
     * @return This builder for chaining.
     */
    public Builder clearCaid() {
      caid_ = getDefaultInstance().getCaid();
      bitField0_ = (bitField0_ & ~0x04000000);
      onChanged();
      return this;
    }
    /**
     * <code>string caid = 27;</code>
     * @param value The bytes for caid to set.
     * @return This builder for chaining.
     */
    public Builder setCaidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      caid_ = value;
      bitField0_ |= 0x04000000;
      onChanged();
      return this;
    }

    private java.lang.Object caidVersion_ = "";
    /**
     * <code>string caidVersion = 28;</code>
     * @return The caidVersion.
     */
    public java.lang.String getCaidVersion() {
      java.lang.Object ref = caidVersion_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        caidVersion_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string caidVersion = 28;</code>
     * @return The bytes for caidVersion.
     */
    public com.google.protobuf.ByteString
        getCaidVersionBytes() {
      java.lang.Object ref = caidVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        caidVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string caidVersion = 28;</code>
     * @param value The caidVersion to set.
     * @return This builder for chaining.
     */
    public Builder setCaidVersion(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      caidVersion_ = value;
      bitField0_ |= 0x08000000;
      onChanged();
      return this;
    }
    /**
     * <code>string caidVersion = 28;</code>
     * @return This builder for chaining.
     */
    public Builder clearCaidVersion() {
      caidVersion_ = getDefaultInstance().getCaidVersion();
      bitField0_ = (bitField0_ & ~0x08000000);
      onChanged();
      return this;
    }
    /**
     * <code>string caidVersion = 28;</code>
     * @param value The bytes for caidVersion to set.
     * @return This builder for chaining.
     */
    public Builder setCaidVersionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      caidVersion_ = value;
      bitField0_ |= 0x08000000;
      onChanged();
      return this;
    }

    private java.lang.Object bootMark_ = "";
    /**
     * <code>string bootMark = 29;</code>
     * @return The bootMark.
     */
    public java.lang.String getBootMark() {
      java.lang.Object ref = bootMark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bootMark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string bootMark = 29;</code>
     * @return The bytes for bootMark.
     */
    public com.google.protobuf.ByteString
        getBootMarkBytes() {
      java.lang.Object ref = bootMark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bootMark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string bootMark = 29;</code>
     * @param value The bootMark to set.
     * @return This builder for chaining.
     */
    public Builder setBootMark(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      bootMark_ = value;
      bitField0_ |= 0x10000000;
      onChanged();
      return this;
    }
    /**
     * <code>string bootMark = 29;</code>
     * @return This builder for chaining.
     */
    public Builder clearBootMark() {
      bootMark_ = getDefaultInstance().getBootMark();
      bitField0_ = (bitField0_ & ~0x10000000);
      onChanged();
      return this;
    }
    /**
     * <code>string bootMark = 29;</code>
     * @param value The bytes for bootMark to set.
     * @return This builder for chaining.
     */
    public Builder setBootMarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      bootMark_ = value;
      bitField0_ |= 0x10000000;
      onChanged();
      return this;
    }

    private java.lang.Object updateMark_ = "";
    /**
     * <code>string updateMark = 30;</code>
     * @return The updateMark.
     */
    public java.lang.String getUpdateMark() {
      java.lang.Object ref = updateMark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        updateMark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string updateMark = 30;</code>
     * @return The bytes for updateMark.
     */
    public com.google.protobuf.ByteString
        getUpdateMarkBytes() {
      java.lang.Object ref = updateMark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        updateMark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string updateMark = 30;</code>
     * @param value The updateMark to set.
     * @return This builder for chaining.
     */
    public Builder setUpdateMark(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      updateMark_ = value;
      bitField0_ |= 0x20000000;
      onChanged();
      return this;
    }
    /**
     * <code>string updateMark = 30;</code>
     * @return This builder for chaining.
     */
    public Builder clearUpdateMark() {
      updateMark_ = getDefaultInstance().getUpdateMark();
      bitField0_ = (bitField0_ & ~0x20000000);
      onChanged();
      return this;
    }
    /**
     * <code>string updateMark = 30;</code>
     * @param value The bytes for updateMark to set.
     * @return This builder for chaining.
     */
    public Builder setUpdateMarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      updateMark_ = value;
      bitField0_ |= 0x20000000;
      onChanged();
      return this;
    }

    private java.lang.Object romVersion_ = "";
    /**
     * <code>string romVersion = 31;</code>
     * @return The romVersion.
     */
    public java.lang.String getRomVersion() {
      java.lang.Object ref = romVersion_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        romVersion_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string romVersion = 31;</code>
     * @return The bytes for romVersion.
     */
    public com.google.protobuf.ByteString
        getRomVersionBytes() {
      java.lang.Object ref = romVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        romVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string romVersion = 31;</code>
     * @param value The romVersion to set.
     * @return This builder for chaining.
     */
    public Builder setRomVersion(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      romVersion_ = value;
      bitField0_ |= 0x40000000;
      onChanged();
      return this;
    }
    /**
     * <code>string romVersion = 31;</code>
     * @return This builder for chaining.
     */
    public Builder clearRomVersion() {
      romVersion_ = getDefaultInstance().getRomVersion();
      bitField0_ = (bitField0_ & ~0x40000000);
      onChanged();
      return this;
    }
    /**
     * <code>string romVersion = 31;</code>
     * @param value The bytes for romVersion to set.
     * @return This builder for chaining.
     */
    public Builder setRomVersionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      romVersion_ = value;
      bitField0_ |= 0x40000000;
      onChanged();
      return this;
    }

    private long sysCompileTs_ ;
    /**
     * <code>int64 sysCompileTs = 32;</code>
     * @return The sysCompileTs.
     */
    @java.lang.Override
    public long getSysCompileTs() {
      return sysCompileTs_;
    }
    /**
     * <code>int64 sysCompileTs = 32;</code>
     * @param value The sysCompileTs to set.
     * @return This builder for chaining.
     */
    public Builder setSysCompileTs(long value) {

      sysCompileTs_ = value;
      bitField0_ |= 0x80000000;
      onChanged();
      return this;
    }
    /**
     * <code>int64 sysCompileTs = 32;</code>
     * @return This builder for chaining.
     */
    public Builder clearSysCompileTs() {
      bitField0_ = (bitField0_ & ~0x80000000);
      sysCompileTs_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object hmscore_ = "";
    /**
     * <code>string hmscore = 33;</code>
     * @return The hmscore.
     */
    public java.lang.String getHmscore() {
      java.lang.Object ref = hmscore_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hmscore_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string hmscore = 33;</code>
     * @return The bytes for hmscore.
     */
    public com.google.protobuf.ByteString
        getHmscoreBytes() {
      java.lang.Object ref = hmscore_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hmscore_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string hmscore = 33;</code>
     * @param value The hmscore to set.
     * @return This builder for chaining.
     */
    public Builder setHmscore(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      hmscore_ = value;
      bitField1_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string hmscore = 33;</code>
     * @return This builder for chaining.
     */
    public Builder clearHmscore() {
      hmscore_ = getDefaultInstance().getHmscore();
      bitField1_ = (bitField1_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string hmscore = 33;</code>
     * @param value The bytes for hmscore to set.
     * @return This builder for chaining.
     */
    public Builder setHmscoreBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      hmscore_ = value;
      bitField1_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object paid_ = "";
    /**
     * <code>string paid = 34;</code>
     * @return The paid.
     */
    public java.lang.String getPaid() {
      java.lang.Object ref = paid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        paid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string paid = 34;</code>
     * @return The bytes for paid.
     */
    public com.google.protobuf.ByteString
        getPaidBytes() {
      java.lang.Object ref = paid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        paid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string paid = 34;</code>
     * @param value The paid to set.
     * @return This builder for chaining.
     */
    public Builder setPaid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      paid_ = value;
      bitField1_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string paid = 34;</code>
     * @return This builder for chaining.
     */
    public Builder clearPaid() {
      paid_ = getDefaultInstance().getPaid();
      bitField1_ = (bitField1_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string paid = 34;</code>
     * @param value The bytes for paid to set.
     * @return This builder for chaining.
     */
    public Builder setPaidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      paid_ = value;
      bitField1_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object bssid_ = "";
    /**
     * <code>string bssid = 35;</code>
     * @return The bssid.
     */
    public java.lang.String getBssid() {
      java.lang.Object ref = bssid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bssid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string bssid = 35;</code>
     * @return The bytes for bssid.
     */
    public com.google.protobuf.ByteString
        getBssidBytes() {
      java.lang.Object ref = bssid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bssid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string bssid = 35;</code>
     * @param value The bssid to set.
     * @return This builder for chaining.
     */
    public Builder setBssid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      bssid_ = value;
      bitField1_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string bssid = 35;</code>
     * @return This builder for chaining.
     */
    public Builder clearBssid() {
      bssid_ = getDefaultInstance().getBssid();
      bitField1_ = (bitField1_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string bssid = 35;</code>
     * @param value The bytes for bssid to set.
     * @return This builder for chaining.
     */
    public Builder setBssidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      bssid_ = value;
      bitField1_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int screenType_ ;
    /**
     * <code>int32 screenType = 36;</code>
     * @return The screenType.
     */
    @java.lang.Override
    public int getScreenType() {
      return screenType_;
    }
    /**
     * <code>int32 screenType = 36;</code>
     * @param value The screenType to set.
     * @return This builder for chaining.
     */
    public Builder setScreenType(int value) {

      screenType_ = value;
      bitField1_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>int32 screenType = 36;</code>
     * @return This builder for chaining.
     */
    public Builder clearScreenType() {
      bitField1_ = (bitField1_ & ~0x00000008);
      screenType_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList checkedApps_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureCheckedAppsIsMutable() {
      if (!checkedApps_.isModifiable()) {
        checkedApps_ = new com.google.protobuf.LazyStringArrayList(checkedApps_);
      }
      bitField1_ |= 0x00000010;
    }
    /**
     * <code>repeated string checkedApps = 37;</code>
     * @return A list containing the checkedApps.
     */
    public com.google.protobuf.ProtocolStringList
        getCheckedAppsList() {
      checkedApps_.makeImmutable();
      return checkedApps_;
    }
    /**
     * <code>repeated string checkedApps = 37;</code>
     * @return The count of checkedApps.
     */
    public int getCheckedAppsCount() {
      return checkedApps_.size();
    }
    /**
     * <code>repeated string checkedApps = 37;</code>
     * @param index The index of the element to return.
     * @return The checkedApps at the given index.
     */
    public java.lang.String getCheckedApps(int index) {
      return checkedApps_.get(index);
    }
    /**
     * <code>repeated string checkedApps = 37;</code>
     * @param index The index of the value to return.
     * @return The bytes of the checkedApps at the given index.
     */
    public com.google.protobuf.ByteString
        getCheckedAppsBytes(int index) {
      return checkedApps_.getByteString(index);
    }
    /**
     * <code>repeated string checkedApps = 37;</code>
     * @param index The index to set the value at.
     * @param value The checkedApps to set.
     * @return This builder for chaining.
     */
    public Builder setCheckedApps(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureCheckedAppsIsMutable();
      checkedApps_.set(index, value);
      bitField1_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string checkedApps = 37;</code>
     * @param value The checkedApps to add.
     * @return This builder for chaining.
     */
    public Builder addCheckedApps(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureCheckedAppsIsMutable();
      checkedApps_.add(value);
      bitField1_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string checkedApps = 37;</code>
     * @param values The checkedApps to add.
     * @return This builder for chaining.
     */
    public Builder addAllCheckedApps(
        java.lang.Iterable<java.lang.String> values) {
      ensureCheckedAppsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, checkedApps_);
      bitField1_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string checkedApps = 37;</code>
     * @return This builder for chaining.
     */
    public Builder clearCheckedApps() {
      checkedApps_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField1_ = (bitField1_ & ~0x00000010);;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string checkedApps = 37;</code>
     * @param value The bytes of the checkedApps to add.
     * @return This builder for chaining.
     */
    public Builder addCheckedAppsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensureCheckedAppsIsMutable();
      checkedApps_.add(value);
      bitField1_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object hmsVer_ = "";
    /**
     * <code>string hmsVer = 38;</code>
     * @return The hmsVer.
     */
    public java.lang.String getHmsVer() {
      java.lang.Object ref = hmsVer_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hmsVer_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string hmsVer = 38;</code>
     * @return The bytes for hmsVer.
     */
    public com.google.protobuf.ByteString
        getHmsVerBytes() {
      java.lang.Object ref = hmsVer_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hmsVer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string hmsVer = 38;</code>
     * @param value The hmsVer to set.
     * @return This builder for chaining.
     */
    public Builder setHmsVer(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      hmsVer_ = value;
      bitField1_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string hmsVer = 38;</code>
     * @return This builder for chaining.
     */
    public Builder clearHmsVer() {
      hmsVer_ = getDefaultInstance().getHmsVer();
      bitField1_ = (bitField1_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string hmsVer = 38;</code>
     * @param value The bytes for hmsVer to set.
     * @return This builder for chaining.
     */
    public Builder setHmsVerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      hmsVer_ = value;
      bitField1_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object hwagVer_ = "";
    /**
     * <code>string hwagVer = 39;</code>
     * @return The hwagVer.
     */
    public java.lang.String getHwagVer() {
      java.lang.Object ref = hwagVer_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hwagVer_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string hwagVer = 39;</code>
     * @return The bytes for hwagVer.
     */
    public com.google.protobuf.ByteString
        getHwagVerBytes() {
      java.lang.Object ref = hwagVer_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hwagVer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string hwagVer = 39;</code>
     * @param value The hwagVer to set.
     * @return This builder for chaining.
     */
    public Builder setHwagVer(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      hwagVer_ = value;
      bitField1_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>string hwagVer = 39;</code>
     * @return This builder for chaining.
     */
    public Builder clearHwagVer() {
      hwagVer_ = getDefaultInstance().getHwagVer();
      bitField1_ = (bitField1_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>string hwagVer = 39;</code>
     * @param value The bytes for hwagVer to set.
     * @return This builder for chaining.
     */
    public Builder setHwagVerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      hwagVer_ = value;
      bitField1_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object deviceName_ = "";
    /**
     * <code>string deviceName = 40;</code>
     * @return The deviceName.
     */
    public java.lang.String getDeviceName() {
      java.lang.Object ref = deviceName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string deviceName = 40;</code>
     * @return The bytes for deviceName.
     */
    public com.google.protobuf.ByteString
        getDeviceNameBytes() {
      java.lang.Object ref = deviceName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string deviceName = 40;</code>
     * @param value The deviceName to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      deviceName_ = value;
      bitField1_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>string deviceName = 40;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeviceName() {
      deviceName_ = getDefaultInstance().getDeviceName();
      bitField1_ = (bitField1_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>string deviceName = 40;</code>
     * @param value The bytes for deviceName to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      deviceName_ = value;
      bitField1_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object timeZone_ = "";
    /**
     * <code>string timeZone = 41;</code>
     * @return The timeZone.
     */
    public java.lang.String getTimeZone() {
      java.lang.Object ref = timeZone_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        timeZone_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string timeZone = 41;</code>
     * @return The bytes for timeZone.
     */
    public com.google.protobuf.ByteString
        getTimeZoneBytes() {
      java.lang.Object ref = timeZone_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        timeZone_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string timeZone = 41;</code>
     * @param value The timeZone to set.
     * @return This builder for chaining.
     */
    public Builder setTimeZone(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      timeZone_ = value;
      bitField1_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>string timeZone = 41;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimeZone() {
      timeZone_ = getDefaultInstance().getTimeZone();
      bitField1_ = (bitField1_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>string timeZone = 41;</code>
     * @param value The bytes for timeZone to set.
     * @return This builder for chaining.
     */
    public Builder setTimeZoneBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      timeZone_ = value;
      bitField1_ |= 0x00000100;
      onChanged();
      return this;
    }

    private long memorySize_ ;
    /**
     * <code>int64 memorySize = 42;</code>
     * @return The memorySize.
     */
    @java.lang.Override
    public long getMemorySize() {
      return memorySize_;
    }
    /**
     * <code>int64 memorySize = 42;</code>
     * @param value The memorySize to set.
     * @return This builder for chaining.
     */
    public Builder setMemorySize(long value) {

      memorySize_ = value;
      bitField1_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>int64 memorySize = 42;</code>
     * @return This builder for chaining.
     */
    public Builder clearMemorySize() {
      bitField1_ = (bitField1_ & ~0x00000200);
      memorySize_ = 0L;
      onChanged();
      return this;
    }

    private long hardDiskSize_ ;
    /**
     * <code>int64 hardDiskSize = 43;</code>
     * @return The hardDiskSize.
     */
    @java.lang.Override
    public long getHardDiskSize() {
      return hardDiskSize_;
    }
    /**
     * <code>int64 hardDiskSize = 43;</code>
     * @param value The hardDiskSize to set.
     * @return This builder for chaining.
     */
    public Builder setHardDiskSize(long value) {

      hardDiskSize_ = value;
      bitField1_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>int64 hardDiskSize = 43;</code>
     * @return This builder for chaining.
     */
    public Builder clearHardDiskSize() {
      bitField1_ = (bitField1_ & ~0x00000400);
      hardDiskSize_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object appStoreVer_ = "";
    /**
     * <code>string appStoreVer = 44;</code>
     * @return The appStoreVer.
     */
    public java.lang.String getAppStoreVer() {
      java.lang.Object ref = appStoreVer_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appStoreVer_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string appStoreVer = 44;</code>
     * @return The bytes for appStoreVer.
     */
    public com.google.protobuf.ByteString
        getAppStoreVerBytes() {
      java.lang.Object ref = appStoreVer_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appStoreVer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string appStoreVer = 44;</code>
     * @param value The appStoreVer to set.
     * @return This builder for chaining.
     */
    public Builder setAppStoreVer(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      appStoreVer_ = value;
      bitField1_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>string appStoreVer = 44;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppStoreVer() {
      appStoreVer_ = getDefaultInstance().getAppStoreVer();
      bitField1_ = (bitField1_ & ~0x00000800);
      onChanged();
      return this;
    }
    /**
     * <code>string appStoreVer = 44;</code>
     * @param value The bytes for appStoreVer to set.
     * @return This builder for chaining.
     */
    public Builder setAppStoreVerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      appStoreVer_ = value;
      bitField1_ |= 0x00000800;
      onChanged();
      return this;
    }

    private java.lang.Object apiLevel_ = "";
    /**
     * <code>string apiLevel = 45;</code>
     * @return The apiLevel.
     */
    public java.lang.String getApiLevel() {
      java.lang.Object ref = apiLevel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        apiLevel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string apiLevel = 45;</code>
     * @return The bytes for apiLevel.
     */
    public com.google.protobuf.ByteString
        getApiLevelBytes() {
      java.lang.Object ref = apiLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        apiLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string apiLevel = 45;</code>
     * @param value The apiLevel to set.
     * @return This builder for chaining.
     */
    public Builder setApiLevel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      apiLevel_ = value;
      bitField1_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>string apiLevel = 45;</code>
     * @return This builder for chaining.
     */
    public Builder clearApiLevel() {
      apiLevel_ = getDefaultInstance().getApiLevel();
      bitField1_ = (bitField1_ & ~0x00001000);
      onChanged();
      return this;
    }
    /**
     * <code>string apiLevel = 45;</code>
     * @param value The bytes for apiLevel to set.
     * @return This builder for chaining.
     */
    public Builder setApiLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      apiLevel_ = value;
      bitField1_ |= 0x00001000;
      onChanged();
      return this;
    }

    private java.lang.Object wifiMac_ = "";
    /**
     * <code>string wifiMac = 46;</code>
     * @return The wifiMac.
     */
    public java.lang.String getWifiMac() {
      java.lang.Object ref = wifiMac_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        wifiMac_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string wifiMac = 46;</code>
     * @return The bytes for wifiMac.
     */
    public com.google.protobuf.ByteString
        getWifiMacBytes() {
      java.lang.Object ref = wifiMac_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        wifiMac_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string wifiMac = 46;</code>
     * @param value The wifiMac to set.
     * @return This builder for chaining.
     */
    public Builder setWifiMac(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      wifiMac_ = value;
      bitField1_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>string wifiMac = 46;</code>
     * @return This builder for chaining.
     */
    public Builder clearWifiMac() {
      wifiMac_ = getDefaultInstance().getWifiMac();
      bitField1_ = (bitField1_ & ~0x00002000);
      onChanged();
      return this;
    }
    /**
     * <code>string wifiMac = 46;</code>
     * @param value The bytes for wifiMac to set.
     * @return This builder for chaining.
     */
    public Builder setWifiMacBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      wifiMac_ = value;
      bitField1_ |= 0x00002000;
      onChanged();
      return this;
    }

    private java.lang.Object startupTime_ = "";
    /**
     * <code>string startupTime = 47;</code>
     * @return The startupTime.
     */
    public java.lang.String getStartupTime() {
      java.lang.Object ref = startupTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        startupTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string startupTime = 47;</code>
     * @return The bytes for startupTime.
     */
    public com.google.protobuf.ByteString
        getStartupTimeBytes() {
      java.lang.Object ref = startupTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        startupTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string startupTime = 47;</code>
     * @param value The startupTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartupTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      startupTime_ = value;
      bitField1_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>string startupTime = 47;</code>
     * @return This builder for chaining.
     */
    public Builder clearStartupTime() {
      startupTime_ = getDefaultInstance().getStartupTime();
      bitField1_ = (bitField1_ & ~0x00004000);
      onChanged();
      return this;
    }
    /**
     * <code>string startupTime = 47;</code>
     * @param value The bytes for startupTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartupTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      startupTime_ = value;
      bitField1_ |= 0x00004000;
      onChanged();
      return this;
    }

    private java.lang.Object bootTime_ = "";
    /**
     * <code>string bootTime = 48;</code>
     * @return The bootTime.
     */
    public java.lang.String getBootTime() {
      java.lang.Object ref = bootTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bootTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string bootTime = 48;</code>
     * @return The bytes for bootTime.
     */
    public com.google.protobuf.ByteString
        getBootTimeBytes() {
      java.lang.Object ref = bootTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bootTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string bootTime = 48;</code>
     * @param value The bootTime to set.
     * @return This builder for chaining.
     */
    public Builder setBootTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      bootTime_ = value;
      bitField1_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>string bootTime = 48;</code>
     * @return This builder for chaining.
     */
    public Builder clearBootTime() {
      bootTime_ = getDefaultInstance().getBootTime();
      bitField1_ = (bitField1_ & ~0x00008000);
      onChanged();
      return this;
    }
    /**
     * <code>string bootTime = 48;</code>
     * @param value The bytes for bootTime to set.
     * @return This builder for chaining.
     */
    public Builder setBootTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      bootTime_ = value;
      bitField1_ |= 0x00008000;
      onChanged();
      return this;
    }

    private java.lang.Object updateTime_ = "";
    /**
     * <code>string updateTime = 49;</code>
     * @return The updateTime.
     */
    public java.lang.String getUpdateTime() {
      java.lang.Object ref = updateTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        updateTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string updateTime = 49;</code>
     * @return The bytes for updateTime.
     */
    public com.google.protobuf.ByteString
        getUpdateTimeBytes() {
      java.lang.Object ref = updateTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        updateTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string updateTime = 49;</code>
     * @param value The updateTime to set.
     * @return This builder for chaining.
     */
    public Builder setUpdateTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      updateTime_ = value;
      bitField1_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>string updateTime = 49;</code>
     * @return This builder for chaining.
     */
    public Builder clearUpdateTime() {
      updateTime_ = getDefaultInstance().getUpdateTime();
      bitField1_ = (bitField1_ & ~0x00010000);
      onChanged();
      return this;
    }
    /**
     * <code>string updateTime = 49;</code>
     * @param value The bytes for updateTime to set.
     * @return This builder for chaining.
     */
    public Builder setUpdateTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      updateTime_ = value;
      bitField1_ |= 0x00010000;
      onChanged();
      return this;
    }

    private java.lang.Object birthTime_ = "";
    /**
     * <code>string birthTime = 50;</code>
     * @return The birthTime.
     */
    public java.lang.String getBirthTime() {
      java.lang.Object ref = birthTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        birthTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string birthTime = 50;</code>
     * @return The bytes for birthTime.
     */
    public com.google.protobuf.ByteString
        getBirthTimeBytes() {
      java.lang.Object ref = birthTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        birthTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string birthTime = 50;</code>
     * @param value The birthTime to set.
     * @return This builder for chaining.
     */
    public Builder setBirthTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      birthTime_ = value;
      bitField1_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>string birthTime = 50;</code>
     * @return This builder for chaining.
     */
    public Builder clearBirthTime() {
      birthTime_ = getDefaultInstance().getBirthTime();
      bitField1_ = (bitField1_ & ~0x00020000);
      onChanged();
      return this;
    }
    /**
     * <code>string birthTime = 50;</code>
     * @param value The bytes for birthTime to set.
     * @return This builder for chaining.
     */
    public Builder setBirthTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      birthTime_ = value;
      bitField1_ |= 0x00020000;
      onChanged();
      return this;
    }

    private java.lang.Object idfaMd5_ = "";
    /**
     * <code>string idfaMd5 = 51;</code>
     * @return The idfaMd5.
     */
    public java.lang.String getIdfaMd5() {
      java.lang.Object ref = idfaMd5_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        idfaMd5_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string idfaMd5 = 51;</code>
     * @return The bytes for idfaMd5.
     */
    public com.google.protobuf.ByteString
        getIdfaMd5Bytes() {
      java.lang.Object ref = idfaMd5_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        idfaMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string idfaMd5 = 51;</code>
     * @param value The idfaMd5 to set.
     * @return This builder for chaining.
     */
    public Builder setIdfaMd5(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      idfaMd5_ = value;
      bitField1_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>string idfaMd5 = 51;</code>
     * @return This builder for chaining.
     */
    public Builder clearIdfaMd5() {
      idfaMd5_ = getDefaultInstance().getIdfaMd5();
      bitField1_ = (bitField1_ & ~0x00040000);
      onChanged();
      return this;
    }
    /**
     * <code>string idfaMd5 = 51;</code>
     * @param value The bytes for idfaMd5 to set.
     * @return This builder for chaining.
     */
    public Builder setIdfaMd5Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      idfaMd5_ = value;
      bitField1_ |= 0x00040000;
      onChanged();
      return this;
    }

    private java.lang.Object oaidMd5_ = "";
    /**
     * <code>string oaidMd5 = 52;</code>
     * @return The oaidMd5.
     */
    public java.lang.String getOaidMd5() {
      java.lang.Object ref = oaidMd5_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        oaidMd5_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string oaidMd5 = 52;</code>
     * @return The bytes for oaidMd5.
     */
    public com.google.protobuf.ByteString
        getOaidMd5Bytes() {
      java.lang.Object ref = oaidMd5_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        oaidMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string oaidMd5 = 52;</code>
     * @param value The oaidMd5 to set.
     * @return This builder for chaining.
     */
    public Builder setOaidMd5(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      oaidMd5_ = value;
      bitField1_ |= 0x00080000;
      onChanged();
      return this;
    }
    /**
     * <code>string oaidMd5 = 52;</code>
     * @return This builder for chaining.
     */
    public Builder clearOaidMd5() {
      oaidMd5_ = getDefaultInstance().getOaidMd5();
      bitField1_ = (bitField1_ & ~0x00080000);
      onChanged();
      return this;
    }
    /**
     * <code>string oaidMd5 = 52;</code>
     * @param value The bytes for oaidMd5 to set.
     * @return This builder for chaining.
     */
    public Builder setOaidMd5Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      oaidMd5_ = value;
      bitField1_ |= 0x00080000;
      onChanged();
      return this;
    }

    private java.lang.Object aliAaid_ = "";
    /**
     * <code>string aliAaid = 53;</code>
     * @return The aliAaid.
     */
    public java.lang.String getAliAaid() {
      java.lang.Object ref = aliAaid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        aliAaid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string aliAaid = 53;</code>
     * @return The bytes for aliAaid.
     */
    public com.google.protobuf.ByteString
        getAliAaidBytes() {
      java.lang.Object ref = aliAaid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        aliAaid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string aliAaid = 53;</code>
     * @param value The aliAaid to set.
     * @return This builder for chaining.
     */
    public Builder setAliAaid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      aliAaid_ = value;
      bitField1_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <code>string aliAaid = 53;</code>
     * @return This builder for chaining.
     */
    public Builder clearAliAaid() {
      aliAaid_ = getDefaultInstance().getAliAaid();
      bitField1_ = (bitField1_ & ~0x00100000);
      onChanged();
      return this;
    }
    /**
     * <code>string aliAaid = 53;</code>
     * @param value The bytes for aliAaid to set.
     * @return This builder for chaining.
     */
    public Builder setAliAaidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      aliAaid_ = value;
      bitField1_ |= 0x00100000;
      onChanged();
      return this;
    }

    private java.lang.Object openudid_ = "";
    /**
     * <code>string openudid = 54;</code>
     * @return The openudid.
     */
    public java.lang.String getOpenudid() {
      java.lang.Object ref = openudid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        openudid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string openudid = 54;</code>
     * @return The bytes for openudid.
     */
    public com.google.protobuf.ByteString
        getOpenudidBytes() {
      java.lang.Object ref = openudid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openudid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string openudid = 54;</code>
     * @param value The openudid to set.
     * @return This builder for chaining.
     */
    public Builder setOpenudid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      openudid_ = value;
      bitField1_ |= 0x00200000;
      onChanged();
      return this;
    }
    /**
     * <code>string openudid = 54;</code>
     * @return This builder for chaining.
     */
    public Builder clearOpenudid() {
      openudid_ = getDefaultInstance().getOpenudid();
      bitField1_ = (bitField1_ & ~0x00200000);
      onChanged();
      return this;
    }
    /**
     * <code>string openudid = 54;</code>
     * @param value The bytes for openudid to set.
     * @return This builder for chaining.
     */
    public Builder setOpenudidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      openudid_ = value;
      bitField1_ |= 0x00200000;
      onChanged();
      return this;
    }

    private java.lang.Object miuiVersion_ = "";
    /**
     * <code>string miuiVersion = 55;</code>
     * @return The miuiVersion.
     */
    public java.lang.String getMiuiVersion() {
      java.lang.Object ref = miuiVersion_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        miuiVersion_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string miuiVersion = 55;</code>
     * @return The bytes for miuiVersion.
     */
    public com.google.protobuf.ByteString
        getMiuiVersionBytes() {
      java.lang.Object ref = miuiVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        miuiVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string miuiVersion = 55;</code>
     * @param value The miuiVersion to set.
     * @return This builder for chaining.
     */
    public Builder setMiuiVersion(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      miuiVersion_ = value;
      bitField1_ |= 0x00400000;
      onChanged();
      return this;
    }
    /**
     * <code>string miuiVersion = 55;</code>
     * @return This builder for chaining.
     */
    public Builder clearMiuiVersion() {
      miuiVersion_ = getDefaultInstance().getMiuiVersion();
      bitField1_ = (bitField1_ & ~0x00400000);
      onChanged();
      return this;
    }
    /**
     * <code>string miuiVersion = 55;</code>
     * @param value The bytes for miuiVersion to set.
     * @return This builder for chaining.
     */
    public Builder setMiuiVersionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      miuiVersion_ = value;
      bitField1_ |= 0x00400000;
      onChanged();
      return this;
    }

    private float cpuFreq_ ;
    /**
     * <code>float cpuFreq = 56;</code>
     * @return The cpuFreq.
     */
    @java.lang.Override
    public float getCpuFreq() {
      return cpuFreq_;
    }
    /**
     * <code>float cpuFreq = 56;</code>
     * @param value The cpuFreq to set.
     * @return This builder for chaining.
     */
    public Builder setCpuFreq(float value) {

      cpuFreq_ = value;
      bitField1_ |= 0x00800000;
      onChanged();
      return this;
    }
    /**
     * <code>float cpuFreq = 56;</code>
     * @return This builder for chaining.
     */
    public Builder clearCpuFreq() {
      bitField1_ = (bitField1_ & ~0x00800000);
      cpuFreq_ = 0F;
      onChanged();
      return this;
    }

    private int cpuNumber_ ;
    /**
     * <code>int32 cpuNumber = 57;</code>
     * @return The cpuNumber.
     */
    @java.lang.Override
    public int getCpuNumber() {
      return cpuNumber_;
    }
    /**
     * <code>int32 cpuNumber = 57;</code>
     * @param value The cpuNumber to set.
     * @return This builder for chaining.
     */
    public Builder setCpuNumber(int value) {

      cpuNumber_ = value;
      bitField1_ |= 0x01000000;
      onChanged();
      return this;
    }
    /**
     * <code>int32 cpuNumber = 57;</code>
     * @return This builder for chaining.
     */
    public Builder clearCpuNumber() {
      bitField1_ = (bitField1_ & ~0x01000000);
      cpuNumber_ = 0;
      onChanged();
      return this;
    }

    private int batteryStatus_ ;
    /**
     * <code>int32 batteryStatus = 58;</code>
     * @return The batteryStatus.
     */
    @java.lang.Override
    public int getBatteryStatus() {
      return batteryStatus_;
    }
    /**
     * <code>int32 batteryStatus = 58;</code>
     * @param value The batteryStatus to set.
     * @return This builder for chaining.
     */
    public Builder setBatteryStatus(int value) {

      batteryStatus_ = value;
      bitField1_ |= 0x02000000;
      onChanged();
      return this;
    }
    /**
     * <code>int32 batteryStatus = 58;</code>
     * @return This builder for chaining.
     */
    public Builder clearBatteryStatus() {
      bitField1_ = (bitField1_ & ~0x02000000);
      batteryStatus_ = 0;
      onChanged();
      return this;
    }

    private int batteryPower_ ;
    /**
     * <code>int32 batteryPower = 59;</code>
     * @return The batteryPower.
     */
    @java.lang.Override
    public int getBatteryPower() {
      return batteryPower_;
    }
    /**
     * <code>int32 batteryPower = 59;</code>
     * @param value The batteryPower to set.
     * @return This builder for chaining.
     */
    public Builder setBatteryPower(int value) {

      batteryPower_ = value;
      bitField1_ |= 0x04000000;
      onChanged();
      return this;
    }
    /**
     * <code>int32 batteryPower = 59;</code>
     * @return This builder for chaining.
     */
    public Builder clearBatteryPower() {
      bitField1_ = (bitField1_ & ~0x04000000);
      batteryPower_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object con_ = "";
    /**
     * <code>string con = 60;</code>
     * @return The con.
     */
    public java.lang.String getCon() {
      java.lang.Object ref = con_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        con_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string con = 60;</code>
     * @return The bytes for con.
     */
    public com.google.protobuf.ByteString
        getConBytes() {
      java.lang.Object ref = con_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        con_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string con = 60;</code>
     * @param value The con to set.
     * @return This builder for chaining.
     */
    public Builder setCon(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      con_ = value;
      bitField1_ |= 0x08000000;
      onChanged();
      return this;
    }
    /**
     * <code>string con = 60;</code>
     * @return This builder for chaining.
     */
    public Builder clearCon() {
      con_ = getDefaultInstance().getCon();
      bitField1_ = (bitField1_ & ~0x08000000);
      onChanged();
      return this;
    }
    /**
     * <code>string con = 60;</code>
     * @param value The bytes for con to set.
     * @return This builder for chaining.
     */
    public Builder setConBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      con_ = value;
      bitField1_ |= 0x08000000;
      onChanged();
      return this;
    }

    private java.lang.Object lan_ = "";
    /**
     * <code>string lan = 61;</code>
     * @return The lan.
     */
    public java.lang.String getLan() {
      java.lang.Object ref = lan_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        lan_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string lan = 61;</code>
     * @return The bytes for lan.
     */
    public com.google.protobuf.ByteString
        getLanBytes() {
      java.lang.Object ref = lan_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lan_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string lan = 61;</code>
     * @param value The lan to set.
     * @return This builder for chaining.
     */
    public Builder setLan(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      lan_ = value;
      bitField1_ |= 0x10000000;
      onChanged();
      return this;
    }
    /**
     * <code>string lan = 61;</code>
     * @return This builder for chaining.
     */
    public Builder clearLan() {
      lan_ = getDefaultInstance().getLan();
      bitField1_ = (bitField1_ & ~0x10000000);
      onChanged();
      return this;
    }
    /**
     * <code>string lan = 61;</code>
     * @param value The bytes for lan to set.
     * @return This builder for chaining.
     */
    public Builder setLanBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      lan_ = value;
      bitField1_ |= 0x10000000;
      onChanged();
      return this;
    }

    private java.lang.Object hardwareModel_ = "";
    /**
     * <code>string hardwareModel = 62;</code>
     * @return The hardwareModel.
     */
    public java.lang.String getHardwareModel() {
      java.lang.Object ref = hardwareModel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hardwareModel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string hardwareModel = 62;</code>
     * @return The bytes for hardwareModel.
     */
    public com.google.protobuf.ByteString
        getHardwareModelBytes() {
      java.lang.Object ref = hardwareModel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hardwareModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string hardwareModel = 62;</code>
     * @param value The hardwareModel to set.
     * @return This builder for chaining.
     */
    public Builder setHardwareModel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      hardwareModel_ = value;
      bitField1_ |= 0x20000000;
      onChanged();
      return this;
    }
    /**
     * <code>string hardwareModel = 62;</code>
     * @return This builder for chaining.
     */
    public Builder clearHardwareModel() {
      hardwareModel_ = getDefaultInstance().getHardwareModel();
      bitField1_ = (bitField1_ & ~0x20000000);
      onChanged();
      return this;
    }
    /**
     * <code>string hardwareModel = 62;</code>
     * @param value The bytes for hardwareModel to set.
     * @return This builder for chaining.
     */
    public Builder setHardwareModelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      hardwareModel_ = value;
      bitField1_ |= 0x20000000;
      onChanged();
      return this;
    }

    private int authStatus_ ;
    /**
     * <code>int32 authStatus = 63;</code>
     * @return The authStatus.
     */
    @java.lang.Override
    public int getAuthStatus() {
      return authStatus_;
    }
    /**
     * <code>int32 authStatus = 63;</code>
     * @param value The authStatus to set.
     * @return This builder for chaining.
     */
    public Builder setAuthStatus(int value) {

      authStatus_ = value;
      bitField1_ |= 0x40000000;
      onChanged();
      return this;
    }
    /**
     * <code>int32 authStatus = 63;</code>
     * @return This builder for chaining.
     */
    public Builder clearAuthStatus() {
      bitField1_ = (bitField1_ & ~0x40000000);
      authStatus_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:ReqDevice)
  }

  // @@protoc_insertion_point(class_scope:ReqDevice)
  private static final cn.taken.ad.logic.adv.yaya.dto.ReqDevice DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.yaya.dto.ReqDevice();
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqDevice getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReqDevice>
      PARSER = new com.google.protobuf.AbstractParser<ReqDevice>() {
    @java.lang.Override
    public ReqDevice parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ReqDevice> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReqDevice> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqDevice getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

