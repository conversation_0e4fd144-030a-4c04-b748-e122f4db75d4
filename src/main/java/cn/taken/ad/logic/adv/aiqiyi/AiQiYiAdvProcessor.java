package cn.taken.ad.logic.adv.aiqiyi;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.business.ActionType;
import cn.taken.ad.constant.business.CarrierType;
import cn.taken.ad.constant.business.ConnectionType;
import cn.taken.ad.constant.business.CoordinateType;
import cn.taken.ad.constant.business.DeviceType;
import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.constant.business.MacroType;
import cn.taken.ad.constant.business.MaterialType;
import cn.taken.ad.constant.business.OrientationType;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.business.TagType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.pojo.advertiser.Advertiser;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.aiqiyi.dto.AdActionType;
import cn.taken.ad.logic.adv.aiqiyi.dto.BidRequest;
import cn.taken.ad.logic.adv.aiqiyi.dto.BidResponse;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestGeoDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestNetworkDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestTagDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestUserDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseMiniProgramDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ParamParser;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 爱奇艺ssp
 */
@Component("AIQIYI" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class AiQiYiAdvProcessor implements AdvProcessor {
    @Resource
    private BaseRedisL2Cache baseRedisL2Cache;

    private static final String ENCRIYOTION_TOKEN = "encriyption_token";
    private static final String INTERGRITY_TOKEN = "integrity_token";

    private static final String TITLE_LEN = "titleLen"; // 标题长度

    private static final Logger log = LoggerFactory.getLogger(AiQiYiAdvProcessor.class);


    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        BidRequest.Builder builder = BidRequest.newBuilder();
        builder.setId(rtbDto.getReqId());
        builder.setTimestamp(System.currentTimeMillis());
        builder.addImp(createImpl(rtbDto, advDto));
        builder.setResourcetype(1);
        builder.setApp(createApp(rtbDto, advDto));
        builder.setDevice(createDevice(rtbDto, advDto));
        builder.setUser(createUser(rtbDto));
        BidRequest request = builder.build();
        advDto.setReqObj(request);

        byte[] bytes = request.toByteArray();
        //log.info("Url:{},Request:{}",url, JsonFormat.printer().omittingInsignificantWhitespace().print(request));
        HttpResult result = httpClient.postBytes(advDto.getRtburl(), bytes, new Header[]{new BasicHeader("Content-Type", "application/octet-stream")
        }, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(result);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        return parseResponse(result, rtbDto, advDto);
    }

    private RtbResponseDto parseResponse(HttpResult result, RtbRequestDto rtbDto, RtbAdvDto advDto) {
        BidResponse response;
        try {
            response = BidResponse.parseFrom(result.getData());
            advDto.setRespObj(response);
            //log.info("Response:{}", JsonFormat.printer().omittingInsignificantWhitespace().print(response));
        } catch (Exception e) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), "Serializable resp fail", "Serializable resp fail");
        }
        if (response.getStatus() != 0) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(),response.getStatus()+"", "" + response.getStatus());
        }
        if (response.getBidList().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        Map<String, String> params = ParamParser.parseParamByJson(advDto.getPnyParam());
        String eToken = params.get(ENCRIYOTION_TOKEN);
        String iToken = params.get(INTERGRITY_TOKEN);
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", "");
        String respId = response.getId();
        response.getBidList().forEach(bid -> {
            TagResponseDto tag = new TagResponseDto();
            tag.setTagInfoId(respId);
            if (StringUtils.isNotBlank(bid.getCrid())) {
                tag.setCreativeId(bid.getCrid());
            }
            if (bid.getPrice() > 0) {
                tag.setPrice((double) bid.getPrice());
            }
            String price;
            if (null != tag.getPrice()){
                price = encrtPrice(tag.getPrice(), eToken, iToken);
            }else{
                price="";
            }
            tag.setWinNoticeUrls(new LinkedList<>());
            if (!CollectionUtils.isEmpty(bid.getWinNoticeUrlList())) {
                tag.getWinNoticeUrls().addAll(new ArrayList<>(bid.getWinNoticeUrlList()));
            }

            List<ResponseTrackDto> trackDtos = new LinkedList<>();
            tag.setTracks(trackDtos);

            tag.setActionType(ActionType.SYSTEM_BROWSER_H5);
            if (bid.hasAction()) {
                if (bid.getAction() == AdActionType.DOWNLOAD_APP) {
                    tag.setActionType(ActionType.DOWNLOAD);
                } else if (bid.getAction() == AdActionType.OPEN_APP_DEEPLINK || bid.getAction() == AdActionType.OPEN_APP_UNIVERSAL_LINK) {
                    tag.setActionType(ActionType.DEEPLINK);
                } else if (bid.getAction() == AdActionType.OPEN_MINI_APP) {
                    tag.setActionType(ActionType.MINI_PROGRAM);
                } else if (bid.getAction() == AdActionType.OPEN_IN_WEBVIEW) {
                    tag.setActionType(ActionType.WEB_VIEW_H5);
                }
            }
            //请求没有贴片类型,这里不处理贴片
            if (bid.hasAdmnative()) {
                BidResponse.Bid.AdmNative amv = bid.getAdmnative();
                if (StringUtils.isNotBlank(amv.getTitle())) {
                    tag.setTitle(amv.getTitle());
                }
                if (!CollectionUtils.isEmpty(amv.getImgsList())) {
                    tag.setMaterialType(MaterialType.IMAGE_TEXT);
                    List<String> images = new ArrayList<>();
                    Integer imageWidth = null;
                    Integer imageHeight = null;
                    for (BidResponse.Bid.Image image : amv.getImgsList()) {
                        if (StringUtils.isNotBlank(image.getUrl())){
                            if (image.getType() == BidResponse.Bid.Image.ImageAssetType.ICON){
                                tag.setIconUrl(image.getUrl());
                            }else if (image.getType() == BidResponse.Bid.Image.ImageAssetType.LOGO){
                                tag.setLogoUrl(image.getUrl());
                            }else if (image.getType() == BidResponse.Bid.Image.ImageAssetType.MAIN || image.getType() == BidResponse.Bid.Image.ImageAssetType.UNKNOWN){
                                images.add(image.getUrl());
                                if (null == imageWidth && image.getW() > 0){
                                    imageWidth = image.getW();
                                }
                                if (null == imageHeight && image.getH() > 0){
                                    imageHeight = image.getH();
                                }
                            }
                        }
                    }
                    if(null != imageWidth){
                        tag.setMaterialWidth(imageWidth);
                    }
                    if(null != imageHeight){
                        tag.setMaterialHeight(imageHeight);
                    }
                    tag.setImgUrls(images);
                }
                if (amv.hasVideo()) {
                    tag.setMaterialType(MaterialType.VIDEO);
                    ResponseVideoDto videoDto = new ResponseVideoDto();
                    BidResponse.Bid.Video video = amv.getVideo();
                    videoDto.setVideoUrl(video.getUrl());
                    if (video.getW() > 0) {
                        tag.setMaterialWidth(video.getW());
                        videoDto.setVideoWidth(video.getW());
                    }
                    if (video.getH() > 0) {
                        tag.setMaterialHeight(video.getH());
                        videoDto.setVideoHeight(video.getH());
                    }
                    if (video.getDuration() > 0) {
                        videoDto.setDuration(video.getDuration());
                    }
                    List<String> coverUrls = new LinkedList<>();
                    if (StringUtils.isNotBlank(video.getStartCover())) {
                        coverUrls.add(video.getStartCover());
                    }
                    if (StringUtils.isNotBlank(video.getCompleteCover())) {
                        coverUrls.add(video.getCompleteCover());
                    }
                    videoDto.setCoverImgUrls(coverUrls);
                    tag.setVideoInfo(videoDto);
                }
                ResponseAppDto appDto = new ResponseAppDto();
                tag.setAppInfo(appDto);
                if (StringUtils.isNotBlank(amv.getAppName())){
                    appDto.setAppName(amv.getAppName());
                }
                if (StringUtils.isNotBlank(amv.getAppVersion())){
                    appDto.setAppVersion(amv.getAppVersion());
                }
                if (StringUtils.isNotBlank(amv.getPackageName())){
                    appDto.setPackageName(amv.getPackageName());
                }
                if (StringUtils.isNotBlank(amv.getAppIcon())){
                    appDto.setAppIconUrl(amv.getAppIcon());
                }
                if (amv.hasLink()){
                    parseResponseLink(amv.getLink(),tag,price,respId,rtbDto.getDevice(),rtbDto.getNetwork());
                }
            } else if (bid.hasOpening()) {
                if (bid.getCreativeType() == BidResponse.Bid.CreativeType.IMG){
                    tag.setMaterialType(MaterialType.IMAGE_TEXT);
                    tag.setImgUrls(new ArrayList<>(Collections.singletonList(bid.getAdUrl())));
                    if (bid.getAdWidth() > 0){
                        tag.setMaterialWidth(bid.getAdWidth());
                    }
                    if (bid.getAdHeight() > 0){
                        tag.setMaterialHeight(bid.getAdHeight());
                    }
                }else if (bid.getCreativeType() == BidResponse.Bid.CreativeType.VIDEO){
                    tag.setMaterialType(MaterialType.VIDEO);
                    ResponseVideoDto videoDto = new ResponseVideoDto();
                    videoDto.setVideoUrl(bid.getAdUrl());
                    if (bid.getAdWidth() > 0) {
                        tag.setMaterialWidth(bid.getAdWidth());
                        videoDto.setVideoWidth(bid.getAdWidth());
                    }
                    if (bid.getAdHeight() > 0) {
                        tag.setMaterialHeight(bid.getAdHeight());
                        videoDto.setVideoHeight(bid.getAdHeight());
                    }
                    if (bid.getAdVideo().getDuration() > 0){
                        videoDto.setDuration(bid.getAdVideo().getDuration());
                    }
                    if (bid.getCreativeDirection() == 1){
                        videoDto.setOrientationType(OrientationType.HORIZONTAL.getType());
                    }else if (bid.getCreativeDirection() == 2){
                        videoDto.setOrientationType(OrientationType.VERTICAL.getType());
                    }
                    tag.setVideoInfo(videoDto);
                }
                tag.setTitle(bid.getTitle());
                tag.setDesc(bid.getDescription());
            }
            if (tag.getAppInfo() == null){
                ResponseAppDto appDto = new ResponseAppDto();
                tag.setAppInfo(appDto);
                if (StringUtils.isNotBlank(bid.getDetailPageUrl())) {
                    appDto.setAppInfoUrl(bid.getDetailPageUrl());
                }
                if (StringUtils.isNotBlank(bid.getAppName())) {
                    appDto.setAppName(bid.getAppName());
                }
                if (StringUtils.isNotBlank(bid.getAppVersion())) {
                    appDto.setAppVersion(bid.getAppVersion());
                }
                if (StringUtils.isNotBlank(bid.getAppDeveloper())) {
                    appDto.setAppDeveloper(bid.getAppDeveloper());
                }
                if (StringUtils.isNotBlank(bid.getAppPermission())) {
                    appDto.setAppPermissionInfoUrl(bid.getAppPermission());
                }
                if (StringUtils.isNotBlank(bid.getAppPrivacy())) {
                    appDto.setAppPrivacyUrl(bid.getAppPrivacy());
                }
            }
            if (StringUtils.isNotBlank(bid.getAppDescPageUrl())) {
                tag.setClickUrl(bid.getAppDescPageUrl());
            }
            ResponseMiniProgramDto miniProgramDto = new ResponseMiniProgramDto();
            if (StringUtils.isNotBlank(bid.getMiniAppName())) {
                miniProgramDto.setName(bid.getMiniAppName());
                tag.setMiniProgram(miniProgramDto);
            }
            if (StringUtils.isNotBlank(bid.getMiniAppPath())) {
                miniProgramDto.setPath(bid.getMiniAppPath());
                tag.setMiniProgram(miniProgramDto);
            }
            if (bid.hasLink()) {
                parseResponseLink(bid.getLink(),tag,price,respId,rtbDto.getDevice(),rtbDto.getNetwork());
            }
            responseDto.getTags().add(tag);
        });
        return responseDto;
    }

    private void parseResponseLink(BidResponse.Bid.Link link, TagResponseDto tag, String price, String respId, RequestDeviceDto deviceDto, RequestNetworkDto networkDto){
        if (StringUtils.isEmpty(tag.getClickUrl())){
            tag.setClickUrl(link.getCurl());
            if (tag.getActionType() == ActionType.DEEPLINK && null != deviceDto.getOsType() && deviceDto.getOsType() == OsType.IOS){
                tag.setUniversalLink(link.getCurl());
            }
        }
        if (!CollectionUtils.isEmpty(link.getImptrackersList())) {
            tag.getTracks().add(new ResponseTrackDto(EventType.EXPOSURE.getType(), new ArrayList<>(link.getImptrackersList())));
        }
        if (!CollectionUtils.isEmpty(link.getClicktrackersList())) {
            tag.getTracks().add(new ResponseTrackDto(EventType.CLICK.getType(), new ArrayList<>(link.getClicktrackersList())));
        }
        if (StringUtils.isNotBlank(link.getDeeplink())) {
            tag.setDeepLinkUrl(link.getDeeplink());
        }
        if (!CollectionUtils.isEmpty(link.getStarttrackersList())) {
            //播放开始，开始上报监测
            tag.getTracks().add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), new ArrayList<>(link.getStarttrackersList())));
        }
        if (!CollectionUtils.isEmpty(link.getFirstQuartileTrackersList())) {
            //视频播放至1/4，开始上报监测
            tag.getTracks().add(new ResponseTrackDto(EventType.VIDEO_25.getType(), new ArrayList<>(link.getFirstQuartileTrackersList())));
        }
        if (!CollectionUtils.isEmpty(link.getMidPointTrackersList())) {
            //视频播放至1/2，开始上报监测
            tag.getTracks().add(new ResponseTrackDto(EventType.VIDEO_50.getType(), new ArrayList<>(link.getMidPointTrackersList())));
        }
        if (!CollectionUtils.isEmpty(link.getThirdQuartileTrackersList())) {
            //视频播放至3/4，开始上报监测
            tag.getTracks().add(new ResponseTrackDto(EventType.VIDEO_75.getType(), new ArrayList<>(link.getThirdQuartileTrackersList())));
        }
        if (!CollectionUtils.isEmpty(link.getCompletetrackersList())) {
            //视频播放完成，开始上报监测
            tag.getTracks().add(new ResponseTrackDto(EventType.VIDEO_END.getType(), new ArrayList<>(link.getCompletetrackersList())));
        }
        if (link.hasDownloadtrackers()) {
            BidResponse.Bid.DownloadTracker downloadTracker = link.getDownloadtrackers();
            if (!CollectionUtils.isEmpty(downloadTracker.getStartdownloadList())) {
                tag.getTracks().add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), new ArrayList<>(downloadTracker.getStartdownloadList())));
            }
            if (!CollectionUtils.isEmpty(downloadTracker.getFinishdownloadList())) {
                tag.getTracks().add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), new ArrayList<>(downloadTracker.getFinishdownloadList())));
            }
            if (!CollectionUtils.isEmpty(downloadTracker.getStartinstallList())) {
                tag.getTracks().add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), new ArrayList<>(downloadTracker.getStartinstallList())));
            }
            if (!CollectionUtils.isEmpty(downloadTracker.getFinishinstallList())) {
                tag.getTracks().add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), new ArrayList<>(downloadTracker.getFinishinstallList())));
            }
        }
        if (!CollectionUtils.isEmpty(link.getConversionTrackersList())) {
            tag.getTracks().add(new ResponseTrackDto(EventType.ACTIVE_APP.getType(), new ArrayList<>(link.getConversionTrackersList())));
        }
        for (ResponseTrackDto track : tag.getTracks()) {
            List<String> urls = track.getTrackUrls();
            if (Objects.equals(track.getTrackType(), EventType.EXPOSURE.getType()) || Objects.equals(track.getTrackType(), EventType.CLICK.getType())) {
                if (StringUtils.isNotBlank(price)) {
                    urls = replaceMacro("${AUCTION_PRICE}", urls, price);
                }
            }
            if (StringUtils.isNotBlank(respId)) {
                urls = replaceMacro("__REQUESTID__", urls, respId);
            }
            OsType osType = deviceDto.getOsType();
            if (null != osType){
                switch (osType){
                    case ANDROID:
                        urls = replaceMacro("__OS__", urls,"0");
                        if (StringUtils.isNotBlank(deviceDto.getAndroidId())){
                            urls = replaceMacro("__ANDROIDID__", urls,Md5.md5(deviceDto.getAndroidId()).toLowerCase());
                        }else if (StringUtils.isNotBlank(deviceDto.getAndroidIdMd5())){
                            urls = replaceMacro("__ANDROIDID__", urls,deviceDto.getAndroidIdMd5().toLowerCase());
                        }
                        break;
                    case IOS:
                        urls = replaceMacro("__OS__", urls,"1");
                        if (StringUtils.isNotBlank(deviceDto.getIdfa())) {
                            urls = replaceMacro("__IDFA__", urls,deviceDto.getIdfa());
                        }
                        if (StringUtils.isNotBlank(deviceDto.getOpenUdId())){
                            urls = replaceMacro("__OPENUDID__", urls,deviceDto.getOpenUdId());
                        }
                        break;
                    case WINDOWS_PC:
                        urls = replaceMacro("__OS__", urls,"2");
                        break;
                    default:
                        urls = replaceMacro("__OS__", urls,"3");
                        break;
                }
            }else{
                urls = replaceMacro("__OS__", urls,"3");
            }
            if (StringUtils.isNotBlank(deviceDto.getImei())){
                urls = replaceMacro("__IMEI__", urls, Md5.md5(deviceDto.getImei()).toLowerCase());
            }else if (StringUtils.isNotBlank(deviceDto.getImeiMd5())){
                urls = replaceMacro("__IMEI__", urls,deviceDto.getImeiMd5().toLowerCase());
            }
            if (StringUtils.isNotBlank(networkDto.getMac())){
                String mac = Md5.md5(networkDto.getMac().replace(":","").toUpperCase()).toLowerCase();
                urls = replaceMacro("__MAC__", urls,mac);
                urls = replaceMacro("__MAC1__", urls,Md5.md5(networkDto.getMac().toUpperCase()).toLowerCase());
            }else if (StringUtils.isNotBlank(networkDto.getMacMd5())){
                urls = replaceMacro("__MAC1__", urls,Md5.md5(networkDto.getMac()));
            }
            urls = replaceMacro("__TS__", urls, MacroType.TIME.getCode());
            urls = replaceMacro("__IMP_AREA_X1Y1X2Y2__", urls, MacroType.DISPLAY_LUX.getCode() + "_" + MacroType.DISPLAY_LUY.getCode() + "_" + MacroType.DISPLAY_RDX.getCode() + "_" + MacroType.DISPLAY_RDY.getCode());
            urls = replaceMacro("__BUTTON_AREA_X1Y1X2Y2__", urls, MacroType.BUTTON_LUX.getCode() + "_" + MacroType.BUTTON_LUY.getCode() + "_" + MacroType.BUTTON_RDX.getCode() + "_" + MacroType.BUTTON_RDY.getCode());
            urls = replaceMacro("__CLICK_POS_XY__", urls, MacroType.DP_DOWN_X.getCode() + "_" + MacroType.DP_DOWN_Y.getCode());
            urls = replaceMacro("CUPID_CCN", urls, "20001");
            urls = replaceMacro("__TARGET_APP_INSTALL__", urls, MacroType.TARGET_APP_INSTALL.getCode());
            track.setTrackUrls(urls);
        }
    }

    private BidRequest.User createUser(RtbRequestDto rtbDto) {
        BidRequest.User.Builder builder = BidRequest.User.newBuilder();
        RequestUserDto userDto = rtbDto.getUser();
        if (userDto.getAge() != null) {
            int age = userDto.getAge();
            if (age == 0) {
                builder.setAge(0);
            } else if (age >= 1 && age <= 18) {
                builder.setAge(1);
            } else if (age >= 19 && age <= 24) {
                builder.setAge(2);
            } else if (age >= 25 && age <= 30) {
                builder.setAge(3);
            } else if (age >= 31 && age <= 35) {
                builder.setAge(4);
            } else if (age >= 36 && age <= 40) {
                builder.setAge(5);
            } else {
                builder.setAge(6);
            }
        }
        if (StringUtils.isNotBlank(userDto.getGender())) {
            builder.setGender(userDto.getGender());
        }
        if (userDto.getInterest() != null && userDto.getInterest().length > 0) {
            builder.setKeywords(StringUtils.join(userDto.getInterest(), ","));
        }
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        if (!CollectionUtils.isEmpty(deviceDto.getInstalledAppInfo())) {
            List<String> appList = new LinkedList<>();
            deviceDto.getInstalledAppInfo().forEach(v -> {
                if (StringUtils.isNotBlank(v.getAppName())) {
                    appList.add(v.getAppName());
                }
            });
            if (appList.size() > 0) {
                builder.setApplist(StringUtils.join(appList, ","));
            }
        }
        return builder.build();
    }

    private BidRequest.Device createDevice(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        BidRequest.Device.Builder builder = BidRequest.Device.newBuilder();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        //必填
        builder.setUa(deviceDto.getUserAgent());
        RequestGeoDto geoDto = rtbDto.getGeo();
        if (null != geoDto){
            BidRequest.Geo.Builder geo = BidRequest.Geo.newBuilder();
            if (geoDto.getLatitude() != null) {
                geo.setLat(geoDto.getLatitude());
            }
            if (geoDto.getLongitude() != null) {
                geo.setLon(geoDto.getLongitude());
            }
            CoordinateType coordinateType = geoDto.getCoordinateType();
            switch (coordinateType) {
                case GLOBAL:
                case BAIDU:
                case STATE:
                    geo.setType(BidRequest.Geo.LocationType.GPS_LOCATION);
                    break;
            }
            // country、prov、city、district 暂无
            builder.setGeo(geo);
        }
        //必填
        builder.setIp(rtbDto.getNetwork().getIp());
        if (deviceDto.getOsType() != null && deviceDto.getOsType() == OsType.IOS){
            if (StringUtils.isNotBlank(deviceDto.getIdfa())) {
                builder.setIdfa(deviceDto.getIdfa());
            }
            if (StringUtils.isNotBlank(deviceDto.getIdfaMd5())) {
                builder.setIdfaMd5(deviceDto.getIdfaMd5());
            }
            if (StringUtils.isNotBlank(deviceDto.getSysInitTime())) {
                builder.setFileInitTime(deviceDto.getSysInitTime());
            }
        }
        if (StringUtils.isNotBlank(deviceDto.getImeiMd5())) {
            builder.setImeiMd5(deviceDto.getImeiMd5());
        }
        if (deviceDto.getOsType() != null && deviceDto.getOsType() == OsType.ANDROID){
            if (StringUtils.isNotBlank(deviceDto.getAndroidId())) {
                builder.setAndroidid(deviceDto.getAndroidId());
            }
            if (StringUtils.isNotBlank(deviceDto.getAndroidIdMd5())) {
                builder.setAndroididMd5(deviceDto.getAndroidIdMd5());
            }
            if (StringUtils.isNotBlank(deviceDto.getOaid())) {
                builder.setOaid(deviceDto.getOaid());
            }
            if (StringUtils.isNotBlank(deviceDto.getOaidMd5())) {
                builder.setOaidMd5(deviceDto.getOaidMd5());
            }
        }
        if (StringUtils.isNotBlank(rtbDto.getNetwork().getMac())) {
            builder.setMac(rtbDto.getNetwork().getMac());
        }
        if (StringUtils.isNotBlank(rtbDto.getNetwork().getMacMd5())) {
            builder.setProcessedMacMd5(rtbDto.getNetwork().getMacMd5());
        }
        if (StringUtils.isNotBlank(deviceDto.getBrand())) {
            builder.setMake(deviceDto.getBrand());
        }
        if (StringUtils.isNotBlank(deviceDto.getModel())) {
            builder.setModel(deviceDto.getModel());
        }
        if (StringUtils.isNotBlank(deviceDto.getOsVersion())) {
            builder.setOsv(deviceDto.getOsVersion());
        }
        if (deviceDto.getOsType() != null) {
            builder.setOs(deviceDto.getOsType().getCode().toLowerCase());
        }
        if (null != deviceDto.getHeight()) {
            builder.setH(deviceDto.getHeight());
        }
        if (null != deviceDto.getWidth()) {
            builder.setW(deviceDto.getWidth());
        }
        if (deviceDto.getDeviceType() != null) {
            DeviceType deviceType = deviceDto.getDeviceType();
            switch (deviceType) {
                case PHONE:
                    builder.setDevicetype(BidRequest.Device.DeviceType.PHONE);
                    break;
                case TV:
                    builder.setDevicetype(BidRequest.Device.DeviceType.TV);
                    break;
                case PC:
                    builder.setDevicetype(BidRequest.Device.DeviceType.PERSONAL_COMPUTER);
                    break;
                case PAD:
                    builder.setDevicetype(BidRequest.Device.DeviceType.PAD);
                    break;
                default:
                    builder.setDevicetype(BidRequest.Device.DeviceType.UNKNOWN_DEVICE);
            }
        }
        builder.setCarrier(0);
        builder.setCarrierName("unknown");
        if (rtbDto.getNetwork().getCarrierType() != null) {
            CarrierType carrierType = rtbDto.getNetwork().getCarrierType();
            switch (carrierType) {
                case CM:
                    builder.setCarrier(1);
                    builder.setCarrierName("中国移动");
                    break;
                case CU:
                    builder.setCarrier(2);
                    builder.setCarrierName("中国联通");
                    break;
                case CT:
                    builder.setCarrier(3);
                    builder.setCarrierName("中国电信");
                    break;
            }
        }
        builder.setConnectiontype(BidRequest.Device.ConnectionType.CONNECTION_UNKNOWN);
        if (rtbDto.getNetwork().getConnectType() != null) {
            ConnectionType connectionType = rtbDto.getNetwork().getConnectType();
            switch (connectionType) {
                case WIFI:
                    builder.setConnectiontype(BidRequest.Device.ConnectionType.WIFI);
                    break;
                case NETWORK_2G:
                    builder.setConnectiontype(BidRequest.Device.ConnectionType.CELL_2G);
                    break;
                case NETWORK_3G:
                    builder.setConnectiontype(BidRequest.Device.ConnectionType.CELL_3G);
                    break;
                case NETWORK_4G:
                    builder.setConnectiontype(BidRequest.Device.ConnectionType.CELL_4G);
                    break;
                case NETWORK_5G:
                    builder.setConnectiontype(BidRequest.Device.ConnectionType.CELL_5G);
                    break;
                case NETWORK_CELLULAR:
                    builder.setConnectiontype(BidRequest.Device.ConnectionType.CELL_UNKNOWN);
                    break;
                case ETHERNET:
                    builder.setConnectiontype(BidRequest.Device.ConnectionType.ETHERNET);
                    break;
                default:
                    builder.setConnectiontype(BidRequest.Device.ConnectionType.CONNECTION_UNKNOWN);
                    break;
            }
        }
        if (!CollectionUtils.isEmpty(deviceDto.getCaids())) {
            BidRequest.Device.CaidInfo.Builder caidInfo = BidRequest.Device.CaidInfo.newBuilder();
            deviceDto.getCaids().forEach(caid -> {
                BidRequest.Device.Caid.Builder cid = BidRequest.Device.Caid.newBuilder();
                if (StringUtils.isNotBlank(caid.getCaid())) {
                    cid.setCaid(caid.getCaid());
                }
                if (StringUtils.isNotBlank(caid.getVersion())) {
                    cid.setVersion(caid.getVersion());
                }
                caidInfo.addCaid(cid);
            });
            builder.setCaidInfo(caidInfo);
        }
        if (StringUtils.isNotBlank(deviceDto.getCountry())) {
            builder.setCountryCode(deviceDto.getCountry());
        }
        if (StringUtils.isNotBlank(deviceDto.getTimeZone())) {
            builder.setTimeZoneSec(deviceDto.getTimeZone());
        }
        if (StringUtils.isNotBlank(deviceDto.getDeviceNameMd5())) {
            builder.setDeviceNameMd5(deviceDto.getDeviceNameMd5());
        }
        if (StringUtils.isNotBlank(deviceDto.getLanguage())) {
            builder.setDeviceLanguage(deviceDto.getLanguage());
        }
        if (StringUtils.isNotBlank(deviceDto.getHardwareMachine())) {
            builder.setMachineOfDevice(deviceDto.getHardwareMachine());
        }
        if (StringUtils.isNotBlank(deviceDto.getBootMark())) {
            builder.setBootMark(deviceDto.getBootMark());
        }
        if (StringUtils.isNotBlank(deviceDto.getUpdateMark())) {
            builder.setUpdateMark(deviceDto.getUpdateMark());
        }
        if (null != deviceDto.getDeviceHardDisk()) {
            builder.setDiskTotal(deviceDto.getDeviceHardDisk());
        }
        if (null != deviceDto.getDeviceMemory()) {
            builder.setMemTotal(deviceDto.getDeviceMemory());
        }
        return builder.build();
    }

    private BidRequest.App createApp(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        BidRequest.App.Builder builder = BidRequest.App.newBuilder();
        RequestAppDto appDto = rtbDto.getApp();
        if (StringUtils.isNotBlank(appDto.getAppName())) {
            builder.setName(appDto.getAppName());
        }
        if (StringUtils.isNotBlank(appDto.getBundle())) {
            builder.setBundle(appDto.getBundle());
        }
        if (StringUtils.isNotBlank(appDto.getAppDomainUrl())) {
            builder.setDomain(appDto.getAppDomainUrl());
        }
        if (StringUtils.isNotBlank(appDto.getAppstoreUrl())) {
            builder.setStoreurl(appDto.getAppstoreUrl());
        }
        if (StringUtils.isNotBlank(appDto.getAppVersion())) {
            builder.setVer(appDto.getAppVersion());
        }
        builder.setDeeplinkstate(2);
        return builder.build();

    }

    private BidRequest.Imp createImpl(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        BidRequest.Imp.Builder builder = BidRequest.Imp.newBuilder();
        RequestTagDto tagDto = rtbDto.getTag();
        builder.setAdzoneId(advDto.getTagCode());
        builder.setMediaAdzoneId(tagDto.getTagId());
        if (tagDto.getPrice() != null) {
            builder.setBidfloor(tagDto.getPrice().intValue());
        }
        BidRequest.Imp.Native.Builder nv = BidRequest.Imp.Native.newBuilder();
        Map<String,String> tagParam = ParamParser.parseParamByJson(advDto.getTagPnyParam());
        Map<String,String> advParam = ParamParser.parseParamByJson(advDto.getPnyParam());
        String len = tagParam.get(TITLE_LEN);
        if (StringUtils.isEmpty(len)){
            len = advParam.get(TITLE_LEN);
        }
        if (StringUtils.isNotBlank(len)){
            nv.setTitleLen(Integer.parseInt(len));
        }
        builder.setAdType(BidRequest.Imp.AdType.FEEDS);
        TagType tagType = tagDto.getTagType();
        switch (tagType){
            case INCENTIVE_VIDEO:
            case FULL_SCREEN_VIDEO:
            case DRAW_VIDEO:
            case TABLE_PLAQUE_VIDEO:
            case NORMAL_VIDEO:
            case VIDEO_CONTENT:
                builder.setAdType(BidRequest.Imp.AdType.FEEDS);
                nv.setVideo(convertNativeVideo(tagDto.getWidth(),tagDto.getHeight(),tagDto.getMinDuration(),tagDto.getMaxDuration()));
                builder.setNative(nv);
                break;
            case OPEN:
                builder.setAdType(BidRequest.Imp.AdType.OPENING);
                // 开屏位置不可以使用Native对象
                break;
            case INFORMATION_FLOW:
                // 添加视频、图片
                builder.setAdType(BidRequest.Imp.AdType.FEEDS);
                if (null != tagDto.getMinDuration() && tagDto.getMaxDuration() != null){
                    nv.setVideo(convertNativeVideo(tagDto.getWidth(),tagDto.getHeight(),tagDto.getMinDuration(),tagDto.getMaxDuration()));
                }
                nv.addAllImgs(convertNativeImages(tagDto.getWidth(),tagDto.getHeight()));
                builder.setNative(nv);
                break;
            default:
                // 其他位置默认图片
                nv.addAllImgs(convertNativeImages(tagDto.getWidth(),tagDto.getHeight()));
                builder.setNative(nv);
                break;

        }
        return builder.build();
    }

    private BidRequest.Imp.Native.Video convertNativeVideo(Integer width, Integer height,Integer maxDuration,Integer minDuration){
        BidRequest.Imp.Native.Video.Builder video = BidRequest.Imp.Native.Video.newBuilder();
        if (height != null) {
            video.setH(height);
        }
        if (width != null) {
            video.setW(width);
        }
        if (maxDuration != null) {
            video.setMaxduration(maxDuration);
        }
        if (minDuration != null) {
            video.setMinduration(minDuration);
        }
        return video.build();
    }

    private List<BidRequest.Imp.Native.Image> convertNativeImages (Integer width,Integer height){
        List<BidRequest.Imp.Native.Image> nv = new ArrayList<>();
        BidRequest.Imp.Native.Image.Builder iconImage = BidRequest.Imp.Native.Image.newBuilder();
        iconImage.setType(BidRequest.Imp.Native.Image.ImageAssetType.ICON);
        nv.add(iconImage.build());

        BidRequest.Imp.Native.Image.Builder logoImage = BidRequest.Imp.Native.Image.newBuilder();
        logoImage.setType(BidRequest.Imp.Native.Image.ImageAssetType.LOGO);
        nv.add(logoImage.build());

        BidRequest.Imp.Native.Image.Builder mainImage = BidRequest.Imp.Native.Image.newBuilder();
        mainImage.setType(BidRequest.Imp.Native.Image.ImageAssetType.MAIN);
        if (height != null) {
            mainImage.setH(height);
        }
        if (width != null) {
            mainImage.setW(width);
        }
        nv.add(mainImage.build());
        return nv;
    }

    private String encrtPrice(Double price, String enToken, String iToken) {
        if (null == price || StringUtils.isEmpty(enToken) || StringUtils.isEmpty(iToken)){
            return  "";
        }
        try {
            String vector = UUID.randomUUID().toString().replaceAll("-", "").substring(16);
            return PriceEncrypterUtils.encryptPrice(price.intValue(), vector.getBytes(StandardCharsets.UTF_8), enToken.getBytes(StandardCharsets.UTF_8), iToken.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam rtbDto) throws Exception {
        if (!rtbDto.isBiddingSuccess()) {
            SuperResult.rightResult();
        }
        List<String> urls = rtbDto.getUrls();
        Advertiser advertiser = baseRedisL2Cache.get(BaseRedisKeys.KV_ADVERTISER_ID_ + rtbDto.getAdvertiserId(), Advertiser.class);
        Map<String, String> param = ParamParser.parseParamByJson(advertiser.getPnyParam());
        String eToken = param.get(ENCRIYOTION_TOKEN);
        String iToken = param.get(INTERGRITY_TOKEN);
        if (rtbDto.getPrice() != null) {
            String price = encrtPrice(rtbDto.getPrice(), eToken, iToken);
            if (StringUtils.isNotBlank(price)) {
                urls = replaceMacro("${AUCTION_PRICE}", urls, price);
            } else {
                log.error(" {} 加密价格失败 {}:{} - {}:{} : {}", rtbDto.getRtbId(), advertiser.getId(), advertiser.getName(), eToken, iToken, JsonHelper.toJsonString(urls));
                return SuperResult.badResult();
            }
        }
        boolean success = false;
        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, -1);
            if (result.isSuccess()) {
                success = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", rtbDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return success ? SuperResult.rightResult() : SuperResult.badResult();
    }
}
