// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

/**
 * Protobuf enum {@code MobReqGenderType}
 */
public enum MobReqGenderType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>MobReqGenderType_Other = 0;</code>
   */
  MobReqGenderType_Other(0),
  /**
   * <code>MobReqGenderType_Male = 1;</code>
   */
  MobReqGenderType_Male(1),
  /**
   * <code>MobReqGenderType_Female = 2;</code>
   */
  MobReqGenderType_Female(2),
  /**
   * <code>MobReqGenderType_UnKnow = 3;</code>
   */
  MobReqGenderType_UnKnow(3),
  UNRECOGNIZED(-1),
  ;

  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      MobReqGenderType.class.getName());
  }
  /**
   * <code>MobReqGenderType_Other = 0;</code>
   */
  public static final int MobReqGenderType_Other_VALUE = 0;
  /**
   * <code>MobReqGenderType_Male = 1;</code>
   */
  public static final int MobReqGenderType_Male_VALUE = 1;
  /**
   * <code>MobReqGenderType_Female = 2;</code>
   */
  public static final int MobReqGenderType_Female_VALUE = 2;
  /**
   * <code>MobReqGenderType_UnKnow = 3;</code>
   */
  public static final int MobReqGenderType_UnKnow_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static MobReqGenderType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static MobReqGenderType forNumber(int value) {
    switch (value) {
      case 0: return MobReqGenderType_Other;
      case 1: return MobReqGenderType_Male;
      case 2: return MobReqGenderType_Female;
      case 3: return MobReqGenderType_UnKnow;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<MobReqGenderType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      MobReqGenderType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<MobReqGenderType>() {
          public MobReqGenderType findValueByNumber(int number) {
            return MobReqGenderType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.getDescriptor().getEnumTypes().get(7);
  }

  private static final MobReqGenderType[] VALUES = values();

  public static MobReqGenderType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private MobReqGenderType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:MobReqGenderType)
}

