// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

/**
 * Protobuf type {@code BidRequest4}
 */
public final class BidRequest4 extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:BidRequest4)
    BidRequest4OrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      BidRequest4.class.getName());
  }
  // Use BidRequest4.newBuilder() to construct.
  private BidRequest4(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BidRequest4() {
    id_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_BidRequest4_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_BidRequest4_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.taken.ad.logic.adv.yaya.dto.BidRequest4.class, cn.taken.ad.logic.adv.yaya.dto.BidRequest4.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMP_FIELD_NUMBER = 2;
  private cn.taken.ad.logic.adv.yaya.dto.ReqImp imp_;
  /**
   * <code>.ReqImp imp = 2;</code>
   * @return Whether the imp field is set.
   */
  @java.lang.Override
  public boolean hasImp() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.ReqImp imp = 2;</code>
   * @return The imp.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqImp getImp() {
    return imp_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqImp.getDefaultInstance() : imp_;
  }
  /**
   * <code>.ReqImp imp = 2;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqImpOrBuilder getImpOrBuilder() {
    return imp_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqImp.getDefaultInstance() : imp_;
  }

  public static final int SITE_FIELD_NUMBER = 3;
  private cn.taken.ad.logic.adv.yaya.dto.ReqSite site_;
  /**
   * <code>.ReqSite site = 3;</code>
   * @return Whether the site field is set.
   */
  @java.lang.Override
  public boolean hasSite() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>.ReqSite site = 3;</code>
   * @return The site.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqSite getSite() {
    return site_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqSite.getDefaultInstance() : site_;
  }
  /**
   * <code>.ReqSite site = 3;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqSiteOrBuilder getSiteOrBuilder() {
    return site_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqSite.getDefaultInstance() : site_;
  }

  public static final int DEVICE_FIELD_NUMBER = 4;
  private cn.taken.ad.logic.adv.yaya.dto.ReqDevice device_;
  /**
   * <code>.ReqDevice device = 4;</code>
   * @return Whether the device field is set.
   */
  @java.lang.Override
  public boolean hasDevice() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>.ReqDevice device = 4;</code>
   * @return The device.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqDevice getDevice() {
    return device_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqDevice.getDefaultInstance() : device_;
  }
  /**
   * <code>.ReqDevice device = 4;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqDeviceOrBuilder getDeviceOrBuilder() {
    return device_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqDevice.getDefaultInstance() : device_;
  }

  public static final int USER_FIELD_NUMBER = 5;
  private cn.taken.ad.logic.adv.yaya.dto.ReqUser user_;
  /**
   * <code>.ReqUser user = 5;</code>
   * @return Whether the user field is set.
   */
  @java.lang.Override
  public boolean hasUser() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>.ReqUser user = 5;</code>
   * @return The user.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqUser getUser() {
    return user_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqUser.getDefaultInstance() : user_;
  }
  /**
   * <code>.ReqUser user = 5;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqUserOrBuilder getUserOrBuilder() {
    return user_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqUser.getDefaultInstance() : user_;
  }

  public static final int APP_FIELD_NUMBER = 6;
  private cn.taken.ad.logic.adv.yaya.dto.ReqApp app_;
  /**
   * <code>.ReqApp app = 6;</code>
   * @return Whether the app field is set.
   */
  @java.lang.Override
  public boolean hasApp() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>.ReqApp app = 6;</code>
   * @return The app.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqApp getApp() {
    return app_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqApp.getDefaultInstance() : app_;
  }
  /**
   * <code>.ReqApp app = 6;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqAppOrBuilder getAppOrBuilder() {
    return app_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqApp.getDefaultInstance() : app_;
  }

  public static final int TEST_FIELD_NUMBER = 7;
  private int test_ = 0;
  /**
   * <code>int32 test = 7;</code>
   * @return The test.
   */
  @java.lang.Override
  public int getTest() {
    return test_;
  }

  public static final int METHOD_FIELD_NUMBER = 8;
  private int method_ = 0;
  /**
   * <code>int32 method = 8;</code>
   * @return The method.
   */
  @java.lang.Override
  public int getMethod() {
    return method_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(2, getImp());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(3, getSite());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(4, getDevice());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(5, getUser());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeMessage(6, getApp());
    }
    if (test_ != 0) {
      output.writeInt32(7, test_);
    }
    if (method_ != 0) {
      output.writeInt32(8, method_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getImp());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getSite());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getDevice());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getUser());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getApp());
    }
    if (test_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, test_);
    }
    if (method_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, method_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.taken.ad.logic.adv.yaya.dto.BidRequest4)) {
      return super.equals(obj);
    }
    cn.taken.ad.logic.adv.yaya.dto.BidRequest4 other = (cn.taken.ad.logic.adv.yaya.dto.BidRequest4) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (hasImp() != other.hasImp()) return false;
    if (hasImp()) {
      if (!getImp()
          .equals(other.getImp())) return false;
    }
    if (hasSite() != other.hasSite()) return false;
    if (hasSite()) {
      if (!getSite()
          .equals(other.getSite())) return false;
    }
    if (hasDevice() != other.hasDevice()) return false;
    if (hasDevice()) {
      if (!getDevice()
          .equals(other.getDevice())) return false;
    }
    if (hasUser() != other.hasUser()) return false;
    if (hasUser()) {
      if (!getUser()
          .equals(other.getUser())) return false;
    }
    if (hasApp() != other.hasApp()) return false;
    if (hasApp()) {
      if (!getApp()
          .equals(other.getApp())) return false;
    }
    if (getTest()
        != other.getTest()) return false;
    if (getMethod()
        != other.getMethod()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    if (hasImp()) {
      hash = (37 * hash) + IMP_FIELD_NUMBER;
      hash = (53 * hash) + getImp().hashCode();
    }
    if (hasSite()) {
      hash = (37 * hash) + SITE_FIELD_NUMBER;
      hash = (53 * hash) + getSite().hashCode();
    }
    if (hasDevice()) {
      hash = (37 * hash) + DEVICE_FIELD_NUMBER;
      hash = (53 * hash) + getDevice().hashCode();
    }
    if (hasUser()) {
      hash = (37 * hash) + USER_FIELD_NUMBER;
      hash = (53 * hash) + getUser().hashCode();
    }
    if (hasApp()) {
      hash = (37 * hash) + APP_FIELD_NUMBER;
      hash = (53 * hash) + getApp().hashCode();
    }
    hash = (37 * hash) + TEST_FIELD_NUMBER;
    hash = (53 * hash) + getTest();
    hash = (37 * hash) + METHOD_FIELD_NUMBER;
    hash = (53 * hash) + getMethod();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.taken.ad.logic.adv.yaya.dto.BidRequest4 prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code BidRequest4}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:BidRequest4)
      cn.taken.ad.logic.adv.yaya.dto.BidRequest4OrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_BidRequest4_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_BidRequest4_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.adv.yaya.dto.BidRequest4.class, cn.taken.ad.logic.adv.yaya.dto.BidRequest4.Builder.class);
    }

    // Construct using cn.taken.ad.logic.adv.yaya.dto.BidRequest4.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getImpFieldBuilder();
        getSiteFieldBuilder();
        getDeviceFieldBuilder();
        getUserFieldBuilder();
        getAppFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      imp_ = null;
      if (impBuilder_ != null) {
        impBuilder_.dispose();
        impBuilder_ = null;
      }
      site_ = null;
      if (siteBuilder_ != null) {
        siteBuilder_.dispose();
        siteBuilder_ = null;
      }
      device_ = null;
      if (deviceBuilder_ != null) {
        deviceBuilder_.dispose();
        deviceBuilder_ = null;
      }
      user_ = null;
      if (userBuilder_ != null) {
        userBuilder_.dispose();
        userBuilder_ = null;
      }
      app_ = null;
      if (appBuilder_ != null) {
        appBuilder_.dispose();
        appBuilder_ = null;
      }
      test_ = 0;
      method_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_BidRequest4_descriptor;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.BidRequest4 getDefaultInstanceForType() {
      return cn.taken.ad.logic.adv.yaya.dto.BidRequest4.getDefaultInstance();
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.BidRequest4 build() {
      cn.taken.ad.logic.adv.yaya.dto.BidRequest4 result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.BidRequest4 buildPartial() {
      cn.taken.ad.logic.adv.yaya.dto.BidRequest4 result = new cn.taken.ad.logic.adv.yaya.dto.BidRequest4(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(cn.taken.ad.logic.adv.yaya.dto.BidRequest4 result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.imp_ = impBuilder_ == null
            ? imp_
            : impBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.site_ = siteBuilder_ == null
            ? site_
            : siteBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.device_ = deviceBuilder_ == null
            ? device_
            : deviceBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.user_ = userBuilder_ == null
            ? user_
            : userBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.app_ = appBuilder_ == null
            ? app_
            : appBuilder_.build();
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.test_ = test_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.method_ = method_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.taken.ad.logic.adv.yaya.dto.BidRequest4) {
        return mergeFrom((cn.taken.ad.logic.adv.yaya.dto.BidRequest4)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.taken.ad.logic.adv.yaya.dto.BidRequest4 other) {
      if (other == cn.taken.ad.logic.adv.yaya.dto.BidRequest4.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasImp()) {
        mergeImp(other.getImp());
      }
      if (other.hasSite()) {
        mergeSite(other.getSite());
      }
      if (other.hasDevice()) {
        mergeDevice(other.getDevice());
      }
      if (other.hasUser()) {
        mergeUser(other.getUser());
      }
      if (other.hasApp()) {
        mergeApp(other.getApp());
      }
      if (other.getTest() != 0) {
        setTest(other.getTest());
      }
      if (other.getMethod() != 0) {
        setMethod(other.getMethod());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getImpFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  getSiteFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  getDeviceFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              input.readMessage(
                  getUserFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              input.readMessage(
                  getAppFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 56: {
              test_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              method_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private cn.taken.ad.logic.adv.yaya.dto.ReqImp imp_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqImp, cn.taken.ad.logic.adv.yaya.dto.ReqImp.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqImpOrBuilder> impBuilder_;
    /**
     * <code>.ReqImp imp = 2;</code>
     * @return Whether the imp field is set.
     */
    public boolean hasImp() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.ReqImp imp = 2;</code>
     * @return The imp.
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqImp getImp() {
      if (impBuilder_ == null) {
        return imp_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqImp.getDefaultInstance() : imp_;
      } else {
        return impBuilder_.getMessage();
      }
    }
    /**
     * <code>.ReqImp imp = 2;</code>
     */
    public Builder setImp(cn.taken.ad.logic.adv.yaya.dto.ReqImp value) {
      if (impBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        imp_ = value;
      } else {
        impBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqImp imp = 2;</code>
     */
    public Builder setImp(
        cn.taken.ad.logic.adv.yaya.dto.ReqImp.Builder builderForValue) {
      if (impBuilder_ == null) {
        imp_ = builderForValue.build();
      } else {
        impBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqImp imp = 2;</code>
     */
    public Builder mergeImp(cn.taken.ad.logic.adv.yaya.dto.ReqImp value) {
      if (impBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          imp_ != null &&
          imp_ != cn.taken.ad.logic.adv.yaya.dto.ReqImp.getDefaultInstance()) {
          getImpBuilder().mergeFrom(value);
        } else {
          imp_ = value;
        }
      } else {
        impBuilder_.mergeFrom(value);
      }
      if (imp_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.ReqImp imp = 2;</code>
     */
    public Builder clearImp() {
      bitField0_ = (bitField0_ & ~0x00000002);
      imp_ = null;
      if (impBuilder_ != null) {
        impBuilder_.dispose();
        impBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.ReqImp imp = 2;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqImp.Builder getImpBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return getImpFieldBuilder().getBuilder();
    }
    /**
     * <code>.ReqImp imp = 2;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqImpOrBuilder getImpOrBuilder() {
      if (impBuilder_ != null) {
        return impBuilder_.getMessageOrBuilder();
      } else {
        return imp_ == null ?
            cn.taken.ad.logic.adv.yaya.dto.ReqImp.getDefaultInstance() : imp_;
      }
    }
    /**
     * <code>.ReqImp imp = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqImp, cn.taken.ad.logic.adv.yaya.dto.ReqImp.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqImpOrBuilder> 
        getImpFieldBuilder() {
      if (impBuilder_ == null) {
        impBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.ReqImp, cn.taken.ad.logic.adv.yaya.dto.ReqImp.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqImpOrBuilder>(
                getImp(),
                getParentForChildren(),
                isClean());
        imp_ = null;
      }
      return impBuilder_;
    }

    private cn.taken.ad.logic.adv.yaya.dto.ReqSite site_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqSite, cn.taken.ad.logic.adv.yaya.dto.ReqSite.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqSiteOrBuilder> siteBuilder_;
    /**
     * <code>.ReqSite site = 3;</code>
     * @return Whether the site field is set.
     */
    public boolean hasSite() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.ReqSite site = 3;</code>
     * @return The site.
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqSite getSite() {
      if (siteBuilder_ == null) {
        return site_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqSite.getDefaultInstance() : site_;
      } else {
        return siteBuilder_.getMessage();
      }
    }
    /**
     * <code>.ReqSite site = 3;</code>
     */
    public Builder setSite(cn.taken.ad.logic.adv.yaya.dto.ReqSite value) {
      if (siteBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        site_ = value;
      } else {
        siteBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqSite site = 3;</code>
     */
    public Builder setSite(
        cn.taken.ad.logic.adv.yaya.dto.ReqSite.Builder builderForValue) {
      if (siteBuilder_ == null) {
        site_ = builderForValue.build();
      } else {
        siteBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqSite site = 3;</code>
     */
    public Builder mergeSite(cn.taken.ad.logic.adv.yaya.dto.ReqSite value) {
      if (siteBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          site_ != null &&
          site_ != cn.taken.ad.logic.adv.yaya.dto.ReqSite.getDefaultInstance()) {
          getSiteBuilder().mergeFrom(value);
        } else {
          site_ = value;
        }
      } else {
        siteBuilder_.mergeFrom(value);
      }
      if (site_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.ReqSite site = 3;</code>
     */
    public Builder clearSite() {
      bitField0_ = (bitField0_ & ~0x00000004);
      site_ = null;
      if (siteBuilder_ != null) {
        siteBuilder_.dispose();
        siteBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.ReqSite site = 3;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqSite.Builder getSiteBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return getSiteFieldBuilder().getBuilder();
    }
    /**
     * <code>.ReqSite site = 3;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqSiteOrBuilder getSiteOrBuilder() {
      if (siteBuilder_ != null) {
        return siteBuilder_.getMessageOrBuilder();
      } else {
        return site_ == null ?
            cn.taken.ad.logic.adv.yaya.dto.ReqSite.getDefaultInstance() : site_;
      }
    }
    /**
     * <code>.ReqSite site = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqSite, cn.taken.ad.logic.adv.yaya.dto.ReqSite.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqSiteOrBuilder> 
        getSiteFieldBuilder() {
      if (siteBuilder_ == null) {
        siteBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.ReqSite, cn.taken.ad.logic.adv.yaya.dto.ReqSite.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqSiteOrBuilder>(
                getSite(),
                getParentForChildren(),
                isClean());
        site_ = null;
      }
      return siteBuilder_;
    }

    private cn.taken.ad.logic.adv.yaya.dto.ReqDevice device_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqDevice, cn.taken.ad.logic.adv.yaya.dto.ReqDevice.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqDeviceOrBuilder> deviceBuilder_;
    /**
     * <code>.ReqDevice device = 4;</code>
     * @return Whether the device field is set.
     */
    public boolean hasDevice() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>.ReqDevice device = 4;</code>
     * @return The device.
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqDevice getDevice() {
      if (deviceBuilder_ == null) {
        return device_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqDevice.getDefaultInstance() : device_;
      } else {
        return deviceBuilder_.getMessage();
      }
    }
    /**
     * <code>.ReqDevice device = 4;</code>
     */
    public Builder setDevice(cn.taken.ad.logic.adv.yaya.dto.ReqDevice value) {
      if (deviceBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        device_ = value;
      } else {
        deviceBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqDevice device = 4;</code>
     */
    public Builder setDevice(
        cn.taken.ad.logic.adv.yaya.dto.ReqDevice.Builder builderForValue) {
      if (deviceBuilder_ == null) {
        device_ = builderForValue.build();
      } else {
        deviceBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqDevice device = 4;</code>
     */
    public Builder mergeDevice(cn.taken.ad.logic.adv.yaya.dto.ReqDevice value) {
      if (deviceBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          device_ != null &&
          device_ != cn.taken.ad.logic.adv.yaya.dto.ReqDevice.getDefaultInstance()) {
          getDeviceBuilder().mergeFrom(value);
        } else {
          device_ = value;
        }
      } else {
        deviceBuilder_.mergeFrom(value);
      }
      if (device_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.ReqDevice device = 4;</code>
     */
    public Builder clearDevice() {
      bitField0_ = (bitField0_ & ~0x00000008);
      device_ = null;
      if (deviceBuilder_ != null) {
        deviceBuilder_.dispose();
        deviceBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.ReqDevice device = 4;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqDevice.Builder getDeviceBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return getDeviceFieldBuilder().getBuilder();
    }
    /**
     * <code>.ReqDevice device = 4;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqDeviceOrBuilder getDeviceOrBuilder() {
      if (deviceBuilder_ != null) {
        return deviceBuilder_.getMessageOrBuilder();
      } else {
        return device_ == null ?
            cn.taken.ad.logic.adv.yaya.dto.ReqDevice.getDefaultInstance() : device_;
      }
    }
    /**
     * <code>.ReqDevice device = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqDevice, cn.taken.ad.logic.adv.yaya.dto.ReqDevice.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqDeviceOrBuilder> 
        getDeviceFieldBuilder() {
      if (deviceBuilder_ == null) {
        deviceBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.ReqDevice, cn.taken.ad.logic.adv.yaya.dto.ReqDevice.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqDeviceOrBuilder>(
                getDevice(),
                getParentForChildren(),
                isClean());
        device_ = null;
      }
      return deviceBuilder_;
    }

    private cn.taken.ad.logic.adv.yaya.dto.ReqUser user_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqUser, cn.taken.ad.logic.adv.yaya.dto.ReqUser.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqUserOrBuilder> userBuilder_;
    /**
     * <code>.ReqUser user = 5;</code>
     * @return Whether the user field is set.
     */
    public boolean hasUser() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>.ReqUser user = 5;</code>
     * @return The user.
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqUser getUser() {
      if (userBuilder_ == null) {
        return user_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqUser.getDefaultInstance() : user_;
      } else {
        return userBuilder_.getMessage();
      }
    }
    /**
     * <code>.ReqUser user = 5;</code>
     */
    public Builder setUser(cn.taken.ad.logic.adv.yaya.dto.ReqUser value) {
      if (userBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        user_ = value;
      } else {
        userBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqUser user = 5;</code>
     */
    public Builder setUser(
        cn.taken.ad.logic.adv.yaya.dto.ReqUser.Builder builderForValue) {
      if (userBuilder_ == null) {
        user_ = builderForValue.build();
      } else {
        userBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqUser user = 5;</code>
     */
    public Builder mergeUser(cn.taken.ad.logic.adv.yaya.dto.ReqUser value) {
      if (userBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0) &&
          user_ != null &&
          user_ != cn.taken.ad.logic.adv.yaya.dto.ReqUser.getDefaultInstance()) {
          getUserBuilder().mergeFrom(value);
        } else {
          user_ = value;
        }
      } else {
        userBuilder_.mergeFrom(value);
      }
      if (user_ != null) {
        bitField0_ |= 0x00000010;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.ReqUser user = 5;</code>
     */
    public Builder clearUser() {
      bitField0_ = (bitField0_ & ~0x00000010);
      user_ = null;
      if (userBuilder_ != null) {
        userBuilder_.dispose();
        userBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.ReqUser user = 5;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqUser.Builder getUserBuilder() {
      bitField0_ |= 0x00000010;
      onChanged();
      return getUserFieldBuilder().getBuilder();
    }
    /**
     * <code>.ReqUser user = 5;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqUserOrBuilder getUserOrBuilder() {
      if (userBuilder_ != null) {
        return userBuilder_.getMessageOrBuilder();
      } else {
        return user_ == null ?
            cn.taken.ad.logic.adv.yaya.dto.ReqUser.getDefaultInstance() : user_;
      }
    }
    /**
     * <code>.ReqUser user = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqUser, cn.taken.ad.logic.adv.yaya.dto.ReqUser.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqUserOrBuilder> 
        getUserFieldBuilder() {
      if (userBuilder_ == null) {
        userBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.ReqUser, cn.taken.ad.logic.adv.yaya.dto.ReqUser.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqUserOrBuilder>(
                getUser(),
                getParentForChildren(),
                isClean());
        user_ = null;
      }
      return userBuilder_;
    }

    private cn.taken.ad.logic.adv.yaya.dto.ReqApp app_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqApp, cn.taken.ad.logic.adv.yaya.dto.ReqApp.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqAppOrBuilder> appBuilder_;
    /**
     * <code>.ReqApp app = 6;</code>
     * @return Whether the app field is set.
     */
    public boolean hasApp() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>.ReqApp app = 6;</code>
     * @return The app.
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqApp getApp() {
      if (appBuilder_ == null) {
        return app_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqApp.getDefaultInstance() : app_;
      } else {
        return appBuilder_.getMessage();
      }
    }
    /**
     * <code>.ReqApp app = 6;</code>
     */
    public Builder setApp(cn.taken.ad.logic.adv.yaya.dto.ReqApp value) {
      if (appBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        app_ = value;
      } else {
        appBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqApp app = 6;</code>
     */
    public Builder setApp(
        cn.taken.ad.logic.adv.yaya.dto.ReqApp.Builder builderForValue) {
      if (appBuilder_ == null) {
        app_ = builderForValue.build();
      } else {
        appBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqApp app = 6;</code>
     */
    public Builder mergeApp(cn.taken.ad.logic.adv.yaya.dto.ReqApp value) {
      if (appBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0) &&
          app_ != null &&
          app_ != cn.taken.ad.logic.adv.yaya.dto.ReqApp.getDefaultInstance()) {
          getAppBuilder().mergeFrom(value);
        } else {
          app_ = value;
        }
      } else {
        appBuilder_.mergeFrom(value);
      }
      if (app_ != null) {
        bitField0_ |= 0x00000020;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.ReqApp app = 6;</code>
     */
    public Builder clearApp() {
      bitField0_ = (bitField0_ & ~0x00000020);
      app_ = null;
      if (appBuilder_ != null) {
        appBuilder_.dispose();
        appBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.ReqApp app = 6;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqApp.Builder getAppBuilder() {
      bitField0_ |= 0x00000020;
      onChanged();
      return getAppFieldBuilder().getBuilder();
    }
    /**
     * <code>.ReqApp app = 6;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqAppOrBuilder getAppOrBuilder() {
      if (appBuilder_ != null) {
        return appBuilder_.getMessageOrBuilder();
      } else {
        return app_ == null ?
            cn.taken.ad.logic.adv.yaya.dto.ReqApp.getDefaultInstance() : app_;
      }
    }
    /**
     * <code>.ReqApp app = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqApp, cn.taken.ad.logic.adv.yaya.dto.ReqApp.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqAppOrBuilder> 
        getAppFieldBuilder() {
      if (appBuilder_ == null) {
        appBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.ReqApp, cn.taken.ad.logic.adv.yaya.dto.ReqApp.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqAppOrBuilder>(
                getApp(),
                getParentForChildren(),
                isClean());
        app_ = null;
      }
      return appBuilder_;
    }

    private int test_ ;
    /**
     * <code>int32 test = 7;</code>
     * @return The test.
     */
    @java.lang.Override
    public int getTest() {
      return test_;
    }
    /**
     * <code>int32 test = 7;</code>
     * @param value The test to set.
     * @return This builder for chaining.
     */
    public Builder setTest(int value) {

      test_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>int32 test = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearTest() {
      bitField0_ = (bitField0_ & ~0x00000040);
      test_ = 0;
      onChanged();
      return this;
    }

    private int method_ ;
    /**
     * <code>int32 method = 8;</code>
     * @return The method.
     */
    @java.lang.Override
    public int getMethod() {
      return method_;
    }
    /**
     * <code>int32 method = 8;</code>
     * @param value The method to set.
     * @return This builder for chaining.
     */
    public Builder setMethod(int value) {

      method_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>int32 method = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearMethod() {
      bitField0_ = (bitField0_ & ~0x00000080);
      method_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:BidRequest4)
  }

  // @@protoc_insertion_point(class_scope:BidRequest4)
  private static final cn.taken.ad.logic.adv.yaya.dto.BidRequest4 DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.yaya.dto.BidRequest4();
  }

  public static cn.taken.ad.logic.adv.yaya.dto.BidRequest4 getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BidRequest4>
      PARSER = new com.google.protobuf.AbstractParser<BidRequest4>() {
    @java.lang.Override
    public BidRequest4 parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BidRequest4> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BidRequest4> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.BidRequest4 getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

