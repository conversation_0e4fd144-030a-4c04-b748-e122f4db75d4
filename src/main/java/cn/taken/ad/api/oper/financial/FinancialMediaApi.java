package cn.taken.ad.api.oper.financial;

import cn.taken.ad.component.excel.common.ExcelVersion;
import cn.taken.ad.component.excel.read.ExcelReader;
import cn.taken.ad.component.excel.utils.ExcelUtils;
import cn.taken.ad.component.excel.write.writer.impl.FieldLimitSheetWriter;
import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.BidType;
import cn.taken.ad.constant.business.FinancialReleaseState;
import cn.taken.ad.constant.web.WebAuth;
import cn.taken.ad.core.dto.web.oper.financial.media.*;
import cn.taken.ad.core.dto.web.oper.media.tag.MediaTagInfo;
import cn.taken.ad.core.dto.web.oper.statistics.media.StatisticsMediaRequestInfo;
import cn.taken.ad.core.pojo.financial.FinancialMedia;
import cn.taken.ad.core.service.financial.FinancialMediaService;
import cn.taken.ad.core.service.media.MediaTagService;
import cn.taken.ad.core.service.system.OperLogService;
import cn.taken.ad.utils.web.OperWebUtils;
import cn.taken.ad.utils.web.WebExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/o/financial/media")
public class FinancialMediaApi {

    @Resource
    private FinancialMediaService mediaFinancialReportService;
    @Resource
    private MediaTagService mediaTagService;
    @Resource
    private OperLogService operLogService;

    private Logger log = LoggerFactory.getLogger(this.getClass());

    @WebAuth("FINANCIAL_MEDIA_VIEW")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public SuperResult<Page<FinancialMediaInfo>> page(@RequestBody FinancialMediaPageReq req) {
        Page<FinancialMediaInfo> page = mediaFinancialReportService.findPage(req);
        operLogService.saveOperLog("媒体账务", "查询请求", OperWebUtils.getWebToken().getUserId());
        return SuperResult.rightResult(page);
    }

    @WebAuth("FINANCIAL_MEDIA_VIEW")
    @RequestMapping(value = "/info", method = RequestMethod.POST)
    public SuperResult<FinancialMediaInfo> info(@RequestBody FinancialMediaInfoReq req) {
        FinancialMediaInfo info = mediaFinancialReportService.findInfo(req);
        if (null == info) {
            return SuperResult.badResult("数据错误");
        }
        operLogService.saveOperLog("媒体账务", "查询详情", OperWebUtils.getWebToken().getUserId());
        return SuperResult.rightResult(info);
    }

    @WebAuth("FINANCIAL_MEDIA_MODIFY")
    @RequestMapping(value = "/modify", method = RequestMethod.POST)
    public SuperResult<String> modify(@RequestBody FinancialMediaModifyReq req) {
        SuperResult<String> result = mediaFinancialReportService.modify(req);
        if (result.getSuccess()) {
            operLogService.saveOperLog("媒体账务", "修改账务", OperWebUtils.getWebToken().getUserId());
        }
        return result;
    }

    @WebAuth("FINANCIAL_MEDIA_MODIFY")
    @RequestMapping(value = "/releaseFinancial", method = RequestMethod.POST)
    public SuperResult<String> releaseFinancial(@RequestBody FinancialMediaReleaseReq req) {
        if (null == FinancialReleaseState.findByType(req.getReleaseState())) {
            return SuperResult.badResult("发布状态错误");
        }
        SuperResult<String> result = mediaFinancialReportService.modifyRelease(req);
        if (result.getSuccess()) {
            operLogService.saveOperLog("媒体账务", "发布账务", OperWebUtils.getWebToken().getUserId());
        }
        return result;
    }

    @WebAuth("FINANCIAL_MEDIA_EXPORT")
    @RequestMapping(value = "/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE, method = RequestMethod.GET)
    public void export(@NotNull(message = "起始时间未选择") String beginTime, @NotNull(message = "结束时间未选择") String endTime, Long mediaId, Long mediaAppId, Long mediaTagId, Integer state, Integer releaseState, Integer bidType, String exportFields) throws IOException {
        List<FinancialMediaInfo> list = mediaFinancialReportService.findExport(beginTime, endTime, mediaId, mediaAppId, mediaTagId, state, releaseState, bidType);
       /* if (CollectionUtils.isEmpty(list)) {
            WebExcelUtils.exportExcel(ExcelVersion.XLSX, Collections.singletonList(new FinancialMediaInfo()), "媒体账务");
            return;
        }*/
        WebExcelUtils.exportExcel("mediaFinancial", ExcelVersion.XLSX, new FieldLimitSheetWriter<FinancialMediaInfo>(list, exportFields.split(","), "媒体账务"));
        operLogService.saveOperLog("媒体账务", "导出", OperWebUtils.getWebToken().getUserId());
    }

    @WebAuth("FINANCIAL_MEDIA_IMPORT")
    @RequestMapping(value = "/import", headers = "content-type=multipart/form-data")
    public SuperResult<String> modify(MultipartFile file) {
        if (null == file || file.isEmpty()) {
            return SuperResult.badResult("上传文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            return SuperResult.badResult("文件名称不能为空");
        }
        try {
            ExcelVersion version = ExcelUtils.parserByName(fileName);
            List<FinancialMediaImport> list = ExcelReader.readFirstSheet(file.getInputStream(), version, FinancialMediaImport.class);
            if (list.isEmpty()) {
                return SuperResult.badResult("文件无数据");
            }
            SuperResult<List<FinancialMedia>> result = checkData(list);
            if (!result.getSuccess()) {
                return SuperResult.badResult(result.getMessage());
            }
            mediaFinancialReportService.updateBatchForImport(result.getResult());
            operLogService.saveOperLog("媒体账务", "导入", OperWebUtils.getWebToken().getUserId());
        } catch (Exception e) {
            log.info("Error", e);
            return SuperResult.badResult("文件解析失败");
        }
        return SuperResult.rightResult();
    }

    @WebAuth("FINANCIAL_MEDIA_MODIFY")
    @RequestMapping(value = "/updateRatio", method = RequestMethod.POST)
    public SuperResult<String> batchUpdateRatio(@RequestBody FinanceMediaUpdateRatioDto dto) {
        this.mediaFinancialReportService.updateRatio(Arrays.asList(dto.getIds()), dto.getRatio());
        return SuperResult.rightResult();
    }

    @WebAuth("FINANCIAL_MEDIA_MODIFY")
    @RequestMapping(value = "/batchPublish", method = RequestMethod.POST)
    public SuperResult<String> batchPublish(@RequestBody Map<String, Long[]> map) {
        Long[] ids = map.get("ids");
        if (ids == null || ids.length == 0) {
            return SuperResult.badResult("id不能为空");
        }
        this.mediaFinancialReportService.updateReleaseState(Arrays.asList(ids), 1);
        return SuperResult.rightResult();
    }

    private SuperResult<List<FinancialMedia>> checkData(List<FinancialMediaImport> list) {
        Set<String> hasKey = new HashSet<>(list.size() * 2);
        List<FinancialMedia> rights = new ArrayList<>(list.size() * 2);
        for (int i = 0; i < list.size(); i++) {
            FinancialMediaImport financialMedia = list.get(i);
            if (StringUtils.isBlank(financialMedia.getMediaCode())) {
                return SuperResult.badResult("数据错误，媒体CODE不能为空，第" + (i + 2) + "行");
            }
            financialMedia.setMediaCode(financialMedia.getMediaCode().trim());
            if (StringUtils.isBlank(financialMedia.getMediaAppCode())) {
                return SuperResult.badResult("数据错误，APPCODE不能为空，第" + (i + 2) + "行");
            }
            financialMedia.setMediaAppCode(financialMedia.getMediaAppCode().trim());
            if (StringUtils.isBlank(financialMedia.getMediaTagCode())) {
                return SuperResult.badResult("数据错误，广告位CODE不能为空，第" + (i + 2) + "行");
            }
            financialMedia.setMediaTagCode(financialMedia.getMediaTagCode().trim());
            if (null == financialMedia.getRealParticipatingTotal()) {
                financialMedia.setRealParticipatingTotal(0L);
            }
            if (null == financialMedia.getRealExposureTotal()) {
                financialMedia.setRealExposureTotal(0L);
            }
            if (null == financialMedia.getRealClickTotal()) {
                financialMedia.setRealClickTotal(0L);
            }
            if (!hasKey.add(financialMedia.getReportTime() + "&&" + financialMedia.getMediaCode() + "&&" + financialMedia.getMediaAppCode() + "&&" + financialMedia.getMediaTagCode())) {
                return SuperResult.badResult("数据重复，第" + (i + 2) + "行");
            }
        }
        List<MediaTagInfo> tagInfos = mediaTagService.findByMediaCodeAppCodeTagCode(list);
        if (CollectionUtils.isEmpty(tagInfos)) {
            return SuperResult.badResult("数据错误,无匹配记录");
        }
        // info 转map
        Map<String, MediaTagInfo> infoMap = tagInfos.stream().collect(Collectors.toMap(k -> k.getMediaCode() + "&&" + k.getMediaAppCode() + "&&" + k.getCode(), v -> v, (o1, o2) -> o2));
        for (int i = 0; i < list.size(); i++) {
            FinancialMediaImport mediaImport = list.get(i);
            String key = mediaImport.getMediaCode() + "&&" + mediaImport.getMediaAppCode() + "&&" + mediaImport.getMediaTagCode();
            MediaTagInfo tagInfo = infoMap.get(key);
            if (null == tagInfo) {
                return SuperResult.badResult("数据错误,无匹配记录，第" + (i + 2) + "行");
            }
            if (tagInfo.getBidType() == BidType.BID.getType() && null == mediaImport.getRealAmount()) {
                mediaImport.setRealAmount(BigDecimal.ZERO);
            }
            FinancialMedia financial = getFinancialMedia(mediaImport, tagInfo);
            rights.add(financial);
        }
        return SuperResult.rightResult(rights);
    }

    private FinancialMedia getFinancialMedia(FinancialMediaImport mediaImport, MediaTagInfo tagInfo) {
        FinancialMedia financialMedia = new FinancialMedia();
        // 基础信息
        financialMedia.setReportTime(mediaImport.getReportTime().trim());
        financialMedia.setMediaId(tagInfo.getMediaId());
        financialMedia.setMediaAppId(tagInfo.getMediaAppId());
        financialMedia.setMediaTagId(tagInfo.getId());
        // 数据信息
        financialMedia.setRealParticipatingTotal(mediaImport.getRealParticipatingTotal());
        financialMedia.setRealExposureTotal(mediaImport.getRealExposureTotal());
        financialMedia.setRealClickTotal(mediaImport.getRealClickTotal());
        financialMedia.setRealAmount(mediaImport.getRealAmount());
        return financialMedia;
    }
}
