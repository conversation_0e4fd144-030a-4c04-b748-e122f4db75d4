<template>
  <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" size="mini" class="ruleForm">
    <el-form-item label="所属媒体" prop="name"> {{ ruleForm.mediaName }} - {{ ruleForm.mediaCode }} </el-form-item>
    <el-form-item label="所属APP" prop="name">
      {{ ruleForm.mediaAppName }} - {{ ruleForm.mediaAppCode }} - {{ parseOsType(ruleForm.mediaAppType) }}
    </el-form-item>
    <el-form-item label="类型" prop="type">
      <el-select v-model="ruleForm.type" filterable clearable placeholder="请选择APP" style="width: 100%" @change="getRealName">
        <el-option v-for="(tt, x) in types" :key="'tagt-' + x" :label="tt.name" :value="tt.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="描述" prop="name">
      <el-input v-model="ruleForm.name" placeholder="建议填写预算名称,如：启航" />
    </el-form-item>
    <el-form-item label="名称">
      {{ realTagName }}
    </el-form-item>
    <el-form-item label="CODE" prop="code">
      <el-input v-if="ruleForm.protocolType === 2" v-model="ruleForm.code">
        <el-button slot="append" icon="el-icon-refresh" @click="handleAppCode" />
      </el-input>
      <span v-else>{{ ruleForm.code }}</span>
    </el-form-item>
    <el-form-item label="流量类型" prop="flowType">
      <el-radio-group v-model="ruleForm.flowType">
        <el-radio v-for="item in flowTypeList" :key="item.id" :label="item.id">{{ item.name }} </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="宽度" prop="width">
      <el-input v-model="ruleForm.width">
        <template slot="append">像素</template>
      </el-input>
    </el-form-item>
    <el-form-item label="高度" prop="height">
      <el-input v-model="ruleForm.height">
        <template slot="append">像素</template>
      </el-input>
    </el-form-item>
    <el-form-item label="超时时间" prop="timeout">
      <el-input v-model="ruleForm.timeout">
        <template slot="append">毫秒</template>
      </el-input>
    </el-form-item>
    <el-form-item label="结算方式" prop="bidType">
      <span>{{ parseBidType(ruleForm.bidType) }}</span>
    </el-form-item>
    <el-form-item v-if="ruleForm.bidType === 2" label="分成结算比例" prop="settlementRatio">
      <el-input v-model="ruleForm.settlementRatio">
        <template slot="append">%</template>
      </el-input>
    </el-form-item>
    <el-form-item label="HTTPS上报" prop="needHttps">
      <el-radio v-model="ruleForm.needHttps" :label="0">否</el-radio>
      <el-radio v-model="ruleForm.needHttps" :label="1">是</el-radio>
    </el-form-item>
    <el-form-item label="HTTPS素材" prop="materialHttps">
      <el-radio v-model="ruleForm.materialHttps" :label="false">否</el-radio>
      <el-radio v-model="ruleForm.materialHttps" :label="true">是</el-radio>
    </el-form-item>
    <el-form-item label="开启过滤" prop="filterOnOff">
      <el-switch v-model="ruleForm.filterOnOff" />
    </el-form-item>
    <el-form-item v-if="ruleForm.filterOnOff" label="链接域名">
      <el-input
        v-model="ruleForm.filterUrlDomain"
        type="textarea"
        :autosize="{ minRows: 1, maxRows: 5 }"
        placeholder="多个英文逗号分割"
      />
    </el-form-item>
    <el-form-item v-if="ruleForm.filterOnOff" label="过滤素材比例">
      <el-radio v-model="ruleForm.filterMaterialRatio" :label="0">不过滤</el-radio>
      <el-radio v-model="ruleForm.filterMaterialRatio" :label="1">过滤</el-radio>
    </el-form-item>
    <el-form-item v-if="ruleForm.filterOnOff" label="过滤素材类型">
      <el-radio v-model="ruleForm.filterMaterialType" :label="0">不过滤</el-radio>
      <el-radio v-model="ruleForm.filterMaterialType" :label="1">过滤</el-radio>
    </el-form-item>
    <el-form-item v-if="ruleForm.filterOnOff" label="过滤操作类型">
      <el-radio v-model="ruleForm.filterActionType" :label="0">不过滤</el-radio>
      <el-radio v-model="ruleForm.filterActionType" :label="1">过滤</el-radio>
    </el-form-item>
    <el-form-item
      v-show="ruleForm.mediaId && ruleForm.protocolTagParam && ruleForm.protocolTagParam !== '' && ruleForm.protocolTagParam !== '[]'"
      label="扩展参数"
      prop="pnyParam"
    >
      <ParameterValueForm ref="pnyParam" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
      <el-button @click="createMM">取消</el-button>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { infoMediaTag, modifyMediaTag } from '@/api/media/mediaTag'
import { infoMediaApp } from '@/api/media/mediaApp'
import { listTagTypeApi, listOsTypeApi, listFlowTypeApi, listBidTypeApi } from '@/api/public/typeinfo'
import UUID from '@/utils/Uuid'
import ParameterValueForm from '@/components/ParameterValueForm'
export default {
  name: 'MediaTagModify',
  components: { ParameterValueForm },
  props: {
    isUpdate: {
      required: true,
      type: Boolean
    },
    id: {
      required: true,
      type: Number
    }
  },
  data() {
    return {
      filterMaterialTypeList: [{ id: 1, name: '图片' }, { id: 2, name: '视频' }],
      btnClicked: false,
      ruleForm: {
        name: null,
        code: null,
        mediaId: null,
        mediaAppId: null,
        type: null,
        width: null,
        height: null,
        bidType: 1,
        pnyParam: null,
        timeout: null,
        needHttps: 0,
        materialHttps: false,
        flowType: 1,
        mediaName: null,
        mediaCode: null,
        appName: null,
        appCode: null,
        typeName: null,
        protocolName: null,
        protocolCode: null,
        protocolTagParam: null,
        protocolType: null,
        settlementRatio: null,
        filterOnOff: false,
        filterUrlDomain: null,
        filterMaterialType: 0,
        filterActionType: 0,
        filterMaterialRatio: 0
      },
      osTypeList: [],
      flowTypeList: [],
      bidTypeList: [],
      rules: {
        name: [{ required: true, trigger: 'blur', message: '不能为空' }],
        code: [{ required: true, trigger: 'blur', message: '不能为空' }],
        mediaId: [{ required: true, trigger: 'blur', message: '不能为空' }],
        appId: [{ required: true, trigger: 'blur', message: '不能为空' }],
        type: [{ required: true, trigger: 'blur', message: '不能为空' }],
        bidType: [{ required: true, trigger: 'blur', message: '不能为空' }],
        width: [{ required: true, trigger: 'blur', message: '不能为空' }],
        height: [{ required: true, trigger: 'blur', message: '不能为空' }],
        flowType: [{ required: true, trigger: 'blur', message: '不能为空' }],
        needHttps: [{ required: true, trigger: 'blur', message: '不能为空' }],
        materialHttps: [{ required: true, trigger: 'blur', message: '不能为空' }],
        timeout: [{ required: true, trigger: 'blur', message: '不能为空' }],
        filterOnOff: [{ required: true, trigger: 'change', message: '未选择' }]
      },
      types: [],
      pnyParams: [],
      lazy: false,
      tagNamArr: [],
      realTagName: ''
    }
  },
  computed: {},
  watch: {
    'ruleForm.name': {
      handler(v) {
        this.getRealName()
      },
      immediate: true
    }
  },
  created() {
    listFlowTypeApi().then(response => {
      this.flowTypeList = response.result
    })
    listBidTypeApi().then(response => {
      this.bidTypeList = response.result
    })
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    listTagTypeApi({}).then(response => {
      this.types = response.result
    })
    this.loadData()
  },
  methods: {
    parseBidType(id) {
      const value = this.bidTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseTagType(id) {
      const value = this.types.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    async loadData() {
      const res = await infoMediaTag({ id: this.id })
      const app = await infoMediaApp({ id: res.result.mediaAppId })
      this.tagNamArr = [
        app.result.name,
        this.parseOsType(app.result.type),
        this.parseTagType(res.result.type),
        '',
        this.parseBidType(res.result.bidType)
      ]
      let name = res.result.name
      res.result.name = null
      name = name.replace(this.tagNamArr[0], '')
      name = name.replace(this.tagNamArr[1], '')
      name = name.replace(this.tagNamArr[2], '')
      name = name.replace(this.tagNamArr[3], '')
      name = name.replace(this.tagNamArr[4], '')
      name = name.replace(/-/g, '')
      this.ruleForm = res.result
      this.ruleForm.name = name
      this.tagNamArr[3] = name
      this.realTagName = this.tagNamArr.join('-')
      this.pnyParams = this.ruleForm.pnyParam ? JSON.parse(this.ruleForm.pnyParam) : []

      const protocolParam = this.ruleForm.protocolTagParam ? JSON.parse(this.ruleForm.protocolTagParam) : []
      const addList = []
      for (const p of protocolParam) {
        let has = false
        for (const mp of this.pnyParams) {
          if (mp.paramsKey === p.paramsKey) {
            has = true
            break
          }
        }
        if (!has) {
          addList.push(p)
        }
      }
      for (const item of addList) {
        this.pnyParams.push({ paramsName: item.paramsName, paramsKey: item.paramsKey, paramsValue: null })
      }

      this.$refs.pnyParam.setParamText(this.pnyParams)
      this.lazy = true
    },
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.$emit('update:isUpdate', false)
      this.closeMe()
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.ruleForm.bidType === 2) {
            if (this.ruleForm.settlementRatio === null || this.ruleForm.settlementRatio === '' || this.ruleForm.settlementRatio <= 0) {
              this.$message({
                message: '分成结算比例不能为空，且必须大于0',
                type: 'warning'
              })
              return false
            }
          }
          this.btnClicked = true
          this.ruleForm.pnyParam = this.$refs.pnyParam.getParamText()
          modifyMediaTag(this.ruleForm)
            .then(response => {
              this.$message({
                message: '修改成功',
                type: 'success'
              })
              this.$emit('update:isUpdate', true)
              this.closeMe()
            })
            .catch(() => {
              this.btnClicked = false
            })
          return true
        } else {
          return false
        }
      })
    },
    handleAppCode() {
      this.ruleForm.code = UUID.gen().replace(/-/g, '').substr(24, 8)
    },
    getRealName() {
      this.tagNamArr[3] = this.ruleForm.name
      this.tagNamArr[2] = this.parseTagType(this.ruleForm.type)
      this.realTagName = this.tagNamArr.join('-')
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
