<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" class="demo-form-inline" size="mini">
        <el-form-item label="系统类型:">
          <el-select v-model="listQuery.systemType">
            <el-option key="oper" label="运营系统" value="OPER" />
            <el-option key="client" label="客户系统" value="CLIENT" />
            <el-option key="cooperate" label="合作系统" value="COOPERATE" />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名:">
          <el-input v-model="listQuery.realname" />
        </el-form-item>
        <el-form-item label="模块:">
          <el-input v-model="listQuery.operModule" />
        </el-form-item>
        <el-form-item label="地址:">
          <el-input v-model="listQuery.operAddress" />
        </el-form-item>
        <el-form-item label="时间:">
          <el-date-picker
            v-model="timeSeclet"
            type="datetimerange"
            align="right"
            unlink-panels
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="info" icon="el-icon-refresh" @click="remove">清除</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      :row-class-name="tableRowClassName"
    >
      <el-table-column align="center" label="序号" width="60" type="index" />
      <el-table-column label="所属系统" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.systemType === 'OPER'">运营系统</span>
          <span v-else-if="scope.row.systemType === 'CLIENT'">客户系统</span>
          <span v-else-if="scope.row.systemType === 'COOPERATE'">合作系统</span>
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.realname }} [{{ scope.row.username }}]</span>
        </template>
      </el-table-column>
      <el-table-column label="地址" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.operIp }} [{{ scope.row.operAddress }}]</span>
        </template>
      </el-table-column>
      <el-table-column label="操作模块" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.operModule }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作内容" align="center">
        <template slot-scope="scope">
          <pre style="text-align: left">{{ scope.row.operContent }}</pre>
        </template>
      </el-table-column>
      <el-table-column label="时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.operTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-if="paginationShow"
      class="pagination"
      :total="total"
      :total-page="totalPage"
      :page="currentPageNum"
      :start.sync="listQuery.start"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listLog } from '@/api/system/log' // 后台接口
import Pagination from '@/components/Pagination' // 分页
export default {
  name: 'Log', // 路由名字
  components: { Pagination }, // 分页模块
  data() {
    return {
      timeSeclet: [],
      paginationShow: true,
      tableKey: 0,
      list: null,
      total: 0,
      totalPage: 0,
      currentPageNum: 0,
      listLoading: false,
      listQuery: {
        start: 0,
        limit: 20,
        realname: null,
        beginOperTime: null,
        endOperTime: null,
        operModule: null,
        operAddress: null,
        systemType: 'OPER'
      }
    }
  },
  created() {
    this.getList()
  },
  activated() {},
  methods: {
    getList() {
      this.list = []
      this.total = 0
      this.totalPage = 0
      this.currentPageNum = 0
      this.paginationShow = false
      this.listLoading = true
      this.listQuery.beginOperTime = this.timeSeclet[0]
      this.listQuery.endOperTime = this.timeSeclet[1]
      listLog(this.listQuery).then(response => {
        this.list = response.result.list
        this.total = response.result.totalCount
        this.totalPage = response.result.totalPage
        this.currentPageNum = response.result.currentPageNum
        this.listLoading = false
        this.paginationShow = true
      })
    },
    remove() {
      this.listQuery.start = 0
      this.listQuery.limit = 20
      this.listQuery.realname = null
      this.listQuery.beginOperTime = null
      this.listQuery.endOperTime = null
      this.listQuery.operModule = null
      this.listQuery.operAddress = null
      this.listQuery.systemType = 'OPER'
      this.timeSeclet = []
      this.getList()
    },
    handleFilter() {
      this.listQuery.start = 0
      this.getList()
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 !== 1) {
        return 'success-row'
      }
      return ''
    }
  }
}
</script>

<style></style>
