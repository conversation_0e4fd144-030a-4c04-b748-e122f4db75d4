import store from '@/store'

// 导出Excel
export function exportExcel(url, queryParams) {
  exportFile(url, queryParams)
}

// 导出文件
export function exportFile(url, queryParams) {
  window.location.href = getExportFileUrl(url, queryParams)
}

// 获取导出文件的url
// 可以用a标签绑定url实现，需要写a标签的download属性
export function getExportFileUrl(url, queryParams) {
  if (queryParams !== null && queryParams !== undefined) {
    const key = Object.keys(queryParams).filter(name => !(queryParams[name] === null || queryParams[name] === undefined))
    queryParams = encodeURI(key.map(name => `${name}=${queryParams[name]}`).join('&'))
    return process.env.VUE_APP_BASE_API + url + '?' + queryParams + '&AUTH-WEB-TOKEN=' + store.state.webToken.sessionId
  } else {
    return process.env.VUE_APP_BASE_API + url + '?AUTH-WEB-TOKEN=' + store.state.webToken.sessionId
  }
}
