import request from '@/utils/HttpUtils'

const baseUri = 'p/typeInfo/'

// 广告位类型
export function listTagTypeApi() {
  return request({
    url: baseUri + 'tagType',
    method: 'post'
  })
}

// 系统类型
export function listOsTypeApi() {
  return request({
    url: baseUri + 'osType',
    method: 'post'
  })
}

// 结算方式
export function listBidTypeApi() {
  return request({
    url: baseUri + 'bidType',
    method: 'post'
  })
}

// 广告位流量类型
export function listFlowTypeApi() {
  return request({
    url: baseUri + 'flowType',
    method: 'post'
  })
}
