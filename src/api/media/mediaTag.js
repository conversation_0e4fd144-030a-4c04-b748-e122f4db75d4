import request from '@/utils/HttpUtils'

const baseUri = 'c/media/tag/'

export function pageMediaTag(data) {
  return request({
    url: baseUri + 'page',
    method: 'post',
    data: data
  })
}

export function addMediaTag(data) {
  return request({
    url: baseUri + 'add',
    method: 'post',
    data: data
  })
}

export function modifyMediaTag(data) {
  return request({
    url: baseUri + 'modify',
    method: 'post',
    data: data
  })
}

export function infoMediaTag(data) {
  return request({
    url: baseUri + 'info',
    method: 'post',
    data: data
  })
}

export function listMediaTag(data) {
  return request({
    url: baseUri + 'list',
    method: 'post',
    data: data
  })
}
