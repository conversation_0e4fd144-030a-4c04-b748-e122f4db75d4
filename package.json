{"name": "ssp-web-oper", "version": "0.1.0", "description": "ssp-web-oper", "author": "", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "tester": "vue-cli-service build --mode test", "jiuwei": "vue-cli-service build --mode jiuwei", "jiaming": "vue-cli-service build --mode jiaming", "qicaifeng": "vue-cli-service build --mode qicaifeng", "jwtest": "vue-cli-service build --mode jwtest", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/components/icons/svg --config=src/components/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "dependencies": {"axios": "^1.7.7", "clipboard": "^2.0.11", "codemirror": "^6.0.1", "cos-js-sdk-v5": "^1.8.4", "driver.js": "^1.3.1", "dropzone": "^6.0.0-beta.2", "echarts": "^5.5.1", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "fuse.js": "^7.0.0", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "jsonlint": "^1.6.3", "jszip": "^3.10.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^2.4.0", "screenfull": "^6.0.2", "showdown": "^2.1.0", "sortablejs": "^1.15.3", "vue": "^2.6.12", "vue-count-to": "^1.0.13", "vue-router": "3.0.2", "vue-splitpane": "^1.0.4", "vuedraggable": "^2.20.0", "vuex": "^3.1.0", "xlsx": "^0.14.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/register": "^7.24.6", "@vue/cli-plugin-babel": "^4.4.6", "@vue/cli-plugin-eslint": "^4.4.6", "@vue/cli-service": "^4.4.6", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "chalk": "^4.1.0", "connect": "^3.6.6", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.0.0", "html-webpack-plugin": "^3.2.0", "husky": "^1.3.1", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "plop": "^4.0.1", "prettier": "^3.3.3", "runjs": "^4.4.2", "sass": "^1.32.0", "sass-loader": "^10.1.0", "script-ext-html-webpack-plugin": "^2.1.5", "script-loader": "^0.7.2", "serve-static": "^1.16.2", "svg-sprite-loader": "^6.0.11", "svgo": "^1.2.0", "vue-template-compiler": "^2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}